package com.example.aicamera.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.aicamera.chess.ChessBoard
import com.example.aicamera.chess.ChessPosition
import com.example.aicamera.ui.theme.getDynamicColorScheme

/**
 * Chess Board Screen - Displays the chess board with FEN from camera
 * Features interactive board, move history, and analysis integration
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChessBoardScreen(
    fen: String?,
    onBackPressed: () -> Unit,
    onAnalyzePosition: (String) -> Unit = {}
) {
    val dynamicColorScheme = getDynamicColorScheme()
    var currentFEN by remember { mutableStateOf(fen ?: "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1") }
    var moveHistory by remember { mutableStateOf(listOf<String>()) }
    var selectedSquare by remember { mutableStateOf<ChessPosition?>(null) }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = dynamicColorScheme.background,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Chess Board",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.SemiBold
                        )
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = { onAnalyzePosition(currentFEN) }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Analytics,
                            contentDescription = "Analyze",
                            tint = dynamicColorScheme.primary
                        )
                    }
                    IconButton(
                        onClick = {
                            // Reset to starting position
                            currentFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
                            moveHistory = emptyList()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.RestartAlt,
                            contentDescription = "Reset"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = dynamicColorScheme.surface,
                    titleContentColor = dynamicColorScheme.onSurface
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Chess Board
            item {
                ChessBoard(
                    fen = currentFEN,
                    onSquareClicked = { position ->
                        selectedSquare = if (selectedSquare == position) null else position
                    },
                    onPieceMoved = { from, to ->
                        // Simple move notation (this would be enhanced with proper chess logic)
                        val move = "${('a' + from.file)}${from.rank + 1}-${('a' + to.file)}${to.rank + 1}"
                        moveHistory = moveHistory + move
                        selectedSquare = null
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Position Information
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.surface
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Position Information",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = dynamicColorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "FEN:",
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                            Text(
                                text = if (fen != null) "From Camera" else "Default",
                                style = MaterialTheme.typography.bodyMedium,
                                color = dynamicColorScheme.primary
                            )
                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = currentFEN,
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            ),
                            color = dynamicColorScheme.onSurfaceVariant,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp)
                        )
                    }
                }
            }

            // Move History
            if (moveHistory.isNotEmpty()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = dynamicColorScheme.surfaceVariant
                        ),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Move History",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.SemiBold
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                            Spacer(modifier = Modifier.height(8.dp))

                            moveHistory.forEachIndexed { index, move ->
                                Text(
                                    text = "${index + 1}. $move",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = dynamicColorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(vertical = 2.dp)
                                )
                            }
                        }
                    }
                }
            }

            // Action Buttons
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Analyze Button
                    Button(
                        onClick = { onAnalyzePosition(currentFEN) },
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = dynamicColorScheme.primary
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Analytics,
                            contentDescription = "Analyze",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Analyze")
                    }

                    // Share FEN Button
                    OutlinedButton(
                        onClick = {
                            // TODO: Share FEN functionality
                        },
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "Share",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Share")
                    }
                }
            }

            // Instructions
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer.copy(alpha = 0.3f)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = "Info",
                                tint = dynamicColorScheme.primary,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "How to use",
                                style = MaterialTheme.typography.titleSmall.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "• Tap pieces to select them\n• Tap another square to move\n• Use the camera to scan real chess boards\n• Analyze positions with the engine",
                            style = MaterialTheme.typography.bodySmall,
                            color = dynamicColorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}
