--------- beginning of main
---------------------------- PROCESS STARTED (25831) for package com.example.aicamera ----------------------------
2025-05-30 14:32:51.996 25831-25831 nativeloader            com.example.aicamera                 D  Configuring clns-4 for other apk /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/lib/arm64:/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.example.aicamera
2025-05-30 14:32:52.025 25831-25831 GraphicsEnvironment     com.example.aicamera                 V  ANGLE Developer option for 'com.example.aicamera' set to: 'default'
2025-05-30 14:32:52.026 25831-25831 GraphicsEnvironment     com.example.aicamera                 V  Neither updatable production driver nor prerelease driver is supported.
2025-05-30 14:32:52.032 25831-25831 NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 14:32:52.034 25831-25831 NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 14:32:52.062 25831-25831 libc                    com.example.aicamera                 W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-30 14:32:52.375 25831-25856 PythonGradioService     com.example.aicamera                 D  🚀 EXTREME: Aggressive Python pre-initialization starting...
2025-05-30 14:32:52.438 25831-25831 xample.aicamer          com.example.aicamera                 E  Invalid ID 0x00000000.
2025-05-30 14:32:52.526 25831-25856 nativeloader            com.example.aicamera                 D  Load /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a/libcrypto_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!classes8.dex): ok
2025-05-30 14:32:52.532 25831-25856 nativeloader            com.example.aicamera                 D  Load /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a/libssl_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!classes8.dex): ok
2025-05-30 14:32:52.537 25831-25856 nativeloader            com.example.aicamera                 D  Load /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a/libsqlite3_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!classes8.dex): ok
2025-05-30 14:32:52.540 25831-25856 nativeloader            com.example.aicamera                 D  Load /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a/libcrypto_python.so using ns clns-4 from class loader (caller=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!classes8.dex): ok
2025-05-30 14:32:52.543 25831-25856 nativeloader            com.example.aicamera                 D  Load /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a/libssl_python.so using ns clns-4 from class loader (caller=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!classes8.dex): ok
2025-05-30 14:32:52.546 25831-25856 nativeloader            com.example.aicamera                 D  Load /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a/libsqlite3_python.so using ns clns-4 from class loader (caller=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!classes8.dex): ok
2025-05-30 14:32:52.555 25831-25856 nativeloader            com.example.aicamera                 D  Load /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a/libpython3.11.so using ns clns-4 from class loader (caller=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!classes8.dex): ok
2025-05-30 14:32:52.560 25831-25856 nativeloader            com.example.aicamera                 D  Load /data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!/lib/arm64-v8a/libchaquopy_java.so using ns clns-4 from class loader (caller=/data/app/~~z7_nGqNG6wZT7GKwnOJSIQ==/com.example.aicamera--VK07RKxBXqxVueT0N7eFg==/base.apk!classes8.dex): ok
2025-05-30 14:32:52.661 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503710): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/zlib.cpython-311.so" dev="dm-10" ino=392611 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:52.686 25831-25831 Choreographer           com.example.aicamera                 I  Skipped 37 frames!  The application may be doing too much work on its main thread.
2025-05-30 14:32:52.691 25831-25831 XDR::VRT                com.example.aicamera                 I  sc is not valid!
2025-05-30 14:32:52.889 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503711): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/java/chaquopy.so" dev="dm-10" ino=392600 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:52.909 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503712): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_ctypes.cpython-311.so" dev="dm-10" ino=392587 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:52.917 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503713): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_struct.cpython-311.so" dev="dm-10" ino=392584 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:52.985 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503714): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_bz2.cpython-311.so" dev="dm-10" ino=392583 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:53.419 25831-25831 xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateMap.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-30 14:32:53.420 25831-25831 xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateMap.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:32:53.421 25831-25831 xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateMap.removeIf$runtime_release(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:32:53.937 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503722): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/fcntl.cpython-311.so" dev="dm-10" ino=392638 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:53.953 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503723): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_posixsubprocess.cpython-311.so" dev="dm-10" ino=392656 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:53.969 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503724): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/select.cpython-311.so" dev="dm-10" ino=392659 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:54.028 25831-25856 PythonGradioService     com.example.aicamera                 D  ✅ Python environment pre-started
2025-05-30 14:32:54.029 25831-25856 PythonGradioService     com.example.aicamera                 D  🔗 Python instance obtained
2025-05-30 14:32:54.053 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503725): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_json.cpython-311.so" dev="dm-10" ino=392662 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:54.137 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503726): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_hashlib.cpython-311.so" dev="dm-10" ino=392670 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:54.168 25831-25831 xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:32:54.169 25831-25831 xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:32:54.169 25831-25831 xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:32:54.520 25831-25831 BufferQueueConsumer     com.example.aicamera                 I  [](id:64e700000000,api:0,p:-1,c:25831) connect: controlledByApp=false
2025-05-30 14:32:54.526 25831-25831 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0] constructor()
2025-05-30 14:32:54.548 25831-25851 hw-ProcessState         com.example.aicamera                 D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-30 14:32:54.578 25831-25851 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:64e700000000,api:1,p:25831,c:25831) connect: api=1 producerControlledByApp=true
2025-05-30 14:32:54.587 25831-25860 ion                     com.example.aicamera                 E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-30 14:32:55.013 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503735): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_multibytecodec.cpython-311.so" dev="dm-10" ino=392698 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:55.445 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503736): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_contextvars.cpython-311.so" dev="dm-10" ino=392699 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:55.461 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503737): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_asyncio.cpython-311.so" dev="dm-10" ino=392706 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:55.521 25831-25831 Compatibil...geReporter com.example.aicamera                 D  Compat change id reported: 171228096; UID 10747; state: ENABLED
2025-05-30 14:32:55.995 25831-25836 xample.aicamer          com.example.aicamera                 I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-30 14:32:56.661 25831-25831 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6503738): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/resource.cpython-311.so" dev="dm-10" ino=392707 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c235,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:32:56.675 25831-25856 python.stderr           com.example.aicamera                 W  /data/data/com.example.aicamera/files/chaquopy/AssetFinder/requirements/websockets/legacy/__init__.py:6: DeprecationWarning: websockets.legacy is deprecated; see https://websockets.readthedocs.io/en/stable/howto/upgrade.html for upgrade instructions
2025-05-30 14:32:56.837 25831-25856 PythonGradioService     com.example.aicamera                 D  📦 chess_fen_client module imported successfully
2025-05-30 14:32:57.007 25831-25851 OpenGLRenderer          com.example.aicamera                 E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-30 14:32:57.008 25831-25851 OpenGLRenderer          com.example.aicamera                 E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-30 14:32:57.025 25831-25845 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=4936ms; Flags=1, FrameTimelineVsyncId=31587773, IntendedVsync=580556726111825, Vsync=580557342778504, InputEventId=0, HandleInputStart=580557345367449, AnimationStart=580557345390911, PerformTraversalsStart=580557345905295, DrawStart=580561397281296, FrameDeadline=580556746111825, FrameInterval=580557344873911, FrameStartTime=16666667, SyncQueued=580561547346603, SyncStart=580561557380449, IssueDrawCommandsStart=580561559612142, SwapBuffers=580561665294526, FrameCompleted=580561672966834, DequeueBufferDuration=0, QueueBufferDuration=2189308, GpuCompleted=580561672966834, SwapBuffersCompleted=580561671678296, DisplayPresentTime=0, 
2025-05-30 14:32:57.370 25831-25831 ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@3922ab2[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-30 14:32:57.370 25831-25831 ImeFocusController      com.example.aicamera                 V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-30 14:32:57.371 25831-25831 ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@3922ab2[ComposeMainActivity], mServedView=null
2025-05-30 14:32:57.372 25831-25831 ImeFocusController      com.example.aicamera                 V  checkFocus: view=null next=DecorView@3922ab2[ComposeMainActivity] force=true package=<none>
2025-05-30 14:32:57.770 25831-25856 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 14:32:57.782 25831-25877 ProfileInstaller        com.example.aicamera                 D  Installing profile for com.example.aicamera
2025-05-30 14:32:58.118 25831-25837 xample.aicamer          com.example.aicamera                 I  Background concurrent copying GC freed 104KB AllocSpace bytes, 0(0B) LOS objects, 26% free, 5651KB/7699KB, paused 284us,101us total 109.425ms
2025-05-30 14:33:00.207 25831-25831 CameraScreen            com.example.aicamera                 D  🎥 Starting camera initialization...
2025-05-30 14:33:00.224 25831-25842 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=1105ms; Flags=0, FrameTimelineVsyncId=31587909, IntendedVsync=580563759085239, Vsync=580564092418579, InputEventId=0, HandleInputStart=580564097183757, AnimationStart=580564097191757, PerformTraversalsStart=580564677274296, DrawStart=580564799590527, FrameDeadline=580563795751906, FrameInterval=580564097152142, FrameStartTime=16666667, SyncQueued=580564844382911, SyncStart=580564844519527, IssueDrawCommandsStart=580564845526604, SwapBuffers=580564858382911, FrameCompleted=580564865173065, DequeueBufferDuration=33000, QueueBufferDuration=1253462, GpuCompleted=580564865173065, SwapBuffersCompleted=580564860920680, DisplayPresentTime=0, 
2025-05-30 14:33:00.247 25831-25888 CameraManagerGlobal     com.example.aicamera                 I  Connecting to camera service
2025-05-30 14:33:00.255 25831-25831 Choreographer           com.example.aicamera                 I  Skipped 48 frames!  The application may be doing too much work on its main thread.
2025-05-30 14:33:00.314 25831-25888 CameraRepository        com.example.aicamera                 D  Added camera: 0
2025-05-30 14:33:00.371 25831-25888 Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 14:33:00.389 25831-25888 CameraRepository        com.example.aicamera                 D  Added camera: 1
2025-05-30 14:33:00.392 25831-25888 Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 14:33:00.394 25831-25888 CameraValidator         com.example.aicamera                 D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-30 14:33:00.407 25831-25842 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=937ms; Flags=0, FrameTimelineVsyncId=31587951, IntendedVsync=580564108242615, Vsync=580564908242631, InputEventId=0, HandleInputStart=580564914426604, AnimationStart=580564914432757, PerformTraversalsStart=580565025196142, DrawStart=580565025376757, FrameDeadline=580564144909282, FrameInterval=580564913953988, FrameStartTime=16666667, SyncQueued=580565036950527, SyncStart=580565037224373, IssueDrawCommandsStart=580565037441988, SwapBuffers=580565039527450, FrameCompleted=580565046350296, DequeueBufferDuration=33154, QueueBufferDuration=1575693, GpuCompleted=580565046350296, SwapBuffersCompleted=580565042389604, DisplayPresentTime=0, 
2025-05-30 14:33:00.481 25831-25831 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:33:00.483 25831-25831 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:33:00.532 25831-25831 DynamicRangeResolver    com.example.aicamera                 D  Resolved dynamic range for use case androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e1 to no compatible HDR dynamic ranges.
                                                                                                    DynamicRange@23de113{encoding=UNSPECIFIED, bitDepth=0}
                                                                                                    ->
                                                                                                    DynamicRange@d38e402{encoding=SDR, bitDepth=8}
2025-05-30 14:33:00.552 25831-25831 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:33:00.554 25831-25831 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=1, used_surfaces=0](androidx.camera.core.processing.SurfaceEdge$SettableSurface@ba9907c}
2025-05-30 14:33:00.561 25831-25831 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=2, used_surfaces=0](androidx.camera.core.SurfaceRequest$2@63d3868}
2025-05-30 14:33:00.564 25831-25831 DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@63d3868}
2025-05-30 14:33:00.564 25831-25831 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.SurfaceRequest$2@63d3868
2025-05-30 14:33:00.567 25831-25831 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:33:00.581 25831-25831 ImageCapture            com.example.aicamera                 D  createPipeline(cameraId: 0, streamSpec: StreamSpec{resolution=3264x2448, dynamicRange=DynamicRange@d38e402{encoding=SDR, bitDepth=8}, expectedFrameRateRange=[0, 0], implementationOptions=androidx.camera.camera2.impl.Camera2ImplConfig@38647bd})
2025-05-30 14:33:00.597 25831-25831 BufferQueueConsumer     com.example.aicamera                 I  [](id:64e700000001,api:0,p:-1,c:25831) connect: controlledByApp=true
2025-05-30 14:33:00.599 25831-25831 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=3, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@33651b2}
2025-05-30 14:33:00.613 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265 ACTIVE
2025-05-30 14:33:00.615 25831-25831 CameraScreen            com.example.aicamera                 D  ✅ Camera ready and streaming
2025-05-30 14:33:00.617 25831-25889 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:33:00.626 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112 ACTIVE
2025-05-30 14:33:00.627 25831-25889 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:33:00.634 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265 ACTIVE
2025-05-30 14:33:00.635 25831-25889 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:33:00.640 25831-25889 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:33:00.647 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use cases [androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112, androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265] now ATTACHED
2025-05-30 14:33:00.648 25831-25831 PreviewView             com.example.aicamera                 D  Surface requested by Preview.
2025-05-30 14:33:00.651 25831-25889 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265, androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112] for camera: 0
2025-05-30 14:33:00.652 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  mMeteringRepeating is ATTACHED, SessionConfig Surfaces: 2, CaptureConfig Surfaces: 1
2025-05-30 14:33:00.659 25831-25889 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265, androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112] for camera: 0
2025-05-30 14:33:00.660 25831-25831 SurfaceFactory          com.example.aicamera                 I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@cf2650a
2025-05-30 14:33:00.676 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Resetting Capture Session
2025-05-30 14:33:00.678 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Releasing session in state INITIALIZED
2025-05-30 14:33:00.680 25831-25831 PreviewView             com.example.aicamera                 D  Preview transformation info updated. TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false}
2025-05-30 14:33:00.680 25831-25831 PreviewTransform        com.example.aicamera                 D  Transformation info set: TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false} 1024x768 false
2025-05-30 14:33:00.681 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Attempting to force open the camera.
2025-05-30 14:33:00.681 25831-25831 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:33:00.682 25831-25889 CameraStateRegistry     com.example.aicamera                 D  tryOpenCamera(Camera@39fde97[id=0]) [Available Cameras: 1, Already Open: false (Previous state: null)] --> SUCCESS
2025-05-30 14:33:00.683 25831-25831 SurfaceViewImpl         com.example.aicamera                 D  Wait for new Surface creation.
2025-05-30 14:33:00.685 25831-25889 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@83ab0ee[id=1]                         UNKNOWN               
                                                                                                    Camera@39fde97[id=0]                         OPENING               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 14:33:00.687 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Opening camera.
2025-05-30 14:33:00.689 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Transitioning camera internal state: INITIALIZED --> OPENING
2025-05-30 14:33:00.690 25831-25889 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPENING, error=null} from OPENING and null
2025-05-30 14:33:00.691 25831-25889 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPENING, error=null}
2025-05-30 14:33:00.694 25831-25889 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265, androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112] for camera: 0
2025-05-30 14:33:00.700 25831-25889 libc                    com.example.aicamera                 W  Access denied finding property "persist.vendor.camera.privapp.list"
2025-05-30 14:33:00.697 25831-25831 CameraX-core_ca         com.example.aicamera                 W  type=1400 audit(0.0:6503740): avc: denied { read } for name="u:object_r:vendor_camera_mtk_prop:s0" dev="tmpfs" ino=13626 scontext=u:r:untrusted_app:s0:c235,c258,c512,c768 tcontext=u:object_r:vendor_camera_mtk_prop:s0 tclass=file permissive=0 app=com.example.aicamera
2025-05-30 14:33:00.706 25831-25831 BufferQueueConsumer     com.example.aicamera                 I  [](id:64e700000002,api:0,p:-1,c:25831) connect: controlledByApp=false
2025-05-30 14:33:00.707 25831-25831 BLASTBufferQueue        com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1] constructor()
--------- beginning of system
2025-05-30 14:33:00.710 25831-25831 SurfaceViewImpl         com.example.aicamera                 D  Surface created.
2025-05-30 14:33:00.714 25831-25831 SurfaceViewImpl         com.example.aicamera                 D  Surface changed. Size: 1024x768
2025-05-30 14:33:00.714 25831-25831 SurfaceViewImpl         com.example.aicamera                 D  Surface set on Preview.
2025-05-30 14:33:00.761 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112 ACTIVE
2025-05-30 14:33:00.763 25831-25889 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265, androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112] for camera: 0
2025-05-30 14:33:00.769 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265 ACTIVE
2025-05-30 14:33:00.772 25831-25889 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265, androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112] for camera: 0
2025-05-30 14:33:00.780 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} CameraDevice.onOpened()
2025-05-30 14:33:00.781 25831-25889 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Transitioning camera internal state: OPENING --> OPENED
2025-05-30 14:33:00.783 25831-25889 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@83ab0ee[id=1]                         UNKNOWN               
                                                                                                    Camera@39fde97[id=0]                         OPEN                  
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 14:33:00.783 25831-25889 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPEN, error=null} from OPEN and null
2025-05-30 14:33:00.783 25831-25889 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPEN, error=null}
2025-05-30 14:33:00.786 25831-25889 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265, androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112] for camera: 0
2025-05-30 14:33:00.809 25831-25889 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265, androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112] for camera: 0
2025-05-30 14:33:00.817 25831-25889 SyncCaptureSessionBase  com.example.aicamera                 D  [androidx.camera.camera2.internal.SynchronizedCaptureSessionBaseImpl@f1fa747] getSurface...done
2025-05-30 14:33:00.817 25831-25889 CaptureSession          com.example.aicamera                 D  Opening capture session.
2025-05-30 14:33:00.831 25831-25889 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=2 androidx.camera.core.SurfaceRequest$2@63d3868
2025-05-30 14:33:00.831 25831-25889 DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=3, used_surfaces=2](androidx.camera.core.impl.ImmediateSurface@33651b2}
2025-05-30 14:33:00.831 25831-25889 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.impl.ImmediateSurface@33651b2
2025-05-30 14:33:00.938 25831-25889 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) connect: api=4 producerControlledByApp=true
2025-05-30 14:33:00.939 25831-25889 BufferQueueProducer     com.example.aicamera                 I  [ImageReader-3264x2448f100m4-25831-0](id:64e700000001,api:4,p:985,c:25831) connect: api=4 producerControlledByApp=false
2025-05-30 14:33:00.947 25831-25889 CaptureSession          com.example.aicamera                 D  Attempting to send capture request onConfigured
2025-05-30 14:33:00.947 25831-25889 CaptureSession          com.example.aicamera                 D  Issuing request for session.
2025-05-30 14:33:00.949 25831-25889 Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 14:33:00.958 25831-25889 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onConfigured() mState=OPENED
2025-05-30 14:33:00.959 25831-25889 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onReady() OPENED
2025-05-30 14:33:01.310 25831-25889 StreamStateObserver     com.example.aicamera                 D  Update Preview stream state to STREAMING
2025-05-30 14:33:01.450 25831-25856 PythonGradioService     com.example.aicamera                 D  🔗 Connection test result: {'success': True, 'message': 'Successfully connected to HF Space', 'space': 'yamero999/chess-fen-generation-api'}
2025-05-30 14:33:01.451 25831-25856 PythonGradioService     com.example.aicamera                 D  ✅ Python gradio service initialized successfully
2025-05-30 14:33:01.459 25831-25856 PythonGradioService     com.example.aicamera                 D  🔥 Pre-warming API connection...
2025-05-30 14:33:02.179 25831-25856 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 14:33:06.163 25831-25856 PythonGradioService     com.example.aicamera                 D  🏆 EXTREME pre-initialization complete in 13788ms
2025-05-30 14:33:07.160 25831-25831 ImageCapture            com.example.aicamera                 D  takePictureInternal
2025-05-30 14:33:07.168 25831-25831 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:33:07.176 25831-25831 TakePictureManager      com.example.aicamera                 D  Issue the next TakePictureRequest.
2025-05-30 14:33:07.211 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Issue capture request
2025-05-30 14:33:07.213 25831-25888 CaptureSession          com.example.aicamera                 D  Issuing capture request.
2025-05-30 14:33:07.223 25831-25888 Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 14:33:07.325 25831-25842 GraphicBuffer           com.example.aicamera                 D  initWithSize w=24071582, h=1
2025-05-30 14:33:07.326 25831-25842 GraphicBuffer           com.example.aicamera                 D  flatten w=24071582, h=1
2025-05-30 14:33:07.747 25831-25831 TakePictureManager      com.example.aicamera                 D  Issue the next TakePictureRequest.
2025-05-30 14:33:07.747 25831-25831 TakePictureManager      com.example.aicamera                 D  No new request.
2025-05-30 14:33:07.931 25831-25837 xample.aicamer          com.example.aicamera                 I  Background concurrent copying GC freed 993KB AllocSpace bytes, 5(180KB) LOS objects, 25% free, 10197KB/13MB, paused 930us,211us total 117.720ms
2025-05-30 14:33:08.058 25831-25831 CameraManager           com.example.aicamera                 D  Photo capture succeeded: /storage/emulated/0/Android/data/com.example.aicamera/files/2025-05-30-14-33-07-077.jpg
2025-05-30 14:33:08.075 25831-26023 CameraManager           com.example.aicamera                 D  📏 Captured image size: 2448x3264
2025-05-30 14:33:08.076 25831-26023 ImageProcessor          com.example.aicamera                 D  🔄 Resizing image to 416x416: 2025-05-30-14-33-07-077.jpg
2025-05-30 14:33:08.080 25831-26023 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels +
2025-05-30 14:33:08.363 25831-26023 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels -
2025-05-30 14:33:08.364 25831-26023 ImageProcessor          com.example.aicamera                 D  📏 Original size: 2448x3264
2025-05-30 14:33:08.411 25831-26023 ImageProcessor          com.example.aicamera                 D  ✅ Image resized and saved: 416x416
2025-05-30 14:33:08.414 25831-26023 CameraManager           com.example.aicamera                 D  ✅ Image resized to 416x416 (66 KB)
2025-05-30 14:33:08.416 25831-26023 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels +
2025-05-30 14:33:08.423 25831-26023 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels -
2025-05-30 14:33:08.423 25831-26023 GalleryManager          com.example.aicamera                 D  📏 Gallery save - Image size: 416x416
2025-05-30 14:33:08.423 25831-26023 GalleryManager          com.example.aicamera                 D  ✅ Image already 416x416, perfect for gallery
2025-05-30 14:33:08.458 25831-26024 NetworkManager          com.example.aicamera                 D  🐍 Starting Python gradio_client upload...
2025-05-30 14:33:08.459 25831-26024 NetworkManager          com.example.aicamera                 D  📁 File: 2025-05-30-14-33-07-077.jpg, Size: 68292 bytes
2025-05-30 14:33:08.459 25831-26024 PythonGradioService     com.example.aicamera                 D  ✅ Python service already initialized
2025-05-30 14:33:08.462 25831-26024 PythonGradioService     com.example.aicamera                 D  🚀 Starting FEN generation using Python gradio_client...
2025-05-30 14:33:08.462 25831-26024 PythonGradioService     com.example.aicamera                 D  📁 File: 2025-05-30-14-33-07-077.jpg, Size: 68292 bytes
2025-05-30 14:33:08.500 25831-26024 PythonGradioService     com.example.aicamera                 D  📦 Converted image to base64: 91056 characters
2025-05-30 14:33:08.500 25831-26024 PythonGradioService     com.example.aicamera                 D  🐍 Calling Python gradio_client...
2025-05-30 14:33:08.752 25831-26023 GalleryManager          com.example.aicamera                 D  ✅ Image saved to gallery: content://media/external/images/media/1000234805
2025-05-30 14:33:08.752 25831-26023 CameraManager           com.example.aicamera                 D  ✅ Image saved to gallery: content://media/external/images/media/1000234805
2025-05-30 14:33:08.775 25831-25831 CameraScreen            com.example.aicamera                 D  ✅ Image saved to gallery: content://media/external/images/media/1000234805
2025-05-30 14:33:09.727 25831-26024 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 14:33:27.672 25831-26024 PythonGradioService     com.example.aicamera                 D  📄 Python result: {'success': True, 'fen': '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8', 'error': None, 'raw_result': {'success': True, 'fen': '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8', 'analysis': {'pieces_detected': 18, 'processing_time_ms': 10131.43, 'processing_time_seconds': 10.131, 'v6_inference_time_ms': 7562.483310699463, 'board_detection_time_ms': 7606.44, 'piece_detection_time_ms': 2523.6, 'mapping_time_ms': 0.2, 'save_time_ms': 1.12, 'board_detection': {'corners_found': [[72.0, 104.0], [309.0, 104.0], [309.0, 279.0], [70.0, 277.0]], 'perspective_corrected': True}, 'piece_analysis': {'total_pieces': 18, 'piece_counts': {'black_rook': 2, 'black_pawn': 5, 'white_rook': 2, 'white_pawn': 5, 'black_bishop': 1, 'white_bishop': 1, 'black_king': 1, 'white_king': 1}, 'confidence_stats': {'avg': 0.948, 'min': 0.703, 'max': 0.989}}, 'grid_mapping': {'occupied_squares': 18, 'empty_squares': 46}}, 'provider': 'huggingface_spaces', 'model_info': {'v6_segmentation': 'yamero999/chess-board-segmentation-v6', 'piece_detection': 'yamero999/chess-piece-detection-yolo11n'}}}
2025-05-30 14:33:27.732 25831-26024 PythonGradioService     com.example.aicamera                 D  📊 Parsed result - Success: true, FEN: 1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8, Error: null
2025-05-30 14:33:27.732 25831-26024 PythonGradioService     com.example.aicamera                 D  🎯 FEN Result: 1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8
2025-05-30 14:33:27.733 25831-26024 PythonGradioService     com.example.aicamera                 D  ✅ Successfully processed FEN response
2025-05-30 14:33:27.739 25831-25831 CameraScreen            com.example.aicamera                 D  📝 Original FEN from AI: '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8'
2025-05-30 14:33:27.741 25831-25831 CameraScreen            com.example.aicamera                 D  🔄 Board flipped 180° (vertical + horizontal) - Black king was in wrong position
2025-05-30 14:33:27.741 25831-25831 CameraScreen            com.example.aicamera                 D     Original: '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8'
2025-05-30 14:33:27.741 25831-25831 CameraScreen            com.example.aicamera                 D     Flipped:  '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1'
2025-05-30 14:33:27.741 25831-25831 CameraScreen            com.example.aicamera                 D  🔧 Completed FEN: '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1' (was 1 parts, now 6)
2025-05-30 14:33:27.741 25831-25831 CameraScreen            com.example.aicamera                 D  🚀 Auto-launching external app with processed FEN: '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1'
2025-05-30 14:33:27.758 25831-25831 ExternalAppManager      com.example.aicamera                 D  Direct launch failed, trying generic Chessis launch
2025-05-30 14:33:27.765 25831-25831 ExternalAppManager      com.example.aicamera                 E  ❌ Failed to launch com.chessis.analysis (Ask Gemini)
                                                                                                    android.content.ActivityNotFoundException: No Activity found to handle Intent { act=android.intent.action.SEND typ=text/plain flg=0x10000001 pkg=com.chessis.analysis clip={text/plain {T(56)}} (has extras) }
                                                                                                    	at android.app.Instrumentation.checkStartActivityResult(Instrumentation.java:2137)
                                                                                                    	at android.app.Instrumentation.execStartActivity(Instrumentation.java:1797)
                                                                                                    	at android.app.Activity.startActivityForResult(Activity.java:5643)
                                                                                                    	at androidx.activity.ComponentActivity.startActivityForResult(ComponentActivity.java:780)
                                                                                                    	at android.app.Activity.startActivityForResult(Activity.java:5591)
                                                                                                    	at androidx.activity.ComponentActivity.startActivityForResult(ComponentActivity.java:761)
                                                                                                    	at android.app.Activity.startActivity(Activity.java:6011)
                                                                                                    	at android.app.Activity.startActivity(Activity.java:5964)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchAppWithFEN(ExternalAppManager.kt:139)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchChessis(ExternalAppManager.kt:78)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchChessApp(ExternalAppManager.kt:25)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt.CameraScreen$completeAndLaunchFEN(CameraScreen.kt:177)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt.access$CameraScreen$completeAndLaunchFEN(CameraScreen.kt:1)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt$CameraScreen$7$3$4$2$1$1.invokeSuspend(CameraScreen.kt:499)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher.performTrampolineDispatch(AndroidUiDispatcher.android.kt:81)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher.access$performTrampolineDispatch(AndroidUiDispatcher.android.kt:41)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher$dispatchCallback$1.run(AndroidUiDispatcher.android.kt:57)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:938)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:233)
                                                                                                    	at android.os.Looper.loop(Looper.java:334)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8399)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:582)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1068)
2025-05-30 14:33:27.833 25831-25831 ExternalAppManager      com.example.aicamera                 D  ✅ Launched app chooser with clean FEN: 8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1
2025-05-30 14:33:27.834 25831-25831 CameraScreen            com.example.aicamera                 D  ✅ Successfully launched external app: chessis
2025-05-30 14:33:31.870 25831-25831 SurfaceViewImpl         com.example.aicamera                 D  Surface destroyed.
2025-05-30 14:33:31.872 25831-25831 SurfaceViewImpl         com.example.aicamera                 D  Surface closed androidx.camera.core.SurfaceRequest@88e5f8b
2025-05-30 14:33:31.884 25831-25831 DeferrableSurface       com.example.aicamera                 D  surface closed,  useCount=2 closed=true androidx.camera.core.SurfaceRequest$2@63d3868
2025-05-30 14:33:31.885 25831-25831 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) disconnect: api -1
2025-05-30 14:33:31.885 25831-25831 BLASTBufferQueue        com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1] destructor()
2025-05-30 14:33:31.886 25831-25831 BufferQueueConsumer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) disconnect
2025-05-30 14:33:31.901 25831-25841 BLASTBufferQueue        com.example.aicamera                 I  releaseBufferCallbackThunk bufferId:110943300222984 framenumber:659 blastBufferQueue is dead
2025-05-30 14:33:31.906 25831-25851 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:64e700000000,api:1,p:25831,c:25831) disconnect: api 1
2025-05-30 14:33:31.927 25831-25841 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) queueBuffer: BufferQueue has been abandoned
2025-05-30 14:33:31.943 25831-26102 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) dequeueBuffer: BufferQueue has been abandoned
2025-05-30 14:33:31.960 25831-25831 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0] destructor()
2025-05-30 14:33:31.960 25831-25831 BufferQueueConsumer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:64e700000000,api:0,p:-1,c:25831) disconnect
2025-05-30 14:33:31.985 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use cases [androidx.camera.core.Preview-beac6b3d-d121-48b2-bd43-05cc798c70e154989112, androidx.camera.core.ImageCapture-03221aa0-0da4-494d-a357-e37154dc30d397621265] now DETACHED for camera
2025-05-30 14:33:31.987 25831-25888 UseCaseAttachState      com.example.aicamera                 D  All use case: [] for camera: 0
2025-05-30 14:33:31.988 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Resetting Capture Session
2025-05-30 14:33:31.988 25831-26006 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) queueBuffer: BufferQueue has been abandoned
2025-05-30 14:33:31.997 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Releasing session in state OPENED
2025-05-30 14:33:31.998 25831-25888 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:33:32.005 25831-25888 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:33:32.010 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Closing camera.
2025-05-30 14:33:32.011 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Transitioning camera internal state: OPENED --> CLOSING
2025-05-30 14:33:32.015 25831-25831 DeferrableSurface       com.example.aicamera                 D  surface closed,  useCount=0 closed=true androidx.camera.core.processing.SurfaceEdge$SettableSurface@ba9907c
2025-05-30 14:33:32.016 25831-25831 DeferrableSurface       com.example.aicamera                 D  Surface terminated[total_surfaces=2, used_surfaces=2](androidx.camera.core.processing.SurfaceEdge$SettableSurface@ba9907c}
2025-05-30 14:33:32.016 25831-25831 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=1 closed=true androidx.camera.core.SurfaceRequest$2@63d3868
2025-05-30 14:33:32.018 25831-25888 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@83ab0ee[id=1]                         UNKNOWN               
                                                                                                    Camera@39fde97[id=0]                         CLOSING               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 14:33:32.020 25831-25888 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=CLOSING, error=null} from CLOSING and null
2025-05-30 14:33:32.020 25831-25888 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=CLOSING, error=null}
2025-05-30 14:33:32.021 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Resetting Capture Session
2025-05-30 14:33:32.023 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Releasing session in state CLOSING
2025-05-30 14:33:32.024 25831-25888 CaptureSession          com.example.aicamera                 D  onSessionFinished()
2025-05-30 14:33:32.035 25831-25831 StreamStateObserver     com.example.aicamera                 D  Update Preview stream state to IDLE
2025-05-30 14:33:32.049 25831-25845 BLASTBufferQueue        com.example.aicamera                 I  releaseBufferCallbackThunk bufferId:110943300222980 framenumber:1297 blastBufferQueue is dead
2025-05-30 14:33:32.062 25831-26102 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) queueBuffer: BufferQueue has been abandoned
2025-05-30 14:33:32.154 25831-26102 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) cancelBuffer: BufferQueue has been abandoned
2025-05-30 14:33:32.156 25831-26102 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) cancelBuffer: BufferQueue has been abandoned
2025-05-30 14:33:32.442 25831-25888 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:64e700000002,api:4,p:985,c:25831) disconnect: api 4
2025-05-30 14:33:32.444 25831-25888 BufferQueueProducer     com.example.aicamera                 I  [ImageReader-3264x2448f100m4-25831-0](id:64e700000001,api:4,p:985,c:25831) disconnect: api 4
2025-05-30 14:33:32.454 25831-25888 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:33:32.460 25831-25888 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=0 closed=true androidx.camera.core.SurfaceRequest$2@63d3868
2025-05-30 14:33:32.460 25831-25888 DeferrableSurface       com.example.aicamera                 D  Surface no longer in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@63d3868}
2025-05-30 14:33:32.460 25831-25888 DeferrableSurface       com.example.aicamera                 D  Surface terminated[total_surfaces=1, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@63d3868}
2025-05-30 14:33:32.462 25831-25888 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=0 closed=false androidx.camera.core.impl.ImmediateSurface@33651b2
2025-05-30 14:33:32.462 25831-25888 DeferrableSurface       com.example.aicamera                 D  Surface no longer in use[total_surfaces=1, used_surfaces=0](androidx.camera.core.impl.ImmediateSurface@33651b2}
2025-05-30 14:33:32.463 25831-25831 SurfaceViewImpl         com.example.aicamera                 D  Safe to release surface.
2025-05-30 14:33:32.463 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} CameraDevice.onClosed()
2025-05-30 14:33:32.465 25831-25888 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Transitioning camera internal state: CLOSING --> INITIALIZED
2025-05-30 14:33:32.467 25831-25888 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@83ab0ee[id=1]                         UNKNOWN               
                                                                                                    Camera@39fde97[id=0]                         CLOSED                
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 0 (Max allowed: 1)
2025-05-30 14:33:32.469 25831-25888 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=CLOSED, error=null} from CLOSED and null
2025-05-30 14:33:32.469 25831-25888 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=CLOSED, error=null}
