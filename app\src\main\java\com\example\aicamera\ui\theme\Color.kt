package com.example.aicamera.ui.theme

import androidx.compose.ui.graphics.Color

// Chess.com inspired color palette
val ChessGreen = Color(0xFF2E7D32)
val ChessGreenLight = Color(0xFF4CAF50)
val ChessGreenDark = Color(0xFF1B5E20)

val ChessBrown = Color(0xFF8D6E63)
val ChessBrownLight = Color(0xFFBCAAA4)
val ChessBrownDark = Color(0xFF5D4037)

val ChessGold = Color(0xFFFFB300)
val ChessGoldLight = Color(0xFFFFD54F)
val ChessGoldDark = Color(0xFFFF8F00)

// Material Design 3 Color Scheme
val md_theme_light_primary = ChessGreen
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color(0xFFA8DAB5)
val md_theme_light_onPrimaryContainer = Color(0xFF002106)
val md_theme_light_secondary = ChessBrown
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Color(0xFFE7C6B7)
val md_theme_light_onSecondaryContainer = Color(0xFF2C1B0F)
val md_theme_light_tertiary = ChessGold
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = Color(0xFFFFE082)
val md_theme_light_onTertiaryContainer = Color(0xFF2D1B00)
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFFCFDF6)
val md_theme_light_onBackground = Color(0xFF1A1C18)
val md_theme_light_surface = Color(0xFFFCFDF6)
val md_theme_light_onSurface = Color(0xFF1A1C18)
val md_theme_light_surfaceVariant = Color(0xFFDDE5DA)
val md_theme_light_onSurfaceVariant = Color(0xFF414941)
val md_theme_light_outline = Color(0xFF717971)
val md_theme_light_inverseOnSurface = Color(0xFFF1F1EB)
val md_theme_light_inverseSurface = Color(0xFF2F312D)
val md_theme_light_inversePrimary = Color(0xFF8DBE9A)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = ChessGreen
val md_theme_light_outlineVariant = Color(0xFFC1C9BE)
val md_theme_light_scrim = Color(0xFF000000)

val md_theme_dark_primary = Color(0xFF8DBE9A)
val md_theme_dark_onPrimary = Color(0xFF003910)
val md_theme_dark_primaryContainer = Color(0xFF00531A)
val md_theme_dark_onPrimaryContainer = Color(0xFFA8DAB5)
val md_theme_dark_secondary = Color(0xFFCAAA9B)
val md_theme_dark_onSecondary = Color(0xFF432B1E)
val md_theme_dark_secondaryContainer = Color(0xFF5C4033)
val md_theme_dark_onSecondaryContainer = Color(0xFFE7C6B7)
val md_theme_dark_tertiary = Color(0xFFE1C54D)
val md_theme_dark_onTertiary = Color(0xFF3E2E00)
val md_theme_dark_tertiaryContainer = Color(0xFF584400)
val md_theme_dark_onTertiaryContainer = Color(0xFFFFE082)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF11130F)
val md_theme_dark_onBackground = Color(0xFFE2E3DD)
val md_theme_dark_surface = Color(0xFF11130F)
val md_theme_dark_onSurface = Color(0xFFE2E3DD)
val md_theme_dark_surfaceVariant = Color(0xFF414941)
val md_theme_dark_onSurfaceVariant = Color(0xFFC1C9BE)
val md_theme_dark_outline = Color(0xFF8B9389)
val md_theme_dark_inverseOnSurface = Color(0xFF11130F)
val md_theme_dark_inverseSurface = Color(0xFFE2E3DD)
val md_theme_dark_inversePrimary = ChessGreen
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFF8DBE9A)
val md_theme_dark_outlineVariant = Color(0xFF414941)
val md_theme_dark_scrim = Color(0xFF000000)

val seed = ChessGreen
