// Generated by view binder compiler. Do not edit!
package com.example.aicamera.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aicamera.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView aiResponseText;

  @NonNull
  public final Button captureButton;

  @NonNull
  public final LinearLayout controlsContainer;

  @NonNull
  public final PreviewView previewView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Button sendToAiButton;

  @NonNull
  public final TextView statusText;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView, @NonNull TextView aiResponseText,
      @NonNull Button captureButton, @NonNull LinearLayout controlsContainer,
      @NonNull PreviewView previewView, @NonNull ProgressBar progressBar,
      @NonNull Button sendToAiButton, @NonNull TextView statusText) {
    this.rootView = rootView;
    this.aiResponseText = aiResponseText;
    this.captureButton = captureButton;
    this.controlsContainer = controlsContainer;
    this.previewView = previewView;
    this.progressBar = progressBar;
    this.sendToAiButton = sendToAiButton;
    this.statusText = statusText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.aiResponseText;
      TextView aiResponseText = ViewBindings.findChildViewById(rootView, id);
      if (aiResponseText == null) {
        break missingId;
      }

      id = R.id.captureButton;
      Button captureButton = ViewBindings.findChildViewById(rootView, id);
      if (captureButton == null) {
        break missingId;
      }

      id = R.id.controlsContainer;
      LinearLayout controlsContainer = ViewBindings.findChildViewById(rootView, id);
      if (controlsContainer == null) {
        break missingId;
      }

      id = R.id.previewView;
      PreviewView previewView = ViewBindings.findChildViewById(rootView, id);
      if (previewView == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.sendToAiButton;
      Button sendToAiButton = ViewBindings.findChildViewById(rootView, id);
      if (sendToAiButton == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, aiResponseText, captureButton,
          controlsContainer, previewView, progressBar, sendToAiButton, statusText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
