package com.example.aicamera.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.aicamera.preferences.ExternalChessApp
import com.example.aicamera.preferences.SupportedChessApps
import com.example.aicamera.integration.ExternalAppManager
import com.example.aicamera.ui.theme.getDynamicColorScheme

/**
 * App Selection Screen - Choose preferred external chess app
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppSelectionScreen(
    onAppSelected: (String) -> Unit,
    onSkip: () -> Unit = {}
) {
    val context = LocalContext.current
    val dynamicColorScheme = getDynamicColorScheme()
    val appManager = remember { ExternalAppManager(context) }

    var selectedAppId by remember { mutableStateOf(SupportedChessApps.CHESSIS.id) }
    val availableApps = remember { appManager.getInstalledApps() }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = dynamicColorScheme.background,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Choose Chess App",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.SemiBold
                        )
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = dynamicColorScheme.surface,
                    titleContentColor = dynamicColorScheme.onSurface
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.CameraAlt,
                            contentDescription = "AI Camera",
                            modifier = Modifier.size(48.dp),
                            tint = dynamicColorScheme.primary
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            text = "AI Chess Camera",
                            style = MaterialTheme.typography.headlineSmall.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = dynamicColorScheme.onPrimaryContainer,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Scan chess boards with AI and analyze positions in your favorite chess app",
                            style = MaterialTheme.typography.bodyMedium,
                            color = dynamicColorScheme.onPrimaryContainer,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // Instructions
            item {
                Text(
                    text = "Select your preferred chess app for analysis:",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = dynamicColorScheme.onSurface
                )
            }

            // App Selection List
            items(availableApps) { app ->
                AppSelectionCard(
                    app = app,
                    isSelected = selectedAppId == app.id,
                    isInstalled = appManager.isAppInstalled(app.packageName),
                    onSelected = { selectedAppId = app.id },
                    dynamicColorScheme = dynamicColorScheme
                )
            }

            // Action Buttons
            item {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Continue Button
                    Button(
                        onClick = { onAppSelected(selectedAppId) },
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = dynamicColorScheme.primary
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "Continue",
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Continue with ${SupportedChessApps.getAppById(selectedAppId)?.name}",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontWeight = FontWeight.Medium
                            )
                        )
                    }

                    // Skip Button
                    TextButton(
                        onClick = onSkip,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = "Skip for now",
                            color = dynamicColorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            // Bottom spacing
            item {
                Spacer(modifier = Modifier.height(32.dp))
            }
        }
    }
}

@Composable
private fun AppSelectionCard(
    app: ExternalChessApp,
    isSelected: Boolean,
    isInstalled: Boolean,
    onSelected: () -> Unit,
    dynamicColorScheme: androidx.compose.material3.ColorScheme
) {
    val containerColor = when {
        isSelected -> dynamicColorScheme.primaryContainer
        else -> dynamicColorScheme.surface
    }

    val contentColor = when {
        isSelected -> dynamicColorScheme.onPrimaryContainer
        else -> dynamicColorScheme.onSurface
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSelected() },
        colors = CardDefaults.cardColors(
            containerColor = containerColor
        ),
        shape = RoundedCornerShape(12.dp),
        border = if (isSelected) {
            androidx.compose.foundation.BorderStroke(2.dp, dynamicColorScheme.primary)
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // App Icon
            val icon = when (app.icon) {
                "analytics" -> Icons.Default.Analytics
                "sports_esports" -> Icons.Default.SportsEsports
                "psychology" -> Icons.Default.Psychology
                "memory" -> Icons.Default.Memory
                "apps" -> Icons.Default.Apps
                else -> Icons.Default.Apps
            }

            Icon(
                imageVector = icon,
                contentDescription = app.name,
                modifier = Modifier.size(32.dp),
                tint = if (isSelected) dynamicColorScheme.primary else dynamicColorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.width(16.dp))

            // App Info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = app.name,
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.SemiBold
                        ),
                        color = contentColor
                    )

                    if (app.isRecommended) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Surface(
                            color = dynamicColorScheme.primary,
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = "RECOMMENDED",
                                style = MaterialTheme.typography.labelSmall,
                                color = dynamicColorScheme.onPrimary,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }

                    if (!isInstalled && app.packageName.isNotEmpty()) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Surface(
                            color = dynamicColorScheme.errorContainer,
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = "NOT INSTALLED",
                                style = MaterialTheme.typography.labelSmall,
                                color = dynamicColorScheme.onErrorContainer,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = app.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isSelected) dynamicColorScheme.onPrimaryContainer else dynamicColorScheme.onSurfaceVariant
                )
            }

            // Selection Indicator
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Selected",
                    modifier = Modifier.size(24.dp),
                    tint = dynamicColorScheme.primary
                )
            }
        }
    }
}
