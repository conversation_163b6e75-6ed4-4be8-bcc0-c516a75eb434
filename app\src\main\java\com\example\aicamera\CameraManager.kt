package com.example.aicamera

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.example.aicamera.utils.GalleryManager
import com.example.aicamera.utils.ImageProcessor
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * CameraManager handles all camera-related operations using CameraX
 * Optimized for 416x416 image capture to match AI model input requirements
 */
class CameraManager(private val context: Context) {

    companion object {
        private const val TAG = "CameraManager"
        private const val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
    }

    private var imageCapture: ImageCapture? = null
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    private val galleryManager = GalleryManager(context)

    /**
     * Start camera and bind to lifecycle
     */
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        onCameraReady: () -> Unit,
        onError: (String) -> Unit
    ) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            try {
                // Camera provider is now guaranteed to be available
                cameraProvider = cameraProviderFuture.get()

                // Set up camera use cases
                bindCameraUseCases(previewView, lifecycleOwner)
                onCameraReady()

            } catch (exc: Exception) {
                Log.e(TAG, "Use case binding failed", exc)
                onError("Failed to start camera: ${exc.message}")
            }
        }, ContextCompat.getMainExecutor(context))
    }

    /**
     * Bind camera use cases to lifecycle
     */
    private fun bindCameraUseCases(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        val cameraProvider = cameraProvider ?: throw IllegalStateException("Camera initialization failed.")

        // Preview use case - optimized for AI processing
        val preview = Preview.Builder()
            .build().also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }

        // Image capture use case - optimized for 416x416 AI models
        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
            .setJpegQuality(95) // High quality for AI processing
            .setTargetResolution(android.util.Size(416, 416)) // Exact size for AI model
            .setTargetAspectRatio(AspectRatio.RATIO_4_3) // Force square aspect ratio
            .build()

        // Select back camera as default
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

        try {
            // Unbind use cases before rebinding
            cameraProvider.unbindAll()

            // Bind use cases to camera
            camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageCapture
            )

        } catch (exc: Exception) {
            Log.e(TAG, "Use case binding failed", exc)
            throw exc
        }
    }

    /**
     * Capture a photo and save it to both internal storage and device gallery
     */
    fun capturePhoto(
        onImageCaptured: (File) -> Unit,
        onError: (String) -> Unit,
        onGallerySaved: (Uri) -> Unit = {},
        onGalleryError: (String) -> Unit = {}
    ) {
        val imageCapture = imageCapture ?: run {
            onError("Camera not initialized")
            return
        }

        // Create time stamped name and MediaStore entry
        val name = SimpleDateFormat(FILENAME_FORMAT, Locale.US)
            .format(System.currentTimeMillis())

        // Create output file in internal storage
        val photoFile = File(
            context.getExternalFilesDir(null),
            "$name.jpg"
        )

        // Create output options object which contains file + metadata
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        // Set up image capture listener, which is triggered after photo has been taken
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exception: ImageCaptureException) {
                    Log.e(TAG, "Photo capture failed: ${exception.message}", exception)
                    onError("Photo capture failed: ${exception.message}")
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    Log.d(TAG, "Photo capture succeeded: ${photoFile.absolutePath}")

                    // Check original image size
                    val originalDimensions = ImageProcessor.getImageDimensions(photoFile)
                    Log.d(TAG, "📏 Captured image size: ${originalDimensions?.first}x${originalDimensions?.second}")

                    // Resize image to exactly 416x416 for optimal AI processing
                    val resizeSuccess = ImageProcessor.resizeImageTo416x416(photoFile)
                    if (resizeSuccess) {
                        Log.d(TAG, "✅ Image resized to 416x416 (${ImageProcessor.getFileSizeKB(photoFile)} KB)")
                    } else {
                        Log.w(TAG, "⚠️ Failed to resize image, using original")
                    }

                    // Notify that the image is ready for AI processing
                    onImageCaptured(photoFile)

                    // Save to gallery in background (using the 416x416 version)
                    if (galleryManager.hasStoragePermission()) {
                        galleryManager.saveToGallery(
                            imageFile = photoFile,
                            onSuccess = { uri ->
                                Log.d(TAG, "✅ Image saved to gallery: $uri")
                                onGallerySaved(uri)
                            },
                            onError = { error ->
                                Log.w(TAG, "⚠️ Failed to save to gallery: $error")
                                onGalleryError(error)
                            }
                        )
                    } else {
                        Log.w(TAG, "⚠️ No storage permission for gallery save")
                        onGalleryError("Storage permission not granted")
                    }
                }
            }
        )
    }

    /**
     * Toggle flash on/off
     */
    fun toggleFlash(isFlashOn: Boolean) {
        camera?.cameraControl?.enableTorch(isFlashOn)
    }

    /**
     * Check if flash is available
     */
    fun hasFlash(): Boolean {
        return camera?.cameraInfo?.hasFlashUnit() ?: false
    }

    /**
     * Get camera instance for direct control
     */
    fun getCamera(): Camera? = camera

    /**
     * Clean up camera resources
     */
    fun shutdown() {
        cameraExecutor.shutdown()
        cameraProvider?.unbindAll()
    }
}
