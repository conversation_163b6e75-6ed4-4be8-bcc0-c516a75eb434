/ Header Record For PersistentHashMapValueStorage kotlin.Enum android.app.Application$ #androidx.activity.ComponentActivity) (androidx.appcompat.app.AppCompatActivity# "com.example.aicamera.NetworkResult# "com.example.aicamera.NetworkResult# "com.example.aicamera.NetworkResult) (androidx.appcompat.app.AppCompatActivity$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity