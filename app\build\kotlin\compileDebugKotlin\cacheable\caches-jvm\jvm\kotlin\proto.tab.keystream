com/example/aicamera/AiResponsecom/example/aicamera/ApiConfig*com/example/aicamera/ApiConfig$ApiProvider*com/example/aicamera/ApiConfig$LocalServer+com/example/aicamera/ApiConfig$PythonBridge*com/example/aicamera/ApiConfig$HuggingFace&com/example/aicamera/ApiConfig$Network"com/example/aicamera/CameraManager,com/example/aicamera/CameraManager$Companion%com/example/aicamera/ChessApplication/com/example/aicamera/ChessApplication$Companion(com/example/aicamera/ComposeMainActivity*com/example/aicamera/ComposeMainActivityKt!com/example/aicamera/MainActivity+com/example/aicamera/MainActivity$Companion#com/example/aicamera/NetworkManager-com/example/aicamera/NetworkManager$Companion"com/example/aicamera/NetworkResult*com/example/aicamera/NetworkResult$Success(com/example/aicamera/NetworkResult$Error*com/example/aicamera/NetworkResult$Loading(com/example/aicamera/PythonGradioService2com/example/aicamera/PythonGradioService$Companion%com/example/aicamera/SettingsActivity/com/example/aicamera/SettingsActivity$Companion0com/example/aicamera/chess/ChessAnalysisActivity:com/example/aicamera/chess/ChessAnalysisActivity$Companion2com/example/aicamera/chess/ChessAnalysisActivityKt.com/example/aicamera/chess/ChessBoardInterface*com/example/aicamera/chess/ChessBoardUtils.com/example/aicamera/chess/ChessBoardWebViewKt)com/example/aicamera/chess/ChessGameState#com/example/aicamera/chess/GameMode)com/example/aicamera/chess/EngineAnalysis'com/example/aicamera/chess/AnalysisLine%com/example/aicamera/chess/ChessPiece/com/example/aicamera/chess/ChessPiece$Companion$com/example/aicamera/chess/PieceType%com/example/aicamera/chess/PieceColor+com/example/aicamera/chess/BoardOrientation%com/example/aicamera/chess/GameResult)com/example/aicamera/chess/GameResultType%com/example/aicamera/chess/PlayOption%com/example/aicamera/chess/RecentGame)com/example/aicamera/chess/LearningModule%com/example/aicamera/chess/ScanResult'com/example/aicamera/chess/PositionInfo.com/example/aicamera/chess/ChessUIComponentsKt)com/example/aicamera/chess/ChessViewModel3com/example/aicamera/chess/ChessViewModel$Companion+com/example/aicamera/chess/StockfishManager5com/example/aicamera/chess/StockfishManager$Companion/com/example/aicamera/chess/ui/theme/ChessColors0com/example/aicamera/chess/ui/theme/ChessThemeKt.com/example/aicamera/ui/screens/CameraScreenKt,com/example/aicamera/ui/screens/HomeScreenKt%com/example/aicamera/ui/theme/ColorKt%com/example/aicamera/ui/theme/ThemeKt$com/example/aicamera/ui/theme/TypeKt.kotlin_module.com/example/aicamera/chess/ChessBoardComposeKt2com/example/aicamera/ui/screens/ChessBoardScreenKt&com/example/aicamera/chess/PieceTrayKt3com/example/aicamera/integration/ExternalAppManager=com/example/aicamera/integration/ExternalAppManager$Companion7com/example/aicamera/preferences/ExternalAppPreferencesAcom/example/aicamera/preferences/ExternalAppPreferences$Companion1com/example/aicamera/preferences/ExternalChessApp3com/example/aicamera/preferences/SupportedChessApps4com/example/aicamera/ui/screens/AppSelectionScreenKt)com/example/aicamera/utils/GalleryManager3com/example/aicamera/utils/GalleryManager$Companion)com/example/aicamera/utils/ImageProcessor                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  