package com.example.aicamera.chess

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import android.util.Log
import com.github.bhlangonijr.chesslib.*
import com.github.bhlangonijr.chesslib.move.Move

/**
 * ViewModel for chess analysis and game management
 */
class ChessViewModel : ViewModel() {

    companion object {
        private const val TAG = "ChessViewModel"
    }

    private val _gameState = MutableStateFlow(ChessGameState())
    val gameState: StateFlow<ChessGameState> = _gameState.asStateFlow()

    private val stockfishManager = StockfishManager.getInstance()

    init {
        // Initialize with starting position
        loadPosition("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
    }

    /**
     * Load a position from FEN string
     */
    fun loadPosition(fen: String) {
        try {
            val board = Board()
            board.loadFromFen(fen)

            _gameState.value = _gameState.value.copy(
                board = board,
                currentFen = fen,
                moveHistory = emptyList(),
                selectedSquare = null,
                possibleMoves = emptyList(),
                lastMove = null,
                engineAnalysis = null
            )

            Log.d(TAG, "Position loaded: $fen")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to load position: $fen", e)
        }
    }

    /**
     * Make a move on the board
     */
    fun makeMove(moveString: String) {
        viewModelScope.launch {
            try {
                val currentState = _gameState.value
                val move = ChessBoardUtils.parseMove(moveString)

                if (move != null && ChessBoardUtils.isLegalMove(currentState.board, move)) {
                    val newBoard = Board()
                    newBoard.loadFromFen(currentState.currentFen)

                    if (ChessBoardUtils.makeMove(newBoard, move)) {
                        val newMoveHistory = currentState.moveHistory.plus(move)

                        _gameState.value = currentState.copy(
                            board = newBoard,
                            currentFen = newBoard.fen,
                            moveHistory = newMoveHistory,
                            selectedSquare = null,
                            possibleMoves = emptyList(),
                            lastMove = move
                        )

                        Log.d(TAG, "Move made: $moveString")

                        // Auto-analyze new position if analysis is enabled
                        if (currentState.isAnalyzing) {
                            analyzeCurrentPosition()
                        }
                    }
                } else {
                    Log.w(TAG, "Illegal move attempted: $moveString")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Failed to make move: $moveString", e)
            }
        }
    }

    /**
     * Select a square on the board
     */
    fun selectSquare(squareString: String) {
        try {
            val square = Square.valueOf(squareString.uppercase())
            val currentState = _gameState.value

            if (currentState.selectedSquare == square) {
                // Deselect
                _gameState.value = currentState.copy(
                    selectedSquare = null,
                    possibleMoves = emptyList()
                )
            } else {
                // Select and get possible moves
                val possibleMoves = ChessBoardUtils.getLegalMovesFromSquare(currentState.board, square)

                _gameState.value = currentState.copy(
                    selectedSquare = square,
                    possibleMoves = possibleMoves
                )
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to select square: $squareString", e)
        }
    }

    /**
     * Start engine analysis
     */
    suspend fun startAnalysis() {
        val currentState = _gameState.value

        _gameState.value = currentState.copy(isAnalyzing = true)

        analyzeCurrentPosition()
    }

    /**
     * Stop engine analysis
     */
    fun stopAnalysis() {
        stockfishManager.stopAnalysis()

        val currentState = _gameState.value
        _gameState.value = currentState.copy(isAnalyzing = false)
    }

    /**
     * Analyze current position
     */
    private suspend fun analyzeCurrentPosition() {
        try {
            val currentState = _gameState.value

            if (!currentState.isAnalyzing) return

            val analysis = stockfishManager.analyzePosition(currentState.currentFen)

            if (analysis != null) {
                _gameState.value = currentState.copy(engineAnalysis = analysis)
                Log.d(TAG, "Analysis complete: ${analysis.bestMove}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Analysis failed", e)
        }
    }

    /**
     * Go to a specific move in history
     */
    fun goToMove(moveIndex: Int) {
        try {
            val currentState = _gameState.value

            if (moveIndex < 0 || moveIndex >= currentState.moveHistory.size) return

            // Rebuild position up to the specified move
            val board = Board()
            board.loadFromFen("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")

            val movesToPlay = currentState.moveHistory.take(moveIndex + 1)
            for (move in movesToPlay) {
                board.doMove(move as com.github.bhlangonijr.chesslib.move.Move)
            }

            _gameState.value = currentState.copy(
                board = board,
                currentFen = board.fen,
                selectedSquare = null,
                possibleMoves = emptyList(),
                lastMove = movesToPlay.lastOrNull()
            )

        } catch (e: Exception) {
            Log.e(TAG, "Failed to go to move $moveIndex", e)
        }
    }

    /**
     * Toggle edit mode
     */
    fun toggleEditMode() {
        val currentState = _gameState.value
        _gameState.value = currentState.copy(
            isEditMode = !currentState.isEditMode,
            selectedSquare = null,
            possibleMoves = emptyList()
        )
    }

    /**
     * Reset to starting position
     */
    fun resetPosition() {
        loadPosition("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
    }

    /**
     * Clear board
     */
    fun clearBoard() {
        loadPosition("8/8/8/8/8/8/8/8 w - - 0 1")
    }

    override fun onCleared() {
        super.onCleared()
        stockfishManager.cleanup()
    }
}
