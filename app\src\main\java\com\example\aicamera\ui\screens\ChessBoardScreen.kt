package com.example.aicamera.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.aicamera.chess.SimpleFENBoard
import com.example.aicamera.chess.ChessGameState
import com.example.aicamera.chess.PieceTray
import com.example.aicamera.chess.EditModeControls
import com.example.aicamera.chess.ChessPiece
import com.github.bhlangonijr.chesslib.Board
import com.example.aicamera.ui.theme.getDynamicColorScheme

/**
 * Chess Board Screen - Displays the chess board with FEN from camera
 * Features interactive board, move history, and analysis integration
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChessBoardScreen(
    fen: String?,
    onBackPressed: () -> Unit,
    onAnalyzePosition: (String) -> Unit = {}
) {
    val dynamicColorScheme = getDynamicColorScheme()
    var currentFEN by remember { mutableStateOf(fen ?: "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1") }
    var editableFEN by remember { mutableStateOf(currentFEN) }
    var isEditingFEN by remember { mutableStateOf(false) }
    var fenError by remember { mutableStateOf<String?>(null) }
    var moveHistory by remember { mutableStateOf(listOf<String>()) }
    var isEditMode by remember { mutableStateOf(false) }
    var selectedPiece by remember { mutableStateOf<ChessPiece?>(null) }
    var showTurnIndicator by remember { mutableStateOf(true) }
    var showCastlingRights by remember { mutableStateOf(true) }
    var showEnPassant by remember { mutableStateOf(true) }
    var showCoordinates by remember { mutableStateOf(true) }
    var boardFlipped by remember { mutableStateOf(false) }

    // Function to convert AI piece-only FEN to complete FEN
    fun completeAIFen(pieceFen: String): String {
        // If already complete FEN, return as is
        if (pieceFen.split(" ").size >= 4) {
            return pieceFen
        }

        // AI returns only piece positions, add the rest based on UI controls
        val turn = if (showTurnIndicator) "w" else "b"
        val castling = buildString {
            if (showCastlingRights) {
                append("KQkq")
            } else {
                append("-")
            }
        }
        val enPassant = if (showEnPassant) "-" else "-"
        val halfMove = "0"
        val fullMove = "1"

        return "$pieceFen $turn $castling $enPassant $halfMove $fullMove"
    }

    val board = remember(currentFEN, showTurnIndicator, showCastlingRights, showEnPassant) {
        try {
            val completeFEN = completeAIFen(currentFEN)
            Board().apply {
                loadFromFen(completeFEN)
            }
        } catch (e: Exception) {
            // If FEN is invalid, return default board
            Board()
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = dynamicColorScheme.background,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Chess Board",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.SemiBold
                        )
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = { onAnalyzePosition(currentFEN) }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Analytics,
                            contentDescription = "Analyze",
                            tint = dynamicColorScheme.primary
                        )
                    }
                    IconButton(
                        onClick = {
                            // Reset to starting position
                            currentFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
                            moveHistory = emptyList()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.RestartAlt,
                            contentDescription = "Reset"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = dynamicColorScheme.surface,
                    titleContentColor = dynamicColorScheme.onSurface
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Chess Board
            item {
                SimpleFENBoard(
                    fen = currentFEN,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Position Information
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.surface
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Position Information",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = dynamicColorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "FEN:",
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = if (fen != null) "From Camera" else "Default",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = dynamicColorScheme.primary
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                IconButton(
                                    onClick = {
                                        isEditingFEN = !isEditingFEN
                                        if (isEditingFEN) {
                                            editableFEN = currentFEN
                                        }
                                    }
                                ) {
                                    Icon(
                                        imageVector = if (isEditingFEN) Icons.Default.Check else Icons.Default.Edit,
                                        contentDescription = if (isEditingFEN) "Save FEN" else "Edit FEN",
                                        tint = dynamicColorScheme.primary
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        if (isEditingFEN) {
                            // Editable FEN input
                            OutlinedTextField(
                                value = editableFEN,
                                onValueChange = { newFEN ->
                                    editableFEN = newFEN
                                    // Validate FEN in real-time
                                    try {
                                        Board().loadFromFen(newFEN)
                                        fenError = null
                                        currentFEN = newFEN
                                    } catch (e: Exception) {
                                        fenError = "Invalid FEN: ${e.message}"
                                    }
                                },
                                label = { Text("FEN Position") },
                                isError = fenError != null,
                                supportingText = {
                                    fenError?.let { error ->
                                        Text(
                                            text = error,
                                            color = MaterialTheme.colorScheme.error
                                        )
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                textStyle = MaterialTheme.typography.bodySmall.copy(
                                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                )
                            )
                        } else {
                            // Display-only FEN
                            Text(
                                text = currentFEN,
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                ),
                                color = dynamicColorScheme.onSurfaceVariant,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(
                                        dynamicColorScheme.surface.copy(alpha = 0.3f),
                                        RoundedCornerShape(8.dp)
                                    )
                                    .padding(12.dp)
                            )
                        }
                    }
                }
            }

            // Edit Mode Controls
            item {
                EditModeControls(
                    isEditMode = isEditMode,
                    onToggleEditMode = {
                        isEditMode = !isEditMode
                        selectedPiece = null
                    },
                    onClearBoard = {
                        currentFEN = "8/8/8/8/8/8/8/8 w - - 0 1"
                        editableFEN = currentFEN
                    },
                    onResetBoard = {
                        currentFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
                        editableFEN = currentFEN
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Analysis Mode Controls
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.surface
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Analysis Options",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = dynamicColorScheme.onSurface,
                            modifier = Modifier.padding(bottom = 12.dp)
                        )

                        // Display Options
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("Show turn indicator")
                            Switch(
                                checked = showTurnIndicator,
                                onCheckedChange = { showTurnIndicator = it }
                            )
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("Show castling rights")
                            Switch(
                                checked = showCastlingRights,
                                onCheckedChange = { showCastlingRights = it }
                            )
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("Show en passant")
                            Switch(
                                checked = showEnPassant,
                                onCheckedChange = { showEnPassant = it }
                            )
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("Show coordinates")
                            Switch(
                                checked = showCoordinates,
                                onCheckedChange = { showCoordinates = it }
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // Board Controls
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            OutlinedButton(
                                onClick = { boardFlipped = !boardFlipped },
                                modifier = Modifier.weight(1f)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.RotateLeft,
                                    contentDescription = "Flip Board",
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("Flip Board")
                            }
                        }
                    }
                }
            }

            // Piece Tray (only in edit mode)
            if (isEditMode) {
                item {
                    PieceTray(
                        selectedPiece = selectedPiece,
                        onPieceSelected = { piece ->
                            selectedPiece = piece
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            // Move History
            if (moveHistory.isNotEmpty()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = dynamicColorScheme.surfaceVariant
                        ),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Move History",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.SemiBold
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                            Spacer(modifier = Modifier.height(8.dp))

                            moveHistory.forEachIndexed { index, move ->
                                Text(
                                    text = "${index + 1}. $move",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = dynamicColorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(vertical = 2.dp)
                                )
                            }
                        }
                    }
                }
            }

            // Action Buttons
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Analyze Button
                    Button(
                        onClick = { onAnalyzePosition(currentFEN) },
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = dynamicColorScheme.primary
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Analytics,
                            contentDescription = "Analyze",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Analyze")
                    }

                    // Share FEN Button
                    OutlinedButton(
                        onClick = {
                            // TODO: Share FEN functionality
                        },
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "Share",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Share")
                    }
                }
            }

            // Instructions
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer.copy(alpha = 0.3f)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = "Info",
                                tint = dynamicColorScheme.primary,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "How to use",
                                style = MaterialTheme.typography.titleSmall.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "• Tap pieces to select them\n• Tap another square to move\n• Use the camera to scan real chess boards\n• Analyze positions with the engine",
                            style = MaterialTheme.typography.bodySmall,
                            color = dynamicColorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}
