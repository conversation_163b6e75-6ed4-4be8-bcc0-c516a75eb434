/ Header Record For PersistentHashMapValueStorage5 4app/src/main/java/com/example/aicamera/AiResponse.kt4 3app/src/main/java/com/example/aicamera/ApiConfig.kt8 7app/src/main/java/com/example/aicamera/CameraManager.kt; :app/src/main/java/com/example/aicamera/ChessApplication.kt7 6app/src/main/java/com/example/aicamera/MainActivity.kt9 8app/src/main/java/com/example/aicamera/NetworkManager.kt> =app/src/main/java/com/example/aicamera/PythonGradioService.kt; :app/src/main/java/com/example/aicamera/SettingsActivity.ktF Eapp/src/main/java/com/example/aicamera/chess/ChessAnalysisActivity.ktB Aapp/src/main/java/com/example/aicamera/chess/ChessBoardWebView.kt< ;app/src/main/java/com/example/aicamera/chess/ChessModels.ktB Aapp/src/main/java/com/example/aicamera/chess/ChessUIComponents.kt? >app/src/main/java/com/example/aicamera/chess/ChessViewModel.ktA @app/src/main/java/com/example/aicamera/chess/StockfishManager.ktD Capp/src/main/java/com/example/aicamera/chess/ui/theme/ChessTheme.ktB Aapp/src/main/java/com/example/aicamera/chess/ChessUIComponents.kt