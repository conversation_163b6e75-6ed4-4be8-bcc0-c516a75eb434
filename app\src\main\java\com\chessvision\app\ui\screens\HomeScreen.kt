package com.chessvision.app.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.chessvision.app.ui.theme.LocalChessExpressiveSurfaces
import com.chessvision.app.ui.theme.getDynamicColorScheme

/**
 * Home Screen - Chess.com inspired landing page with Material Design 3 styling
 * Features prominent chess gameplay options with integrated camera scanning
 */
@Composable
fun HomeScreen(
    onNavigateToCamera: () -> Unit,
    onNavigateToBoard: () -> Unit,
    onNavigateToAnalysis: () -> Unit,
    onNavigateToSettings: () -> Unit
) {
    val dynamicColorScheme = getDynamicColorScheme()
    val expressiveSurfaces = LocalChessExpressiveSurfaces.current

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = dynamicColorScheme.background,
        bottomBar = {
            // Chess.com style bottom navigation
            NavigationBar(
                containerColor = dynamicColorScheme.surface,
                contentColor = dynamicColorScheme.onSurface
            ) {
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Home, contentDescription = "Home") },
                    label = { Text("Home") },
                    selected = true,
                    onClick = { }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.CameraAlt, contentDescription = "Scan") },
                    label = { Text("Scan") },
                    selected = false,
                    onClick = onNavigateToCamera
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Games, contentDescription = "Play") },
                    label = { Text("Play") },
                    selected = false,
                    onClick = onNavigateToBoard
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Analytics, contentDescription = "Analysis") },
                    label = { Text("Analysis") },
                    selected = false,
                    onClick = onNavigateToAnalysis
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Settings, contentDescription = "Settings") },
                    label = { Text("Settings") },
                    selected = false,
                    onClick = onNavigateToSettings
                )
            }
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(dynamicColorScheme.background),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Hero Section - Chess.com inspired
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(20.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Psychology,
                            contentDescription = "Chess AI",
                            modifier = Modifier.size(48.dp),
                            tint = dynamicColorScheme.onPrimaryContainer
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            text = "♛ Chess Vision AI",
                            style = MaterialTheme.typography.headlineMedium.copy(
                                color = dynamicColorScheme.onPrimaryContainer,
                                fontWeight = FontWeight.Bold
                            )
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Scan, analyze, and play chess with AI-powered vision",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                color = dynamicColorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                            ),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // Quick Actions - Chess.com style
            item {
                Text(
                    text = "Quick Actions",
                    style = MaterialTheme.typography.titleLarge.copy(
                        color = dynamicColorScheme.onSurface,
                        fontWeight = FontWeight.SemiBold
                    ),
                    modifier = Modifier.padding(horizontal = 4.dp)
                )
            }

            // Action Cards Row - Chess.com inspired layout
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Scan Board Card
                    Card(
                        modifier = Modifier
                            .weight(1f)
                            .height(120.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = dynamicColorScheme.secondary
                        ),
                        shape = RoundedCornerShape(16.dp),
                        onClick = onNavigateToCamera
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.CameraAlt,
                                contentDescription = "Scan Board",
                                modifier = Modifier.size(32.dp),
                                tint = dynamicColorScheme.onSecondary
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Scan Board",
                                style = MaterialTheme.typography.labelLarge.copy(
                                    color = dynamicColorScheme.onSecondary,
                                    fontWeight = FontWeight.Medium
                                ),
                                textAlign = TextAlign.Center
                            )
                        }
                    }

                    // Play Chess Card
                    Card(
                        modifier = Modifier
                            .weight(1f)
                            .height(120.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = dynamicColorScheme.tertiary
                        ),
                        shape = RoundedCornerShape(16.dp),
                        onClick = onNavigateToBoard
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Games,
                                contentDescription = "Play Chess",
                                modifier = Modifier.size(32.dp),
                                tint = dynamicColorScheme.onTertiary
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Play Chess",
                                style = MaterialTheme.typography.labelLarge.copy(
                                    color = dynamicColorScheme.onTertiary,
                                    fontWeight = FontWeight.Medium
                                ),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }

            // Analysis Section
            item {
                Text(
                    text = "Analysis Tools",
                    style = MaterialTheme.typography.titleLarge.copy(
                        color = dynamicColorScheme.onSurface,
                        fontWeight = FontWeight.SemiBold
                    ),
                    modifier = Modifier.padding(horizontal = 4.dp)
                )
            }

            // Analysis Card
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.surfaceContainer
                    ),
                    shape = RoundedCornerShape(16.dp),
                    onClick = onNavigateToAnalysis
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Analytics,
                            contentDescription = "Analysis",
                            modifier = Modifier.size(40.dp),
                            tint = dynamicColorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(16.dp))
                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = "Chess Analysis",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    color = dynamicColorScheme.onSurface,
                                    fontWeight = FontWeight.SemiBold
                                )
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "Analyze positions with Stockfish engine",
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    color = dynamicColorScheme.onSurfaceVariant
                                )
                            )
                        }
                        Icon(
                            imageVector = Icons.Default.ChevronRight,
                            contentDescription = "Go to Analysis",
                            tint = dynamicColorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            // Features Section
            item {
                Text(
                    text = "Features",
                    style = MaterialTheme.typography.titleLarge.copy(
                        color = dynamicColorScheme.onSurface,
                        fontWeight = FontWeight.SemiBold
                    ),
                    modifier = Modifier.padding(horizontal = 4.dp)
                )
            }

            // Features List
            item {
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FeatureItem(
                        icon = Icons.Default.CameraAlt,
                        title = "AI Vision Scanning",
                        description = "Capture chess boards at 416x416 resolution for optimal AI processing",
                        colorScheme = dynamicColorScheme
                    )
                    FeatureItem(
                        icon = Icons.Default.Psychology,
                        title = "FEN Generation",
                        description = "Convert board images to FEN notation using advanced AI models",
                        colorScheme = dynamicColorScheme
                    )
                    FeatureItem(
                        icon = Icons.Default.Speed,
                        title = "Real-time Analysis",
                        description = "Get instant position evaluation and move suggestions",
                        colorScheme = dynamicColorScheme
                    )
                }
            }
        }
    }
}

@Composable
private fun FeatureItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    description: String,
    colorScheme: ColorScheme
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(24.dp),
                tint = colorScheme.primary
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleSmall.copy(
                        color = colorScheme.onSurface,
                        fontWeight = FontWeight.Medium
                    )
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = colorScheme.onSurfaceVariant
                    )
                )
            }
        }
    }
}
