package com.example.aicamera.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * Image Processing Utility for Chess AI Camera
 * Ensures all images are exactly 416x416 for optimal AI processing
 */
object ImageProcessor {
    
    private const val TAG = "ImageProcessor"
    private const val TARGET_SIZE = 416
    private const val JPEG_QUALITY = 95
    
    /**
     * Resize image file to exactly 416x416 and save back to the same file
     * This ensures consistent input size for AI processing and API calls
     */
    fun resizeImageTo416x416(imageFile: File): <PERSON><PERSON>an {
        return try {
            Log.d(TAG, "🔄 Resizing image to 416x416: ${imageFile.name}")
            
            // Load the original image
            val originalBitmap = BitmapFactory.decodeFile(imageFile.absolutePath)
            if (originalBitmap == null) {
                Log.e(TAG, "❌ Failed to decode image file")
                return false
            }
            
            Log.d(TAG, "📏 Original size: ${originalBitmap.width}x${originalBitmap.height}")
            
            // Check if already correct size
            if (originalBitmap.width == TARGET_SIZE && originalBitmap.height == TARGET_SIZE) {
                Log.d(TAG, "✅ Image already 416x416, no resize needed")
                originalBitmap.recycle()
                return true
            }
            
            // Resize to exactly 416x416
            val resizedBitmap = Bitmap.createScaledBitmap(
                originalBitmap, 
                TARGET_SIZE, 
                TARGET_SIZE, 
                true // Use bilinear filtering for better quality
            )
            
            // Recycle original bitmap to free memory
            originalBitmap.recycle()
            
            // Save resized image back to the same file
            FileOutputStream(imageFile).use { outputStream ->
                val success = resizedBitmap.compress(
                    Bitmap.CompressFormat.JPEG, 
                    JPEG_QUALITY, 
                    outputStream
                )
                
                if (success) {
                    Log.d(TAG, "✅ Image resized and saved: ${resizedBitmap.width}x${resizedBitmap.height}")
                } else {
                    Log.e(TAG, "❌ Failed to compress and save resized image")
                }
                
                // Recycle resized bitmap
                resizedBitmap.recycle()
                
                return success
            }
            
        } catch (e: IOException) {
            Log.e(TAG, "❌ IO error during image resize", e)
            false
        } catch (e: OutOfMemoryError) {
            Log.e(TAG, "❌ Out of memory during image resize", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "❌ Unexpected error during image resize", e)
            false
        }
    }
    
    /**
     * Create a 416x416 copy of an image without modifying the original
     * Useful for creating API-ready copies while preserving originals
     */
    fun createResizedCopy(sourceFile: File, targetFile: File): Boolean {
        return try {
            Log.d(TAG, "🔄 Creating 416x416 copy: ${sourceFile.name} -> ${targetFile.name}")
            
            // Load the original image
            val originalBitmap = BitmapFactory.decodeFile(sourceFile.absolutePath)
            if (originalBitmap == null) {
                Log.e(TAG, "❌ Failed to decode source image file")
                return false
            }
            
            // Resize to exactly 416x416
            val resizedBitmap = Bitmap.createScaledBitmap(
                originalBitmap, 
                TARGET_SIZE, 
                TARGET_SIZE, 
                true
            )
            
            // Recycle original bitmap
            originalBitmap.recycle()
            
            // Save resized image to target file
            FileOutputStream(targetFile).use { outputStream ->
                val success = resizedBitmap.compress(
                    Bitmap.CompressFormat.JPEG, 
                    JPEG_QUALITY, 
                    outputStream
                )
                
                // Recycle resized bitmap
                resizedBitmap.recycle()
                
                if (success) {
                    Log.d(TAG, "✅ Resized copy created successfully")
                } else {
                    Log.e(TAG, "❌ Failed to save resized copy")
                }
                
                return success
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error creating resized copy", e)
            false
        }
    }
    
    /**
     * Get image dimensions without loading the full bitmap
     * Useful for checking image size efficiently
     */
    fun getImageDimensions(imageFile: File): Pair<Int, Int>? {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imageFile.absolutePath, options)
            
            if (options.outWidth > 0 && options.outHeight > 0) {
                Pair(options.outWidth, options.outHeight)
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting image dimensions", e)
            null
        }
    }
    
    /**
     * Validate that an image is exactly 416x416
     */
    fun isCorrectSize(imageFile: File): Boolean {
        val dimensions = getImageDimensions(imageFile)
        return dimensions?.let { (width, height) ->
            width == TARGET_SIZE && height == TARGET_SIZE
        } ?: false
    }
    
    /**
     * Get file size in KB for logging
     */
    fun getFileSizeKB(file: File): Long {
        return file.length() / 1024
    }
}
