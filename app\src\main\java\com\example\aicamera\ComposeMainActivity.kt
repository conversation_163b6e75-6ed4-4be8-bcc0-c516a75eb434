package com.example.aicamera

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.aicamera.ui.screens.CameraScreen
import com.example.aicamera.ui.screens.HomeScreen
import com.example.aicamera.ui.theme.ChessTheme

/**
 * New Compose-based MainActivity with Chess.com inspired UI
 * Replaces the old View-based MainActivity with modern Compose design
 */
class ComposeMainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Load settings from preferences
        SettingsActivity.loadSettingsOnStartup(this)

        setContent {
            ChessTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    ChessApp()
                }
            }
        }
    }
}

@Composable
fun ChessApp() {
    val navController = rememberNavController()
    var capturedFEN by remember { mutableStateOf<String?>(null) }

    NavHost(
        navController = navController,
        startDestination = "home"
    ) {
        composable("home") {
            HomeScreen(
                onNavigateToCamera = {
                    navController.navigate("camera")
                },
                onNavigateToAnalysis = {
                    navController.navigate("analysis")
                },
                onNavigateToSettings = {
                    navController.navigate("settings")
                }
            )
        }

        composable("camera") {
            CameraScreen(
                onBackPressed = {
                    navController.popBackStack()
                },
                onFENGenerated = { fen ->
                    capturedFEN = fen
                    // Could navigate to analysis screen with FEN
                    navController.navigate("analysis")
                }
            )
        }

        composable("analysis") {
            AnalysisScreen(
                fen = capturedFEN,
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }

        composable("settings") {
            SettingsScreen(
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnalysisScreen(
    fen: String?,
    onBackPressed: () -> Unit
) {
    // Placeholder for analysis screen
    // This would integrate with your existing ChessAnalysisActivity
    Column {
        TopAppBar(
            title = { Text("Analysis") },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            }
        )

        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (fen != null) "FEN: $fen" else "No FEN available",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackPressed: () -> Unit
) {
    // Placeholder for settings screen
    // This would integrate with your existing SettingsActivity
    Column {
        TopAppBar(
            title = { Text("Settings") },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            }
        )

        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "Settings coming soon...",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}
