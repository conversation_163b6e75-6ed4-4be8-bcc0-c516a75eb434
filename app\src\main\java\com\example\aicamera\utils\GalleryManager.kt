package com.example.aicamera.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * Gallery Manager - Handles saving chess position images to device gallery
 * Saves 416x416 images with proper metadata for easy identification
 */
class GalleryManager(private val context: Context) {
    
    companion object {
        private const val TAG = "GalleryManager"
        private const val CHESS_FOLDER = "Chess Positions"
        private const val FILENAME_FORMAT = "chess_position_yyyyMMdd_HHmmss"
        private const val IMAGE_QUALITY = 95 // High quality for chess positions
    }
    
    /**
     * Save captured chess image to device gallery
     * Creates a dedicated "Chess Positions" folder for organization
     */
    fun saveToGallery(
        imageFile: File,
        onSuccess: (Uri) -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            // Load the image
            val bitmap = BitmapFactory.decodeFile(imageFile.absolutePath)
            if (bitmap == null) {
                onError("Failed to load captured image")
                return
            }
            
            // Ensure image is 416x416 (resize if needed)
            val resizedBitmap = if (bitmap.width != 416 || bitmap.height != 416) {
                Bitmap.createScaledBitmap(bitmap, 416, 416, true)
            } else {
                bitmap
            }
            
            // Save to gallery with proper metadata
            saveImageToGallery(resizedBitmap, onSuccess, onError)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save image to gallery", e)
            onError("Failed to save to gallery: ${e.message}")
        }
    }
    
    /**
     * Save bitmap directly to gallery (for processed images)
     */
    fun saveBitmapToGallery(
        bitmap: Bitmap,
        onSuccess: (Uri) -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            // Ensure image is 416x416
            val resizedBitmap = if (bitmap.width != 416 || bitmap.height != 416) {
                Bitmap.createScaledBitmap(bitmap, 416, 416, true)
            } else {
                bitmap
            }
            
            saveImageToGallery(resizedBitmap, onSuccess, onError)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save bitmap to gallery", e)
            onError("Failed to save to gallery: ${e.message}")
        }
    }
    
    /**
     * Internal method to save image to gallery with proper metadata
     */
    private fun saveImageToGallery(
        bitmap: Bitmap,
        onSuccess: (Uri) -> Unit,
        onError: (String) -> Unit
    ) {
        val timestamp = System.currentTimeMillis()
        val filename = SimpleDateFormat(FILENAME_FORMAT, Locale.US).format(Date(timestamp))
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ - Use MediaStore API
            saveToMediaStore(bitmap, filename, timestamp, onSuccess, onError)
        } else {
            // Android 9 and below - Use legacy external storage
            saveLegacy(bitmap, filename, onSuccess, onError)
        }
    }
    
    /**
     * Save using MediaStore API (Android 10+)
     */
    private fun saveToMediaStore(
        bitmap: Bitmap,
        filename: String,
        timestamp: Long,
        onSuccess: (Uri) -> Unit,
        onError: (String) -> Unit
    ) {
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, "$filename.jpg")
            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
            put(MediaStore.MediaColumns.DATE_ADDED, timestamp / 1000)
            put(MediaStore.MediaColumns.DATE_MODIFIED, timestamp / 1000)
            put(MediaStore.MediaColumns.SIZE, bitmap.byteCount)
            put(MediaStore.MediaColumns.WIDTH, 416)
            put(MediaStore.MediaColumns.HEIGHT, 416)
            
            // Add to Pictures/Chess Positions folder
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                put(MediaStore.MediaColumns.RELATIVE_PATH, "${Environment.DIRECTORY_PICTURES}/$CHESS_FOLDER")
                put(MediaStore.MediaColumns.IS_PENDING, 1)
            }
        }
        
        val resolver = context.contentResolver
        val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
        
        if (uri != null) {
            try {
                resolver.openOutputStream(uri)?.use { outputStream ->
                    if (bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, outputStream)) {
                        // Mark as not pending
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            contentValues.clear()
                            contentValues.put(MediaStore.MediaColumns.IS_PENDING, 0)
                            resolver.update(uri, contentValues, null, null)
                        }
                        
                        Log.d(TAG, "✅ Image saved to gallery: $uri")
                        onSuccess(uri)
                    } else {
                        onError("Failed to compress image")
                    }
                }
            } catch (e: IOException) {
                Log.e(TAG, "Failed to save image", e)
                onError("Failed to save image: ${e.message}")
            }
        } else {
            onError("Failed to create media store entry")
        }
    }
    
    /**
     * Save using legacy external storage (Android 9 and below)
     */
    @Suppress("DEPRECATION")
    private fun saveLegacy(
        bitmap: Bitmap,
        filename: String,
        onSuccess: (Uri) -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
            val chessDir = File(picturesDir, CHESS_FOLDER)
            
            // Create directory if it doesn't exist
            if (!chessDir.exists()) {
                chessDir.mkdirs()
            }
            
            val imageFile = File(chessDir, "$filename.jpg")
            
            FileOutputStream(imageFile).use { outputStream ->
                if (bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, outputStream)) {
                    // Notify media scanner
                    val uri = Uri.fromFile(imageFile)
                    context.sendBroadcast(
                        android.content.Intent(android.content.Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri)
                    )
                    
                    Log.d(TAG, "✅ Image saved to gallery (legacy): $uri")
                    onSuccess(uri)
                } else {
                    onError("Failed to compress image")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save image (legacy)", e)
            onError("Failed to save image: ${e.message}")
        }
    }
    
    /**
     * Check if we have permission to write to external storage
     */
    fun hasStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ doesn't need WRITE_EXTERNAL_STORAGE for MediaStore
            true
        } else {
            // Android 9 and below need WRITE_EXTERNAL_STORAGE
            context.checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE) == 
                android.content.pm.PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * Get required permissions for gallery storage
     */
    fun getRequiredPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ doesn't need WRITE_EXTERNAL_STORAGE for MediaStore
            emptyArray()
        } else {
            // Android 9 and below need WRITE_EXTERNAL_STORAGE
            arrayOf(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    }
}
