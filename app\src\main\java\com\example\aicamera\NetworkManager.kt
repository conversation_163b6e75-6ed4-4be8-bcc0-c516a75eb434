package com.example.aicamera

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * NetworkManager handles all network operations for communicating with AI servers
 * Supports both local server and Hugging Face cloud API
 */
class NetworkManager {

    companion object {
        private const val TAG = "NetworkManager"
    }

    private val pythonGradioService: PythonGradioService = PythonGradioService.getInstance()

    // Python gradio service will be initialized when first used

    /**
     * Upload an image file to the AI server (supports both local and cloud APIs)
     * @param imageFile The image file to upload
     * @return NetworkResult containing the response or error
     */
    suspend fun uploadImage(imageFile: File): NetworkResult<AiResponse> {
        // Validate configuration
        if (!ApiConfig.isConfigurationValid()) {
            return NetworkResult.Error("API configuration is invalid. Please check your settings.")
        }

        return when (ApiConfig.currentProvider) {
            ApiConfig.ApiProvider.LOCAL_SERVER -> uploadToPythonGradio(imageFile)
            ApiConfig.ApiProvider.HUGGING_FACE -> uploadToPythonGradio(imageFile)
        }
    }



    /**
     * Upload image using Python gradio_client library directly on Android
     * @param imageFile The 416x416 chess board image
     * @return NetworkResult containing the chess analysis
     */
    suspend fun uploadToPythonGradio(imageFile: File): NetworkResult<AiResponse> = withContext(Dispatchers.IO) {
        return@withContext try {
            Log.d(TAG, "🐍 Starting Python gradio_client upload...")
            Log.d(TAG, "📁 File: ${imageFile.name}, Size: ${imageFile.length()} bytes")

            // Initialize Python service if not already done (should be pre-initialized)
            val context = ChessApplication.getInstance()
            if (context == null) {
                return@withContext NetworkResult.Error("Application context not available")
            }

            if (!pythonGradioService.initialize(context)) {
                return@withContext NetworkResult.Error("Failed to initialize Python gradio service")
            }

            // Use Python gradio service (already runs on IO thread)
            pythonGradioService.generateFen(imageFile)

        } catch (e: Exception) {
            Log.e(TAG, "💥 Python gradio upload failed with exception", e)
            NetworkResult.Error("Python gradio error: ${e.message}")
        }
    }




}

/**
 * Sealed class representing network operation results
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val message: String) : NetworkResult<T>()
    data class Loading<T>(val message: String = "Loading...") : NetworkResult<T>()
}
