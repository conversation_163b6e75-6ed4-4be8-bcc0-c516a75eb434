package com.example.aicamera.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.aicamera.ui.theme.getDynamicColorScheme

/**
 * Home Screen - Chess.com inspired landing page with Material Design 3 styling
 * Features prominent chess gameplay options with integrated camera scanning
 */
@Composable
fun HomeScreen(
    onNavigateToCamera: () -> Unit,
    onNavigateToAnalysis: () -> Unit,
    onNavigateToSettings: () -> Unit
) {
    val dynamicColorScheme = getDynamicColorScheme()

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = dynamicColorScheme.background,
        bottomBar = {
            // Chess.com style bottom navigation
            NavigationBar(
                containerColor = dynamicColorScheme.surface,
                contentColor = dynamicColorScheme.onSurface
            ) {
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Home, contentDescription = "Home") },
                    label = { Text("Home") },
                    selected = true,
                    onClick = { }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.CameraAlt, contentDescription = "Scan") },
                    label = { Text("Scan") },
                    selected = false,
                    onClick = onNavigateToCamera
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Analytics, contentDescription = "Analysis") },
                    label = { Text("Analysis") },
                    selected = false,
                    onClick = onNavigateToAnalysis
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Settings, contentDescription = "Settings") },
                    label = { Text("Settings") },
                    selected = false,
                    onClick = onNavigateToSettings
                )
            }
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(dynamicColorScheme.background),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Hero Section - Chess.com inspired
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(20.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Psychology,
                            contentDescription = "Chess AI",
                            modifier = Modifier.size(48.dp),
                            tint = dynamicColorScheme.onPrimaryContainer
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            text = "♛ Fizzi Chess AI",
                            style = MaterialTheme.typography.headlineMedium.copy(
                                color = dynamicColorScheme.onPrimaryContainer,
                                fontWeight = FontWeight.Bold
                            )
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Scan, analyze, and play chess with AI-powered vision",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                color = dynamicColorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                            ),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // Quick Actions - Chess.com style
            item {
                Text(
                    text = "Quick Actions",
                    style = MaterialTheme.typography.titleLarge.copy(
                        color = dynamicColorScheme.onSurface,
                        fontWeight = FontWeight.SemiBold
                    ),
                    modifier = Modifier.padding(horizontal = 4.dp)
                )
            }

            // Action Cards Row - Chess.com inspired layout
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Scan Board Card
                    Card(
                        modifier = Modifier
                            .weight(1f)
                            .height(120.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = dynamicColorScheme.secondary
                        ),
                        shape = RoundedCornerShape(16.dp),
                        onClick = onNavigateToCamera
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.CameraAlt,
                                contentDescription = "Scan Board",
                                modifier = Modifier.size(32.dp),
                                tint = dynamicColorScheme.onSecondary
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Scan Board",
                                style = MaterialTheme.typography.labelLarge.copy(
                                    color = dynamicColorScheme.onSecondary,
                                    fontWeight = FontWeight.Medium
                                ),
                                textAlign = TextAlign.Center
                            )
                        }
                    }

                    // Analysis Card
                    Card(
                        modifier = Modifier
                            .weight(1f)
                            .height(120.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = dynamicColorScheme.tertiary
                        ),
                        shape = RoundedCornerShape(16.dp),
                        onClick = onNavigateToAnalysis
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Analytics,
                                contentDescription = "Analysis",
                                modifier = Modifier.size(32.dp),
                                tint = dynamicColorScheme.onTertiary
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Analysis",
                                style = MaterialTheme.typography.labelLarge.copy(
                                    color = dynamicColorScheme.onTertiary,
                                    fontWeight = FontWeight.Medium
                                ),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }

            // Features Section
            item {
                Text(
                    text = "Features",
                    style = MaterialTheme.typography.titleLarge.copy(
                        color = dynamicColorScheme.onSurface,
                        fontWeight = FontWeight.SemiBold
                    ),
                    modifier = Modifier.padding(horizontal = 4.dp)
                )
            }

            // Features List
            item {
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FeatureItem(
                        icon = Icons.Default.CameraAlt,
                        title = "AI Vision Scanning",
                        description = "Capture chess boards at 416x416 resolution for optimal AI processing",
                        colorScheme = dynamicColorScheme
                    )
                    FeatureItem(
                        icon = Icons.Default.Psychology,
                        title = "FEN Generation",
                        description = "Convert board images to FEN notation using Hugging Face API",
                        colorScheme = dynamicColorScheme
                    )
                    FeatureItem(
                        icon = Icons.Default.Cloud,
                        title = "Cloud Processing",
                        description = "Fast, reliable AI processing with zero first-user penalty",
                        colorScheme = dynamicColorScheme
                    )
                    FeatureItem(
                        icon = Icons.Default.Speed,
                        title = "Gradio Client Integration",
                        description = "Direct integration with Gradio API for seamless processing",
                        colorScheme = dynamicColorScheme
                    )
                }
            }
        }
    }
}

@Composable
private fun FeatureItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    description: String,
    colorScheme: ColorScheme
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = colorScheme.surface
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(24.dp),
                tint = colorScheme.primary
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleSmall.copy(
                        color = colorScheme.onSurface,
                        fontWeight = FontWeight.Medium
                    )
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                )
            }
        }
    }
}
