@app/src/main/java/com/example/aicamera/chess/StockfishManager.ktEapp/src/main/java/com/example/aicamera/chess/ChessAnalysisActivity.kt=app/src/main/java/com/example/aicamera/ComposeMainActivity.ktAapp/src/main/java/com/example/aicamera/chess/ChessBoardWebView.kt?app/src/main/java/com/example/aicamera/ui/screens/HomeScreen.kt7app/src/main/java/com/example/aicamera/CameraManager.kt8app/src/main/java/com/example/aicamera/ui/theme/Theme.kt;app/src/main/java/com/example/aicamera/chess/ChessModels.kt7app/src/main/java/com/example/aicamera/ui/theme/Type.ktCapp/src/main/java/com/example/aicamera/chess/ui/theme/ChessTheme.kt=app/src/main/java/com/example/aicamera/PythonGradioService.kt:app/src/main/java/com/example/aicamera/SettingsActivity.kt:app/src/main/java/com/example/aicamera/ChessApplication.ktAapp/src/main/java/com/example/aicamera/ui/screens/CameraScreen.kt8app/src/main/java/com/example/aicamera/ui/theme/Color.ktAapp/src/main/java/com/example/aicamera/chess/ChessUIComponents.kt3app/src/main/java/com/example/aicamera/ApiConfig.kt6app/src/main/java/com/example/aicamera/MainActivity.kt>app/src/main/java/com/example/aicamera/chess/ChessViewModel.kt8app/src/main/java/com/example/aicamera/NetworkManager.kt4app/src/main/java/com/example/aicamera/AiResponse.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       