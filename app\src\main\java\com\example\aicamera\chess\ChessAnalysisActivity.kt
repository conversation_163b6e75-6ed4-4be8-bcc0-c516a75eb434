package com.example.aicamera.chess

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aicamera.MainActivity
import com.example.aicamera.chess.ui.theme.ChessTheme
import kotlinx.coroutines.launch

class ChessAnalysisActivity : ComponentActivity() {

    companion object {
        private const val TAG = "ChessAnalysisActivity"
        const val EXTRA_SCANNED_FEN = "scanned_fen"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Get scanned FEN if provided
        val scannedFen = intent.getStringExtra(EXTRA_SCANNED_FEN)

        setContent {
            ChessTheme {
                ChessAnalysisScreen(
                    initialFen = scannedFen,
                    onNavigateToScanner = {
                        val intent = Intent(this@ChessAnalysisActivity, MainActivity::class.java)
                        intent.putExtra("open_scanner", true)
                        startActivity(intent)
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChessAnalysisScreen(
    initialFen: String? = null,
    onNavigateToScanner: () -> Unit,
    viewModel: ChessViewModel = viewModel()
) {
    val gameState by viewModel.gameState.collectAsState()
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    var showScanDialog by remember { mutableStateOf(false) }
    var showEditDialog by remember { mutableStateOf(false) }

    // Initialize with scanned FEN if provided
    LaunchedEffect(initialFen) {
        initialFen?.let { fen ->
            viewModel.loadPosition(fen)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding()
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Column {
                    Text(
                        text = "Chess Analysis",
                        style = MaterialTheme.typography.titleLarge
                    )
                    gameState.engineAnalysis?.let { analysis ->
                        Text(
                            text = "Eval: ${StockfishManager.getInstance().formatEvaluation(analysis.evaluation)}",
                            style = MaterialTheme.typography.bodySmall,
                            color = StockfishManager.getInstance().getEvaluationColor(analysis.evaluation)
                        )
                    }
                }
            },
            actions = {
                // Engine Toggle
                IconButton(
                    onClick = {
                        scope.launch {
                            if (gameState.isAnalyzing) {
                                viewModel.stopAnalysis()
                            } else {
                                viewModel.startAnalysis()
                            }
                        }
                    }
                ) {
                    Icon(
                        imageVector = if (gameState.isAnalyzing) Icons.Default.Pause else Icons.Default.PlayArrow,
                        contentDescription = if (gameState.isAnalyzing) "Pause Analysis" else "Start Analysis",
                        tint = if (gameState.isAnalyzing) Color(0xFF4CAF50) else MaterialTheme.colorScheme.onSurface
                    )
                }

                // Scan Board Button
                IconButton(
                    onClick = { showScanDialog = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.CameraAlt,
                        contentDescription = "Scan Board",
                        tint = Color(0xFF9C27B0)
                    )
                }

                // Edit Position
                IconButton(
                    onClick = { showEditDialog = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit Position"
                    )
                }

                // More Options
                IconButton(onClick = { /* Show menu */ }) {
                    Icon(
                        Icons.Default.MoreVert,
                        contentDescription = "More"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        )

        // Main Content
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Chess Board Section
            item {
                ChessBoardCard(
                    gameState = gameState,
                    onMoveSelected = { move: String ->
                        scope.launch {
                            viewModel.makeMove(move)
                        }
                    },
                    onSquareSelected = { square: String ->
                        viewModel.selectSquare(square)
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Engine Analysis Section
            item {
                EngineAnalysisCard(
                    analysis = gameState.engineAnalysis,
                    isAnalyzing = gameState.isAnalyzing,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Move History & Navigation
            if (gameState.moveHistory.isNotEmpty()) {
                item {
                    MoveHistoryCard(
                        moves = gameState.moveHistory,
                        onMoveSelected = { moveIndex: Int ->
                            viewModel.goToMove(moveIndex)
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            // Position Information
            item {
                PositionInfoCard(
                    gameState = gameState,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }

    // Scan Dialog
    if (showScanDialog) {
        ScanBoardDialog(
            onDismiss = { showScanDialog = false },
            onNavigateToScanner = {
                showScanDialog = false
                onNavigateToScanner()
            }
        )
    }

    // Edit Dialog
    if (showEditDialog) {
        EditPositionDialog(
            currentFen = gameState.currentFen,
            onDismiss = { showEditDialog = false },
            onPositionChanged = { fen ->
                viewModel.loadPosition(fen)
                showEditDialog = false
            }
        )
    }
}

@Composable
fun ScanBoardDialog(
    onDismiss: () -> Unit,
    onNavigateToScanner: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Scan Chess Board",
                        style = MaterialTheme.typography.titleLarge
                    )

                    IconButton(onClick = onDismiss) {
                        Icon(
                            Icons.Default.Close,
                            contentDescription = "Close"
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Icon
                Icon(
                    imageVector = Icons.Default.CameraAlt,
                    contentDescription = "Camera",
                    modifier = Modifier.size(64.dp),
                    tint = Color(0xFF9C27B0)
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "Use AI vision to analyze any chess position",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Scan Button
                Button(
                    onClick = onNavigateToScanner,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF9C27B0)
                    )
                ) {
                    Icon(Icons.Default.CameraAlt, "Scan")
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Open Camera Scanner")
                }
            }
        }
    }
}

@Composable
fun EditPositionDialog(
    currentFen: String,
    onDismiss: () -> Unit,
    onPositionChanged: (String) -> Unit
) {
    var fenText by remember { mutableStateOf(currentFen) }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Edit Position",
                        style = MaterialTheme.typography.titleLarge
                    )

                    IconButton(onClick = onDismiss) {
                        Icon(
                            Icons.Default.Close,
                            contentDescription = "Close"
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // FEN Input
                OutlinedTextField(
                    value = fenText,
                    onValueChange = { fenText = it },
                    label = { Text("FEN Notation") },
                    modifier = Modifier.fillMaxWidth(),
                    textStyle = MaterialTheme.typography.bodyMedium.copy(
                        fontFamily = FontFamily.Monospace
                    ),
                    maxLines = 3
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Quick Actions
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = {
                            fenText = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Reset")
                    }

                    OutlinedButton(
                        onClick = {
                            fenText = "8/8/8/8/8/8/8/8 w - - 0 1"
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Clear")
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Action Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Cancel")
                    }

                    Button(
                        onClick = { onPositionChanged(fenText) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Apply")
                    }
                }
            }
        }
    }
}
