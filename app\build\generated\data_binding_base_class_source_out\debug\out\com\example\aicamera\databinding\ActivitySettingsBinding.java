// Generated by view binder compiler. Do not edit!
package com.example.aicamera.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aicamera.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySettingsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Spinner apiProviderSpinner;

  @NonNull
  public final MaterialCardView huggingFaceSection;

  @NonNull
  public final MaterialCardView localServerSection;

  @NonNull
  public final Spinner modelSpinner;

  @NonNull
  public final Button saveButton;

  @NonNull
  public final Button testConnectionButton;

  @NonNull
  public final TextInputEditText tokenEditText;

  @NonNull
  public final Toolbar toolbar;

  private ActivitySettingsBinding(@NonNull ConstraintLayout rootView,
      @NonNull Spinner apiProviderSpinner, @NonNull MaterialCardView huggingFaceSection,
      @NonNull MaterialCardView localServerSection, @NonNull Spinner modelSpinner,
      @NonNull Button saveButton, @NonNull Button testConnectionButton,
      @NonNull TextInputEditText tokenEditText, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.apiProviderSpinner = apiProviderSpinner;
    this.huggingFaceSection = huggingFaceSection;
    this.localServerSection = localServerSection;
    this.modelSpinner = modelSpinner;
    this.saveButton = saveButton;
    this.testConnectionButton = testConnectionButton;
    this.tokenEditText = tokenEditText;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.apiProviderSpinner;
      Spinner apiProviderSpinner = ViewBindings.findChildViewById(rootView, id);
      if (apiProviderSpinner == null) {
        break missingId;
      }

      id = R.id.huggingFaceSection;
      MaterialCardView huggingFaceSection = ViewBindings.findChildViewById(rootView, id);
      if (huggingFaceSection == null) {
        break missingId;
      }

      id = R.id.localServerSection;
      MaterialCardView localServerSection = ViewBindings.findChildViewById(rootView, id);
      if (localServerSection == null) {
        break missingId;
      }

      id = R.id.modelSpinner;
      Spinner modelSpinner = ViewBindings.findChildViewById(rootView, id);
      if (modelSpinner == null) {
        break missingId;
      }

      id = R.id.saveButton;
      Button saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.testConnectionButton;
      Button testConnectionButton = ViewBindings.findChildViewById(rootView, id);
      if (testConnectionButton == null) {
        break missingId;
      }

      id = R.id.tokenEditText;
      TextInputEditText tokenEditText = ViewBindings.findChildViewById(rootView, id);
      if (tokenEditText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivitySettingsBinding((ConstraintLayout) rootView, apiProviderSpinner,
          huggingFaceSection, localServerSection, modelSpinner, saveButton, testConnectionButton,
          tokenEditText, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
