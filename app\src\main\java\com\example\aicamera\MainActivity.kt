package com.example.aicamera

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.aicamera.databinding.ActivityMainBinding
import com.example.aicamera.utils.GalleryManager
import kotlinx.coroutines.launch
import java.io.File

class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"

        // Target image size for optimal API performance
        const val TARGET_IMAGE_SIZE = 416

        // Get required permissions based on Android version
        private fun getRequiredPermissions(): Array<String> {
            val permissions = mutableListOf(
                Manifest.permission.CAMERA,
                Manifest.permission.INTERNET
            )

            // Add storage permission for Android 9 and below
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }

            return permissions.toTypedArray()
        }
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var cameraManager: CameraManager
    private lateinit var networkManager: NetworkManager
    private var capturedImageFile: File? = null

    // Permission launcher
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        var allGranted = true
        permissions.entries.forEach {
            if (!it.value) allGranted = false
        }

        if (allGranted) {
            startCamera()
        } else {
            showError("Camera permission is required to use this app")
        }
    }

    // Removed crop activity launcher - direct processing

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Load settings from preferences
        SettingsActivity.loadSettingsOnStartup(this)

        // Initialize managers
        cameraManager = CameraManager(this)
        networkManager = NetworkManager()

        // Set up UI listeners
        setupUI()

        // Request permissions and start camera
        val requiredPermissions = getRequiredPermissions()
        if (allPermissionsGranted(requiredPermissions)) {
            startCamera()
        } else {
            requestPermissionLauncher.launch(requiredPermissions)
        }
    }

    private fun setupUI() {
        binding.captureButton.setOnClickListener {
            capturePhoto()
        }

        binding.sendToAiButton.setOnClickListener {
            capturedImageFile?.let { file ->
                sendImageToAI(file)
            }
        }
    }

    private fun startCamera() {
        updateStatus("Starting camera...")

        cameraManager.startCamera(
            previewView = binding.previewView,
            lifecycleOwner = this,
            onCameraReady = {
                updateStatus("Camera ready - tap to capture")
                binding.captureButton.isEnabled = true
            },
            onError = { error ->
                showError("Camera error: $error")
            }
        )
    }

    private fun capturePhoto() {
        binding.captureButton.isEnabled = false
        updateStatus("Capturing photo...")

        cameraManager.capturePhoto(
            onImageCaptured = { file ->
                capturedImageFile = file
                updateStatus("Photo captured - ready for AI processing")
                showToast("Photo captured: ${file.name}")

                // Enable AI processing button
                binding.sendToAiButton.isEnabled = true
                binding.captureButton.isEnabled = true
            },
            onError = { error ->
                showError("Capture failed: $error")
                binding.captureButton.isEnabled = true
            },
            onGallerySaved = { uri ->
                Log.d(TAG, "✅ Image saved to gallery: $uri")
                showToast("Saved to gallery: Chess Positions")
            },
            onGalleryError = { error ->
                Log.w(TAG, "⚠️ Gallery save failed: $error")
                // Don't show error to user as this is not critical
            }
        )
    }

    // Removed crop activity - direct processing

    private fun sendImageToAI(imageFile: File) {
        binding.sendToAiButton.isEnabled = false
        showProgressBar(true)

        // Update status based on current provider
        val statusMessage = when (ApiConfig.currentProvider) {
            ApiConfig.ApiProvider.HUGGING_FACE -> getString(R.string.processing_with_cloud)
            ApiConfig.ApiProvider.LOCAL_SERVER -> getString(R.string.processing_with_local)
        }
        updateStatus(statusMessage)

        lifecycleScope.launch {
            when (val result = networkManager.uploadImage(imageFile)) {
                is NetworkResult.Success -> {
                    val response = result.data
                    if (response.success) {
                        updateStatus("AI processing complete (${response.provider})")
                        displayAIResponse(response.result ?: "No result provided")

                        // Show confidence if available
                        val confidenceText = response.confidence?.let {
                            " (Confidence: ${(it * 100).toInt()}%)"
                        } ?: ""
                        showToast("Image processed successfully$confidenceText")
                    } else {
                        showError("AI processing failed: ${response.error}")
                    }
                }
                is NetworkResult.Error -> {
                    showError("Upload failed: ${result.message}")
                }
                is NetworkResult.Loading -> {
                    updateStatus(result.message)
                }
            }

            showProgressBar(false)
            binding.sendToAiButton.isEnabled = true
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_settings -> {
                val intent = Intent(this, SettingsActivity::class.java)
                startActivity(intent)
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun displayAIResponse(response: String) {
        binding.aiResponseText.text = response
    }

    private fun updateStatus(message: String) {
        binding.statusText.text = message
        Log.d(TAG, "Status: $message")
    }

    private fun showProgressBar(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
    }

    private fun showError(message: String) {
        updateStatus("Error: $message")
        showToast(message)
        Log.e(TAG, message)
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun allPermissionsGranted(permissions: Array<String>) = permissions.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraManager.shutdown()
    }
}
