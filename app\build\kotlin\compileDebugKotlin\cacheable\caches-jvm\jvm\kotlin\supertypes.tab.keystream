*com.example.aicamera.ApiConfig.ApiProvider%com.example.aicamera.ChessApplication!com.example.aicamera.MainActivity*com.example.aicamera.NetworkResult.Success(com.example.aicamera.NetworkResult.Error*com.example.aicamera.NetworkResult.Loading%com.example.aicamera.SettingsActivity0com.example.aicamera.chess.ChessAnalysisActivity#com.example.aicamera.chess.GameMode$com.example.aicamera.chess.PieceType%com.example.aicamera.chess.PieceColor+com.example.aicamera.chess.BoardOrientation)com.example.aicamera.chess.GameResultType)com.example.aicamera.chess.ChessViewModel4com.example.aicamera.databinding.ActivityMainBinding8com.example.aicamera.databinding.ActivitySettingsBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          