package com.example.aicamera.ui.screens

import android.Manifest
import android.content.Intent
import android.provider.MediaStore
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.aicamera.CameraManager
import com.example.aicamera.NetworkManager
import com.example.aicamera.NetworkResult
import com.example.aicamera.integration.ExternalAppManager
import com.example.aicamera.preferences.ExternalAppPreferences
import com.example.aicamera.preferences.SupportedChessApps
import com.example.aicamera.ui.theme.getDynamicColorScheme
import kotlinx.coroutines.launch
import java.io.File

/**
 * Camera Screen - Chess.com inspired camera interface for chess board scanning
 * Features 416x416 preview optimized for AI processing with enhanced UI
 * Integrates with existing Hugging Face API functionality
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CameraScreen(
    onBackPressed: () -> Unit,
    onFENGenerated: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val dynamicColorScheme = getDynamicColorScheme()
    val scope = rememberCoroutineScope()

    var isProcessing by remember { mutableStateOf(false) }
    var capturedFEN by remember { mutableStateOf<String?>(null) }
    var hasCameraPermission by remember { mutableStateOf(false) }
    var isFlashOn by remember { mutableStateOf(false) }
    var apiStatus by remember { mutableStateOf("Ready for capture") }
    var showError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    var capturedImageFile by remember { mutableStateOf<File?>(null) }

    // Initialize managers
    val cameraManager = remember { CameraManager(context) }
    val networkManager = remember { NetworkManager() }
    val appPreferences = remember { ExternalAppPreferences(context) }
    val appManager = remember { ExternalAppManager(context) }

    val previewView = remember { PreviewView(context) }
    var camera: Camera? by remember { mutableStateOf(null) }

    // Function to complete AI FEN and auto-launch external app
    fun completeAndLaunchFEN(pieceFen: String) {
        Log.d("CameraScreen", "📝 Original FEN from AI: '$pieceFen'")

        // Always complete the FEN with standard game state
        val fenParts = pieceFen.trim().split(" ")
        val completeFEN = if (fenParts.size >= 6) {
            // Already complete FEN
            Log.d("CameraScreen", "✅ FEN already complete (${fenParts.size} parts)")
            pieceFen
        } else {
            // Add missing parts: active color, castling, en passant, halfmove, fullmove
            val piecePosition = fenParts[0]
            val completed = "$piecePosition w KQkq - 0 1"
            Log.d("CameraScreen", "🔧 Completed FEN: '$completed' (was ${fenParts.size} parts, now 6)")
            completed
        }

        Log.d("CameraScreen", "🚀 Auto-launching external app with FEN: '$completeFEN'")

        // Get preferred app and launch immediately
        val selectedAppId = appPreferences.currentApp
        val selectedApp = SupportedChessApps.getAppById(selectedAppId)
        val success = appManager.launchChessApp(selectedAppId, completeFEN)

        if (success) {
            Log.d("CameraScreen", "✅ Successfully launched external app: $selectedAppId")
            apiStatus = "Opened in ${selectedApp?.name ?: "Chess App"}"
        } else {
            Log.w("CameraScreen", "⚠️ Failed to launch external app, falling back to navigation")
            // Still call the original callback as fallback
            onFENGenerated(completeFEN)
        }
    }

    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasCameraPermission = isGranted
    }

    // Gallery launcher
    val galleryLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        // Handle gallery result if needed
        // For now, just show a message
        apiStatus = "Gallery opened (feature coming soon)"
    }

    LaunchedEffect(Unit) {
        permissionLauncher.launch(Manifest.permission.CAMERA)
    }

    LaunchedEffect(hasCameraPermission) {
        if (hasCameraPermission) {
            cameraManager.startCamera(
                previewView = previewView,
                lifecycleOwner = lifecycleOwner,
                onCameraReady = {
                    apiStatus = "Camera ready - tap to capture"
                    // Initialize flash state after camera is ready
                    isFlashOn = false
                },
                onError = { error ->
                    errorMessage = "Camera error: $error"
                    showError = true
                }
            )
        }
    }

    if (!hasCameraPermission) {
        // Permission request UI
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(dynamicColorScheme.background),
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier.padding(32.dp),
                colors = CardDefaults.cardColors(
                    containerColor = dynamicColorScheme.surface
                ),
                shape = RoundedCornerShape(24.dp)
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.CameraAlt,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = dynamicColorScheme.primary
                    )
                    Text(
                        text = "Camera Permission Required",
                        style = MaterialTheme.typography.headlineSmall,
                        color = dynamicColorScheme.onSurface
                    )
                    Text(
                        text = "Please grant camera permission to scan chess boards",
                        style = MaterialTheme.typography.bodyMedium,
                        color = dynamicColorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Button(
                        onClick = { permissionLauncher.launch(Manifest.permission.CAMERA) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = dynamicColorScheme.primary
                        )
                    ) {
                        Text("Grant Permission")
                    }
                }
            }
        }
        return
    }

    // Chess.com inspired camera layout with integrated preview and controls
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(dynamicColorScheme.background)
    ) {
        // Top App Bar - Chess.com style
        TopAppBar(
            title = {
                Text(
                    text = "Scan Chess Board",
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.SemiBold
                    )
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            actions = {
                IconButton(
                    onClick = {
                        try {
                            isFlashOn = !isFlashOn
                            cameraManager.toggleFlash(isFlashOn)
                        } catch (e: Exception) {
                            Log.e("CameraScreen", "Flash toggle failed", e)
                            errorMessage = "Flash control failed: ${e.message}"
                            showError = true
                            isFlashOn = false
                        }
                    },
                    enabled = hasCameraPermission && cameraManager.hasFlash()
                ) {
                    Icon(
                        imageVector = if (isFlashOn) Icons.Default.FlashOn else Icons.Default.FlashOff,
                        contentDescription = "Flash",
                        tint = if (isFlashOn) dynamicColorScheme.primary else dynamicColorScheme.onSurface
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = dynamicColorScheme.surface,
                titleContentColor = dynamicColorScheme.onSurface
            )
        )

        // Main Content Area
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Camera Preview Card - 416x416 optimized
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f), // Square aspect ratio for chess board
                colors = CardDefaults.cardColors(
                    containerColor = dynamicColorScheme.surface
                ),
                shape = RoundedCornerShape(20.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // Camera Preview
                    AndroidView(
                        factory = { previewView },
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(20.dp))
                    )


                }
            }

            // FEN Output Card - Chess.com style
            if (capturedFEN != null) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Generated FEN",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold,
                                color = dynamicColorScheme.onPrimaryContainer
                            )
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = capturedFEN!!,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                                color = dynamicColorScheme.onPrimaryContainer
                            ),
                            modifier = Modifier
                                .background(
                                    dynamicColorScheme.surface.copy(alpha = 0.3f),
                                    RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp)
                        )
                    }
                }
            }

            // API Status Card
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = dynamicColorScheme.surface
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (isProcessing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = dynamicColorScheme.primary
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                            tint = dynamicColorScheme.primary
                        )
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = apiStatus,
                        style = MaterialTheme.typography.bodyMedium,
                        color = dynamicColorScheme.onSurface
                    )
                }
            }

            // Action Buttons - Chess.com style
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Gallery Button
                OutlinedButton(
                    onClick = {
                        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                        galleryLauncher.launch(intent)
                    },
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.PhotoLibrary,
                        contentDescription = "Gallery",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Gallery")
                }

                // Capture Button
                Button(
                    onClick = {
                        if (!isProcessing) {
                            isProcessing = true
                            apiStatus = "Capturing photo..."

                            cameraManager.capturePhoto(
                                onImageCaptured = { file ->
                                    capturedImageFile = file
                                    apiStatus = "Processing with AI..."

                                    // Send to API using existing NetworkManager
                                    scope.launch {
                                        when (val result = networkManager.uploadImage(file)) {
                                            is NetworkResult.Success -> {
                                                val response = result.data
                                                if (response.success) {
                                                    apiStatus = "FEN Generated Successfully"
                                                    capturedFEN = response.result
                                                    // Auto-launch external app immediately
                                                    response.result?.let { completeAndLaunchFEN(it) }
                                                } else {
                                                    apiStatus = "AI Processing Failed"
                                                    errorMessage = response.error ?: "Unknown error"
                                                    showError = true
                                                }
                                            }
                                            is NetworkResult.Error -> {
                                                apiStatus = "Upload Failed"
                                                errorMessage = result.message
                                                showError = true
                                            }
                                            is NetworkResult.Loading -> {
                                                apiStatus = result.message
                                            }
                                        }
                                        isProcessing = false
                                    }
                                },
                                onError = { error ->
                                    isProcessing = false
                                    apiStatus = "Capture Failed"
                                    errorMessage = "Camera error: $error"
                                    showError = true
                                },
                                onGallerySaved = { uri ->
                                    Log.d("CameraScreen", "✅ Image saved to gallery: $uri")
                                    // Could show a subtle notification here
                                },
                                onGalleryError = { error ->
                                    Log.w("CameraScreen", "⚠️ Gallery save failed: $error")
                                    // Gallery save failure is not critical, don't show error
                                }
                            )
                        }
                    },
                    modifier = Modifier.weight(2f),
                    enabled = !isProcessing,
                    shape = RoundedCornerShape(12.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = dynamicColorScheme.primary
                    )
                ) {
                    if (isProcessing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(18.dp),
                            color = dynamicColorScheme.onPrimary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Processing...")
                    } else {
                        Icon(
                            imageVector = Icons.Default.CameraAlt,
                            contentDescription = "Capture",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Capture Board")
                    }
                }
            }
        }

        // Error Dialog
        if (showError) {
            AlertDialog(
                onDismissRequest = { showError = false },
                title = {
                    Text(
                        text = "Processing Error",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                text = {
                    Text(
                        text = errorMessage,
                        style = MaterialTheme.typography.bodyMedium
                    )
                },
                confirmButton = {
                    TextButton(
                        onClick = { showError = false }
                    ) {
                        Text("OK")
                    }
                },
                shape = RoundedCornerShape(16.dp)
            )
        }
    }
}
