package com.example.aicamera.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.aicamera.preferences.ExternalAppPreferences
import com.example.aicamera.integration.ExternalAppManager
import com.example.aicamera.preferences.SupportedChessApps
import com.example.aicamera.ui.theme.getDynamicColorScheme

/**
 * FEN Result Screen - Shows AI-detected chess position and launches external app
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChessBoardScreen(
    fen: String?,
    onBackPressed: () -> Unit,
    onAnalyzePosition: (String) -> Unit = {},
    onNavigateToAppSelection: () -> Unit = {}
) {
    val context = LocalContext.current
    val dynamicColorScheme = getDynamicColorScheme()
    val appPreferences = remember { ExternalAppPreferences(context) }
    val appManager = remember { ExternalAppManager(context) }

    // Auto-return to camera after brief display
    LaunchedEffect(fen) {
        if (fen != null) {
            // Show success message briefly, then return to camera
            kotlinx.coroutines.delay(2000) // 2 seconds
            onBackPressed()
        }
    }

    // Function to check if black king is in wrong position (last 4 rows)
    fun shouldFlipBoard(fenPosition: String): Boolean {
        val ranks = fenPosition.split("/")
        // Check last 4 ranks (indices 4-7) for black king 'k'
        for (i in 4..7) {
            if (i < ranks.size && ranks[i].contains('k')) {
                return true // Black king found in last 4 rows, need to flip
            }
        }
        return false // Black king in first 4 rows, keep as is
    }

    // Function to flip the board (reverse ranks AND files, keep piece colors)
    fun flipFENBoard(fenPosition: String): String {
        val ranks = fenPosition.split("/")

        // Reverse ranks (vertical flip) AND reverse each rank (horizontal flip)
        val flippedRanks = ranks.reversed().map { rank ->
            // Convert rank to individual squares, reverse them, then back to FEN format
            val squares = mutableListOf<String>()

            // Parse the rank into individual squares
            for (char in rank) {
                if (char.isDigit()) {
                    // Add empty squares
                    repeat(char.digitToInt()) {
                        squares.add("1")
                    }
                } else {
                    // Add piece
                    squares.add(char.toString())
                }
            }

            // Reverse the squares (horizontal flip)
            squares.reverse()

            // Convert back to FEN format (compress consecutive empty squares)
            val result = StringBuilder()
            var emptyCount = 0

            for (square in squares) {
                if (square == "1") {
                    emptyCount++
                } else {
                    if (emptyCount > 0) {
                        result.append(emptyCount)
                        emptyCount = 0
                    }
                    result.append(square)
                }
            }

            // Add any remaining empty squares
            if (emptyCount > 0) {
                result.append(emptyCount)
            }

            result.toString()
        }

        return flippedRanks.joinToString("/")
    }

    // Function to convert AI piece-only FEN to complete FEN with board flipping
    fun completeAIFen(pieceFen: String): String {
        val fenParts = pieceFen.trim().split(" ")
        val originalPiecePosition = fenParts[0]

        // Check if board needs flipping based on black king position
        val processedPiecePosition = if (shouldFlipBoard(originalPiecePosition)) {
            flipFENBoard(originalPiecePosition)
        } else {
            originalPiecePosition
        }

        return if (fenParts.size >= 6) {
            // Already complete FEN, but use processed position
            val otherParts = fenParts.drop(1).joinToString(" ")
            "$processedPiecePosition $otherParts"
        } else {
            // Add missing parts: active color, castling, en passant, halfmove, fullmove
            "$processedPiecePosition w KQkq - 0 1"
        }
    }

    val completeFEN = remember(fen) {
        fen?.let { completeAIFen(it) } ?: "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = dynamicColorScheme.background,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Chess Position",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.SemiBold
                        )
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = dynamicColorScheme.surface,
                    titleContentColor = dynamicColorScheme.onSurface
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Success Header
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "Success",
                            modifier = Modifier.size(48.dp),
                            tint = dynamicColorScheme.primary
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            text = "Position Detected!",
                            style = MaterialTheme.typography.headlineSmall.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = dynamicColorScheme.onPrimaryContainer,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = if (fen != null) {
                                val selectedApp = SupportedChessApps.getAppById(appPreferences.currentApp)
                                "Opening in ${selectedApp?.name ?: "Chess App"}..."
                            } else "Using default starting position",
                            style = MaterialTheme.typography.bodyMedium,
                            color = dynamicColorScheme.onPrimaryContainer,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // FEN Display
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.surface
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Chess Position (FEN)",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = dynamicColorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.height(12.dp))

                        Text(
                            text = completeFEN,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            ),
                            color = dynamicColorScheme.onSurfaceVariant,
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    dynamicColorScheme.surfaceVariant.copy(alpha = 0.3f),
                                    RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp)
                        )
                    }
                }
            }

            // External Chess App Integration
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(20.dp)
                    ) {
                        Text(
                            text = "Open in Chess App",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = dynamicColorScheme.onPrimaryContainer,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        // Primary app button (user's preferred app)
                        val selectedApp = SupportedChessApps.getAppById(appPreferences.currentApp)
                        Button(
                            onClick = {
                                appManager.launchChessApp(appPreferences.currentApp, completeFEN)
                            },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = dynamicColorScheme.primary
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            val icon = when (selectedApp?.icon) {
                                "analytics" -> Icons.Default.Analytics
                                "sports_esports" -> Icons.Default.SportsEsports
                                "psychology" -> Icons.Default.Psychology
                                "memory" -> Icons.Default.Memory
                                "apps" -> Icons.Default.Apps
                                else -> Icons.Default.Analytics
                            }

                            Icon(
                                imageVector = icon,
                                contentDescription = selectedApp?.name,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = "Open in ${selectedApp?.name ?: "Chess App"}",
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    fontWeight = FontWeight.Medium
                                )
                            )
                        }

                        Spacer(modifier = Modifier.height(12.dp))

                        // Alternative options
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            OutlinedButton(
                                onClick = {
                                    appManager.launchChessApp("generic", completeFEN)
                                },
                                modifier = Modifier.weight(1f)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Share,
                                    contentDescription = "Other Apps",
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("Other", style = MaterialTheme.typography.bodySmall)
                            }

                            OutlinedButton(
                                onClick = {
                                    val clipboard = context.getSystemService(android.content.Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                                    val clip = android.content.ClipData.newPlainText("Chess FEN", completeFEN)
                                    clipboard.setPrimaryClip(clip)
                                },
                                modifier = Modifier.weight(1f)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.ContentCopy,
                                    contentDescription = "Copy FEN",
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("Copy", style = MaterialTheme.typography.bodySmall)
                            }
                        }
                    }
                }
            }

            // Settings
            item {
                OutlinedButton(
                    onClick = onNavigateToAppSelection,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "Settings",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Change Preferred Chess App")
                }
            }
        }
    }
}
