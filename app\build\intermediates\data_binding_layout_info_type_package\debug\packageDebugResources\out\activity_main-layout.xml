<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.aicamera" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="51"/></Target><Target id="@+id/previewView" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="51"/></Target><Target id="@+id/controlsContainer" view="LinearLayout"><Expressions/><location startLine="19" startOffset="4" endLine="87" endOffset="18"/></Target><Target id="@+id/captureButton" view="Button"><Expressions/><location startLine="31" startOffset="8" endLine="37" endOffset="37"/></Target><Target id="@+id/sendToAiButton" view="Button"><Expressions/><location startLine="40" startOffset="8" endLine="47" endOffset="37"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="50" startOffset="8" endLine="58" endOffset="40"/></Target><Target id="@+id/aiResponseText" view="TextView"><Expressions/><location startLine="76" startOffset="12" endLine="84" endOffset="49"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="90" startOffset="4" endLine="98" endOffset="61"/></Target></Targets></Layout>