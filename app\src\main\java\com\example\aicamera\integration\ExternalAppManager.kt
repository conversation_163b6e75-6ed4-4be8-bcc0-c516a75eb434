package com.example.aicamera.integration

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.util.Log
import com.example.aicamera.preferences.ExternalChessApp
import com.example.aicamera.preferences.SupportedChessApps

/**
 * External Chess App Integration Manager
 * Handles launching external chess apps with FEN positions
 */
class ExternalAppManager(private val context: Context) {

    companion object {
        private const val TAG = "ExternalAppManager"
    }

    /**
     * Launch external chess app with FEN position
     */
    fun launchChessApp(appId: String, fen: String): Boolean {
        return when (appId) {
            "chessis" -> launchChessis(fen)
            "chess_com" -> launchChessCom(fen)
            "lichess" -> launchLichess(fen)
            "droidfish" -> launchDroidFish(fen)
            "generic" -> launchGenericChessApp(fen)
            else -> {
                Log.w(TAG, "Unknown app ID: $appId")
                launchGenericChessApp(fen)
            }
        }
    }

    /**
     * Check if external app is installed
     */
    fun isAppInstalled(packageName: String): Boolean {
        if (packageName.isEmpty()) return true // Generic option always available

        return try {
            context.packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * Get list of installed supported chess apps
     */
    fun getInstalledApps(): List<ExternalChessApp> {
        return SupportedChessApps.getAllApps().filter { app ->
            app.packageName.isEmpty() || isAppInstalled(app.packageName)
        }
    }

    /**
     * Launch Chessis app with FEN
     */
    private fun launchChessis(fen: String): Boolean {
        return try {
            // Try direct Board Editor launch with clean FEN
            val intent = Intent().apply {
                action = Intent.ACTION_MAIN
                setClassName("com.chessis.analysis", "com.chessis.analysis.BoardEditorActivity")
                putExtra("fen", fen) // Clean FEN without prefix
                putExtra("mode", "editor")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            Log.d(TAG, "✅ Launched Chessis Board Editor with clean FEN: $fen")
            true
        } catch (e: Exception) {
            Log.d(TAG, "Direct launch failed, trying generic Chessis launch")
            launchAppWithFEN("com.chessis.analysis", fen)
        }
    }

    /**
     * Launch Chess.com app with FEN
     */
    private fun launchChessCom(fen: String): Boolean {
        return launchAppWithFEN("com.chess", fen)
    }

    /**
     * Launch Lichess app with FEN
     */
    private fun launchLichess(fen: String): Boolean {
        return launchAppWithFEN("org.lichess.mobileapp", fen)
    }

    /**
     * Launch DroidFish app with FEN
     */
    private fun launchDroidFish(fen: String): Boolean {
        return launchAppWithFEN("org.petero.droidfish", fen)
    }

    /**
     * Launch generic chess app chooser
     */
    private fun launchGenericChessApp(fen: String): Boolean {
        return try {
            val intent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, fen) // Clean FEN without prefix
                putExtra(Intent.EXTRA_SUBJECT, "Chess Position from AI Camera")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            val chooser = Intent.createChooser(intent, "Open with Chess App")
            chooser.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(chooser)
            Log.d(TAG, "✅ Launched app chooser with clean FEN: $fen")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to launch app chooser", e)
            copyFenToClipboard(fen)
        }
    }

    /**
     * Generic app launch with FEN
     */
    private fun launchAppWithFEN(packageName: String, fen: String): Boolean {
        return try {
            val intent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, fen) // Clean FEN without prefix
                putExtra(Intent.EXTRA_SUBJECT, "Chess Position")
                setPackage(packageName)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            Log.d(TAG, "✅ Launched $packageName with clean FEN: $fen")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to launch $packageName", e)
            launchGenericChessApp(fen)
        }
    }

    /**
     * Copy FEN to clipboard as fallback
     */
    private fun copyFenToClipboard(fen: String): Boolean {
        return try {
            val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("Chess FEN", fen)
            clipboard.setPrimaryClip(clip)
            Log.d(TAG, "✅ Copied FEN to clipboard")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to copy FEN to clipboard", e)
            false
        }
    }
}
