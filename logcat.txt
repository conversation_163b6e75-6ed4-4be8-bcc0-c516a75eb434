--------- beginning of main
--------- beginning of system
---------------------------- PROCESS STARTED (19161) for package com.example.aicamera ----------------------------
2025-05-30 14:11:58.914 19161-19161 ziparchive              com.example.aicamera                 W  Unable to open '/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.dm': No such file or directory
2025-05-30 14:11:58.914 19161-19161 ziparchive              com.example.aicamera                 W  Unable to open '/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.dm': No such file or directory
2025-05-30 14:12:00.107 19161-19161 nativeloader            com.example.aicamera                 D  Configuring clns-4 for other apk /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/lib/arm64:/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.example.aicamera
2025-05-30 14:12:00.144 19161-19161 GraphicsEnvironment     com.example.aicamera                 V  ANGLE Developer option for 'com.example.aicamera' set to: 'default'
2025-05-30 14:12:00.144 19161-19161 GraphicsEnvironment     com.example.aicamera                 V  Neither updatable production driver nor prerelease driver is supported.
2025-05-30 14:12:00.150 19161-19161 NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 14:12:00.152 19161-19161 NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 14:12:00.177 19161-19161 libc                    com.example.aicamera                 W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-30 14:12:00.460 19161-19238 PythonGradioService     com.example.aicamera                 D  🚀 Pre-initializing Python environment in background...
2025-05-30 14:12:00.520 19161-19161 xample.aicamer          com.example.aicamera                 E  Invalid ID 0x00000000.
2025-05-30 14:12:00.624 19161-19238 nativeloader            com.example.aicamera                 D  Load /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a/libcrypto_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!classes8.dex): ok
2025-05-30 14:12:00.636 19161-19238 nativeloader            com.example.aicamera                 D  Load /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a/libssl_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!classes8.dex): ok
2025-05-30 14:12:00.643 19161-19238 nativeloader            com.example.aicamera                 D  Load /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a/libsqlite3_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!classes8.dex): ok
2025-05-30 14:12:00.647 19161-19238 nativeloader            com.example.aicamera                 D  Load /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a/libcrypto_python.so using ns clns-4 from class loader (caller=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!classes8.dex): ok
2025-05-30 14:12:00.650 19161-19238 nativeloader            com.example.aicamera                 D  Load /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a/libssl_python.so using ns clns-4 from class loader (caller=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!classes8.dex): ok
2025-05-30 14:12:00.653 19161-19238 nativeloader            com.example.aicamera                 D  Load /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a/libsqlite3_python.so using ns clns-4 from class loader (caller=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!classes8.dex): ok
2025-05-30 14:12:00.685 19161-19238 nativeloader            com.example.aicamera                 D  Load /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a/libpython3.11.so using ns clns-4 from class loader (caller=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!classes8.dex): ok
2025-05-30 14:12:00.690 19161-19238 nativeloader            com.example.aicamera                 D  Load /data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!/lib/arm64-v8a/libchaquopy_java.so using ns clns-4 from class loader (caller=/data/app/~~WycFop4sHUogKtS2kvplVg==/com.example.aicamera-2f9ZDOHyGGnnZlFIiYCSuA==/base.apk!classes8.dex): ok
2025-05-30 14:12:00.772 19161-19161 Choreographer           com.example.aicamera                 I  Skipped 34 frames!  The application may be doing too much work on its main thread.
2025-05-30 14:12:00.777 19161-19161 XDR::VRT                com.example.aicamera                 I  sc is not valid!
2025-05-30 14:12:00.813 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499130): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/zlib.cpython-311.so" dev="dm-10" ino=392631 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:01.069 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499131): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/java/chaquopy.so" dev="dm-10" ino=392609 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:01.089 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499132): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_ctypes.cpython-311.so" dev="dm-10" ino=392588 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:01.101 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499133): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_struct.cpython-311.so" dev="dm-10" ino=392585 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:01.185 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499134): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_bz2.cpython-311.so" dev="dm-10" ino=392584 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:01.197 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499135): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_lzma.cpython-311.so" dev="dm-10" ino=392603 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:01.337 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499136): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/math.cpython-311.so" dev="dm-10" ino=392629 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:01.517 19161-19161 xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateMap.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-30 14:12:01.517 19161-19161 xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateMap.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:12:01.519 19161-19161 xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateMap.removeIf$runtime_release(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:12:02.289 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499143): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/fcntl.cpython-311.so" dev="dm-10" ino=392659 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:02.295 19161-19161 xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:12:02.296 19161-19161 xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:12:02.296 19161-19161 xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:12:02.309 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499144): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_posixsubprocess.cpython-311.so" dev="dm-10" ino=392662 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:02.325 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499145): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/select.cpython-311.so" dev="dm-10" ino=392636 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:02.400 19161-19238 PythonGradioService     com.example.aicamera                 D  ✅ Python environment pre-started
2025-05-30 14:12:02.400 19161-19238 PythonGradioService     com.example.aicamera                 D  🔗 Python instance obtained
2025-05-30 14:12:02.429 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499146): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_json.cpython-311.so" dev="dm-10" ino=392667 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:02.691 19161-19161 BufferQueueConsumer     com.example.aicamera                 I  [](id:4ad900000000,api:0,p:-1,c:19161) connect: controlledByApp=false
2025-05-30 14:12:02.695 19161-19161 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0] constructor()
2025-05-30 14:12:02.720 19161-19230 hw-ProcessState         com.example.aicamera                 D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-30 14:12:02.766 19161-19230 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:4ad900000000,api:1,p:19161,c:19161) connect: api=1 producerControlledByApp=true
2025-05-30 14:12:02.778 19161-19253 ion                     com.example.aicamera                 E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-30 14:12:03.325 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499156): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_csv.cpython-311.so" dev="dm-10" ino=392709 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:03.477 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499158): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_multibytecodec.cpython-311.so" dev="dm-10" ino=392710 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:03.700 19161-19161 Compatibil...geReporter com.example.aicamera                 D  Compat change id reported: 171228096; UID 10743; state: ENABLED
2025-05-30 14:12:03.818 19161-19168 xample.aicamer          com.example.aicamera                 I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-30 14:12:03.933 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499159): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_contextvars.cpython-311.so" dev="dm-10" ino=392711 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:03.953 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499160): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_asyncio.cpython-311.so" dev="dm-10" ino=392718 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:05.091 19161-19230 OpenGLRenderer          com.example.aicamera                 E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-30 14:12:05.092 19161-19230 OpenGLRenderer          com.example.aicamera                 E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-30 14:12:05.116 19161-19181 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=4900ms; Flags=1, FrameTimelineVsyncId=31556473, IntendedVsync=579304850361121, Vsync=579305417027799, InputEventId=0, HandleInputStart=579305431243375, AnimationStart=579305431267682, PerformTraversalsStart=579305432507759, DrawStart=579309546895990, FrameDeadline=579304870361121, FrameInterval=579305430780375, FrameStartTime=16666667, SyncQueued=579309692122221, SyncStart=579309697928067, IssueDrawCommandsStart=579309698937836, SwapBuffers=579309749176067, FrameCompleted=579309756643990, DequeueBufferDuration=0, QueueBufferDuration=1973846, GpuCompleted=579309756643990, SwapBuffersCompleted=579309754558221, DisplayPresentTime=1898757280, 
2025-05-30 14:12:05.213 19161-19161 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6499162): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/resource.cpython-311.so" dev="dm-10" ino=392719 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c231,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:12:05.247 19161-19238 python.stderr           com.example.aicamera                 W  /data/data/com.example.aicamera/files/chaquopy/AssetFinder/requirements/websockets/legacy/__init__.py:6: DeprecationWarning: websockets.legacy is deprecated; see https://websockets.readthedocs.io/en/stable/howto/upgrade.html for upgrade instructions
2025-05-30 14:12:05.429 19161-19238 PythonGradioService     com.example.aicamera                 D  📦 chess_fen_client module imported successfully
2025-05-30 14:12:05.479 19161-19161 ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@e7e7e67[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-30 14:12:05.479 19161-19161 ImeFocusController      com.example.aicamera                 V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-30 14:12:05.480 19161-19161 ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@e7e7e67[ComposeMainActivity], mServedView=null
2025-05-30 14:12:05.480 19161-19161 ImeFocusController      com.example.aicamera                 V  checkFocus: view=null next=DecorView@e7e7e67[ComposeMainActivity] force=true package=<none>
2025-05-30 14:12:06.452 19161-19270 ProfileInstaller        com.example.aicamera                 D  Installing profile for com.example.aicamera
2025-05-30 14:12:07.152 19161-19238 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 14:12:10.518 19161-19169 xample.aicamer          com.example.aicamera                 I  Background concurrent copying GC freed 80KB AllocSpace bytes, 0(0B) LOS objects, 25% free, 5855KB/7903KB, paused 242us,117us total 106.970ms
2025-05-30 14:12:11.219 19161-19238 PythonGradioService     com.example.aicamera                 D  🔗 Connection test result: {'success': True, 'message': 'Successfully connected to HF Space', 'space': 'yamero999/chess-fen-generation-api'}
2025-05-30 14:12:11.219 19161-19238 PythonGradioService     com.example.aicamera                 D  ✅ Python gradio service initialized successfully
2025-05-30 14:12:11.618 19161-19161 Choreographer           com.example.aicamera                 I  Skipped 34 frames!  The application may be doing too much work on its main thread.
2025-05-30 14:12:11.628 19161-19291 CameraManagerGlobal     com.example.aicamera                 I  Connecting to camera service
2025-05-30 14:12:11.710 19161-19291 CameraRepository        com.example.aicamera                 D  Added camera: 0
2025-05-30 14:12:11.805 19161-19291 Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 14:12:11.824 19161-19291 CameraRepository        com.example.aicamera                 D  Added camera: 1
2025-05-30 14:12:11.827 19161-19291 Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 14:12:11.830 19161-19291 CameraValidator         com.example.aicamera                 D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-30 14:12:12.251 19161-19161 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:12:12.252 19161-19161 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:12:12.301 19161-19161 DynamicRangeResolver    com.example.aicamera                 D  Resolved dynamic range for use case androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd to no compatible HDR dynamic ranges.
                                                                                                    DynamicRange@ba9907c{encoding=UNSPECIFIED, bitDepth=0}
                                                                                                    ->
                                                                                                    DynamicRange@f9b7b6f{encoding=SDR, bitDepth=8}
2025-05-30 14:12:12.324 19161-19161 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:12:12.331 19161-19161 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=1, used_surfaces=0](androidx.camera.core.processing.SurfaceEdge$SettableSurface@7b0e481}
2025-05-30 14:12:12.338 19161-19161 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=2, used_surfaces=0](androidx.camera.core.SurfaceRequest$2@38647bd}
2025-05-30 14:12:12.342 19161-19161 DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@38647bd}
2025-05-30 14:12:12.342 19161-19161 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.SurfaceRequest$2@38647bd
2025-05-30 14:12:12.351 19161-19161 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:12:12.364 19161-19161 ImageCapture            com.example.aicamera                 D  createPipeline(cameraId: 0, streamSpec: StreamSpec{resolution=3264x2448, dynamicRange=DynamicRange@f9b7b6f{encoding=SDR, bitDepth=8}, expectedFrameRateRange=[0, 0], implementationOptions=androidx.camera.camera2.impl.Camera2ImplConfig@5b3d6fe})
2025-05-30 14:12:12.380 19161-19161 BufferQueueConsumer     com.example.aicamera                 I  [](id:4ad900000001,api:0,p:-1,c:19161) connect: controlledByApp=true
2025-05-30 14:12:12.382 19161-19161 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=3, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@f881e5f}
2025-05-30 14:12:12.396 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Use case androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194 ACTIVE
2025-05-30 14:12:12.401 19161-19293 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:12:12.413 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Use case androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613 ACTIVE
2025-05-30 14:12:12.414 19161-19293 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:12:12.416 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Use case androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194 ACTIVE
2025-05-30 14:12:12.417 19161-19293 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:12:12.419 19161-19293 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:12:12.423 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Use cases [androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613, androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194] now ATTACHED
2025-05-30 14:12:12.427 19161-19293 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194, androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613] for camera: 0
2025-05-30 14:12:12.428 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  mMeteringRepeating is ATTACHED, SessionConfig Surfaces: 2, CaptureConfig Surfaces: 1
2025-05-30 14:12:12.433 19161-19293 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194, androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613] for camera: 0
2025-05-30 14:12:12.435 19161-19161 PreviewView             com.example.aicamera                 D  Surface requested by Preview.
2025-05-30 14:12:12.442 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Resetting Capture Session
2025-05-30 14:12:12.443 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Releasing session in state INITIALIZED
2025-05-30 14:12:12.446 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Attempting to force open the camera.
2025-05-30 14:12:12.447 19161-19293 CameraStateRegistry     com.example.aicamera                 D  tryOpenCamera(Camera@7eee1bb[id=0]) [Available Cameras: 1, Already Open: false (Previous state: null)] --> SUCCESS
2025-05-30 14:12:12.448 19161-19293 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@7eee1bb[id=0]                         OPENING               
                                                                                                    Camera@adf884[id=1]                          UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 14:12:12.449 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Opening camera.
2025-05-30 14:12:12.450 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Transitioning camera internal state: INITIALIZED --> OPENING
2025-05-30 14:12:12.451 19161-19293 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPENING, error=null} from OPENING and null
2025-05-30 14:12:12.451 19161-19161 SurfaceFactory          com.example.aicamera                 I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@f3d9a2d
2025-05-30 14:12:12.451 19161-19293 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPENING, error=null}
2025-05-30 14:12:12.453 19161-19293 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194, androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613] for camera: 0
2025-05-30 14:12:12.457 19161-19161 CameraX-core_ca         com.example.aicamera                 W  type=1400 audit(0.0:6499163): avc: denied { read } for name="u:object_r:vendor_camera_mtk_prop:s0" dev="tmpfs" ino=13626 scontext=u:r:untrusted_app:s0:c231,c258,c512,c768 tcontext=u:object_r:vendor_camera_mtk_prop:s0 tclass=file permissive=0 app=com.example.aicamera
2025-05-30 14:12:12.460 19161-19293 libc                    com.example.aicamera                 W  Access denied finding property "persist.vendor.camera.privapp.list"
2025-05-30 14:12:12.476 19161-19161 PreviewView             com.example.aicamera                 D  Preview transformation info updated. TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false}
2025-05-30 14:12:12.477 19161-19161 PreviewTransform        com.example.aicamera                 D  Transformation info set: TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false} 1024x768 false
2025-05-30 14:12:12.479 19161-19161 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:12:12.482 19161-19161 SurfaceViewImpl         com.example.aicamera                 D  Wait for new Surface creation.
2025-05-30 14:12:12.496 19161-19161 BufferQueueConsumer     com.example.aicamera                 I  [](id:4ad900000002,api:0,p:-1,c:19161) connect: controlledByApp=false
2025-05-30 14:12:12.497 19161-19161 BLASTBufferQueue        com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1] constructor()
2025-05-30 14:12:12.499 19161-19161 SurfaceViewImpl         com.example.aicamera                 D  Surface created.
2025-05-30 14:12:12.503 19161-19161 SurfaceViewImpl         com.example.aicamera                 D  Surface changed. Size: 1024x768
2025-05-30 14:12:12.503 19161-19161 SurfaceViewImpl         com.example.aicamera                 D  Surface set on Preview.
2025-05-30 14:12:12.617 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Use case androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613 ACTIVE
2025-05-30 14:12:12.620 19161-19293 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194, androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613] for camera: 0
2025-05-30 14:12:12.628 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Use case androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194 ACTIVE
2025-05-30 14:12:12.630 19161-19293 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194, androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613] for camera: 0
2025-05-30 14:12:12.637 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} CameraDevice.onOpened()
2025-05-30 14:12:12.638 19161-19293 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Transitioning camera internal state: OPENING --> OPENED
2025-05-30 14:12:12.639 19161-19293 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@7eee1bb[id=0]                         OPEN                  
                                                                                                    Camera@adf884[id=1]                          UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 14:12:12.639 19161-19293 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPEN, error=null} from OPEN and null
2025-05-30 14:12:12.639 19161-19293 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPEN, error=null}
2025-05-30 14:12:12.642 19161-19293 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194, androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613] for camera: 0
2025-05-30 14:12:12.657 19161-19293 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194, androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613] for camera: 0
2025-05-30 14:12:12.665 19161-19293 SyncCaptureSessionBase  com.example.aicamera                 D  [androidx.camera.camera2.internal.SynchronizedCaptureSessionBaseImpl@fe86ae0] getSurface...done
2025-05-30 14:12:12.666 19161-19293 CaptureSession          com.example.aicamera                 D  Opening capture session.
2025-05-30 14:12:12.681 19161-19293 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=2 androidx.camera.core.SurfaceRequest$2@38647bd
2025-05-30 14:12:12.682 19161-19293 DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=3, used_surfaces=2](androidx.camera.core.impl.ImmediateSurface@f881e5f}
2025-05-30 14:12:12.682 19161-19293 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.impl.ImmediateSurface@f881e5f
2025-05-30 14:12:12.878 19161-19293 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) connect: api=4 producerControlledByApp=true
2025-05-30 14:12:12.879 19161-19293 BufferQueueProducer     com.example.aicamera                 I  [ImageReader-3264x2448f100m4-19161-0](id:4ad900000001,api:4,p:985,c:19161) connect: api=4 producerControlledByApp=false
2025-05-30 14:12:12.891 19161-19293 CaptureSession          com.example.aicamera                 D  Attempting to send capture request onConfigured
2025-05-30 14:12:12.891 19161-19293 CaptureSession          com.example.aicamera                 D  Issuing request for session.
2025-05-30 14:12:12.895 19161-19293 Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 14:12:12.916 19161-19293 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onConfigured() mState=OPENED
2025-05-30 14:12:12.929 19161-19293 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onReady() OPENED
2025-05-30 14:12:13.377 19161-19293 StreamStateObserver     com.example.aicamera                 D  Update Preview stream state to STREAMING
2025-05-30 14:12:21.160 19161-19161 ImageCapture            com.example.aicamera                 D  takePictureInternal
2025-05-30 14:12:21.173 19161-19161 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:12:21.178 19161-19161 TakePictureManager      com.example.aicamera                 D  Issue the next TakePictureRequest.
2025-05-30 14:12:21.211 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Issue capture request
2025-05-30 14:12:21.214 19161-19291 CaptureSession          com.example.aicamera                 D  Issuing capture request.
2025-05-30 14:12:21.227 19161-19291 Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 14:12:21.348 19161-19181 GraphicBuffer           com.example.aicamera                 D  initWithSize w=24071582, h=1
2025-05-30 14:12:21.351 19161-19183 GraphicBuffer           com.example.aicamera                 D  flatten w=24071582, h=1
2025-05-30 14:12:21.934 19161-19161 TakePictureManager      com.example.aicamera                 D  Issue the next TakePictureRequest.
2025-05-30 14:12:21.935 19161-19161 TakePictureManager      com.example.aicamera                 D  No new request.
2025-05-30 14:12:22.195 19161-19161 CameraManager           com.example.aicamera                 D  Photo capture succeeded: /storage/emulated/0/Android/data/com.example.aicamera/files/2025-05-30-14-12-21-129.jpg
2025-05-30 14:12:22.213 19161-19161 CameraManager           com.example.aicamera                 D  📏 Captured image size: 2448x3264
2025-05-30 14:12:22.214 19161-19161 ImageProcessor          com.example.aicamera                 D  🔄 Resizing image to 416x416: 2025-05-30-14-12-21-129.jpg
2025-05-30 14:12:22.218 19161-19161 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels +
2025-05-30 14:12:22.453 19161-19161 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels -
2025-05-30 14:12:22.454 19161-19161 ImageProcessor          com.example.aicamera                 D  📏 Original size: 2448x3264
2025-05-30 14:12:22.489 19161-19161 ImageProcessor          com.example.aicamera                 D  ✅ Image resized and saved: 416x416
2025-05-30 14:12:22.490 19161-19161 CameraManager           com.example.aicamera                 D  ✅ Image resized to 416x416 (66 KB)
2025-05-30 14:12:22.499 19161-19161 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels +
2025-05-30 14:12:22.505 19161-19161 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels -
2025-05-30 14:12:22.506 19161-19161 GalleryManager          com.example.aicamera                 D  📏 Gallery save - Image size: 416x416
2025-05-30 14:12:22.506 19161-19161 GalleryManager          com.example.aicamera                 D  ✅ Image already 416x416, perfect for gallery
2025-05-30 14:12:22.782 19161-19161 GalleryManager          com.example.aicamera                 D  ✅ Image saved to gallery: content://media/external/images/media/1000234804
2025-05-30 14:12:22.782 19161-19161 CameraManager           com.example.aicamera                 D  ✅ Image saved to gallery: content://media/external/images/media/1000234804
2025-05-30 14:12:22.782 19161-19161 CameraScreen            com.example.aicamera                 D  ✅ Image saved to gallery: content://media/external/images/media/1000234804
2025-05-30 14:12:22.794 19161-19161 Choreographer           com.example.aicamera                 I  Skipped 36 frames!  The application may be doing too much work on its main thread.
2025-05-30 14:12:22.794 19161-19464 NetworkManager          com.example.aicamera                 D  🐍 Starting Python gradio_client upload...
2025-05-30 14:12:22.794 19161-19464 NetworkManager          com.example.aicamera                 D  📁 File: 2025-05-30-14-12-21-129.jpg, Size: 68078 bytes
2025-05-30 14:12:22.794 19161-19464 PythonGradioService     com.example.aicamera                 D  ✅ Python service already initialized
2025-05-30 14:12:22.797 19161-19464 PythonGradioService     com.example.aicamera                 D  🚀 Starting FEN generation using Python gradio_client...
2025-05-30 14:12:22.797 19161-19464 PythonGradioService     com.example.aicamera                 D  📁 File: 2025-05-30-14-12-21-129.jpg, Size: 68078 bytes
2025-05-30 14:12:22.830 19161-19464 PythonGradioService     com.example.aicamera                 D  📦 Converted image to base64: 90772 characters
2025-05-30 14:12:22.830 19161-19464 PythonGradioService     com.example.aicamera                 D  🐍 Calling Python gradio_client...
2025-05-30 14:12:23.973 19161-19464 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 14:12:33.292 19161-19464 PythonGradioService     com.example.aicamera                 D  📄 Python result: {'success': True, 'fen': '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8', 'error': None, 'raw_result': {'success': True, 'fen': '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8', 'analysis': {'pieces_detected': 18, 'processing_time_ms': 1470.98, 'processing_time_seconds': 1.471, 'v6_inference_time_ms': 1069.7948932647705, 'board_detection_time_ms': 1084.58, 'piece_detection_time_ms': 385.28, 'mapping_time_ms': 0.21, 'save_time_ms': 0.87, 'board_detection': {'corners_found': [[44.0, 125.0], [319.0, 130.0], [304.0, 324.0], [51.0, 321.0]], 'perspective_corrected': True}, 'piece_analysis': {'total_pieces': 18, 'piece_counts': {'white_bishop': 1, 'black_bishop': 1, 'white_pawn': 5, 'black_pawn': 5, 'white_rook': 2, 'black_rook': 2, 'white_king': 1, 'black_king': 1}, 'confidence_stats': {'avg': 0.946, 'min': 0.844, 'max': 0.988}}, 'grid_mapping': {'occupied_squares': 18, 'empty_squares': 46}}, 'provider': 'huggingface_spaces', 'model_info': {'v6_segmentation': 'yamero999/chess-board-segmentation-v6', 'piece_detection': 'yamero999/chess-piece-detection-yolo11n'}}}
2025-05-30 14:12:33.361 19161-19464 PythonGradioService     com.example.aicamera                 D  📊 Parsed result - Success: true, FEN: 1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8, Error: null
2025-05-30 14:12:33.361 19161-19464 PythonGradioService     com.example.aicamera                 D  🎯 FEN Result: 1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8
2025-05-30 14:12:33.362 19161-19464 PythonGradioService     com.example.aicamera                 D  ✅ Successfully processed FEN response
2025-05-30 14:12:33.365 19161-19161 CameraScreen            com.example.aicamera                 D  📝 Original FEN from AI: '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8'
2025-05-30 14:12:33.370 19161-19161 CameraScreen            com.example.aicamera                 D  🔄 Board flipped 180° (vertical + horizontal) - Black king was in wrong position
2025-05-30 14:12:33.370 19161-19161 CameraScreen            com.example.aicamera                 D     Original: '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8'
2025-05-30 14:12:33.370 19161-19161 CameraScreen            com.example.aicamera                 D     Flipped:  '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1'
2025-05-30 14:12:33.370 19161-19161 CameraScreen            com.example.aicamera                 D  🔧 Completed FEN: '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1' (was 1 parts, now 6)
2025-05-30 14:12:33.370 19161-19161 CameraScreen            com.example.aicamera                 D  🚀 Auto-launching external app with processed FEN: '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1'
2025-05-30 14:12:33.384 19161-19161 ExternalAppManager      com.example.aicamera                 D  Direct launch failed, trying generic Chessis launch
2025-05-30 14:12:33.393 19161-19161 ExternalAppManager      com.example.aicamera                 E  ❌ Failed to launch com.chessis.analysis (Ask Gemini)
                                                                                                    android.content.ActivityNotFoundException: No Activity found to handle Intent { act=android.intent.action.SEND typ=text/plain flg=0x10000001 pkg=com.chessis.analysis clip={text/plain {T(56)}} (has extras) }
                                                                                                    	at android.app.Instrumentation.checkStartActivityResult(Instrumentation.java:2137)
                                                                                                    	at android.app.Instrumentation.execStartActivity(Instrumentation.java:1797)
                                                                                                    	at android.app.Activity.startActivityForResult(Activity.java:5643)
                                                                                                    	at androidx.activity.ComponentActivity.startActivityForResult(ComponentActivity.java:780)
                                                                                                    	at android.app.Activity.startActivityForResult(Activity.java:5591)
                                                                                                    	at androidx.activity.ComponentActivity.startActivityForResult(ComponentActivity.java:761)
                                                                                                    	at android.app.Activity.startActivity(Activity.java:6011)
                                                                                                    	at android.app.Activity.startActivity(Activity.java:5964)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchAppWithFEN(ExternalAppManager.kt:139)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchChessis(ExternalAppManager.kt:78)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchChessApp(ExternalAppManager.kt:25)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt.CameraScreen$completeAndLaunchFEN(CameraScreen.kt:177)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt.access$CameraScreen$completeAndLaunchFEN(CameraScreen.kt:1)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt$CameraScreen$6$3$4$2$1$1.invokeSuspend(CameraScreen.kt:478)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher.performTrampolineDispatch(AndroidUiDispatcher.android.kt:81)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher.access$performTrampolineDispatch(AndroidUiDispatcher.android.kt:41)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher$dispatchCallback$1.run(AndroidUiDispatcher.android.kt:57)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:938)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:233)
                                                                                                    	at android.os.Looper.loop(Looper.java:334)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8399)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:582)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1068)
2025-05-30 14:12:33.474 19161-19161 ExternalAppManager      com.example.aicamera                 D  ✅ Launched app chooser with clean FEN: 8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1
2025-05-30 14:12:33.474 19161-19161 CameraScreen            com.example.aicamera                 D  ✅ Successfully launched external app: chessis
2025-05-30 14:12:42.692 19161-19161 SurfaceViewImpl         com.example.aicamera                 D  Surface destroyed.
2025-05-30 14:12:42.692 19161-19161 SurfaceViewImpl         com.example.aicamera                 D  Surface closed androidx.camera.core.SurfaceRequest@1e17314
2025-05-30 14:12:42.699 19161-19161 DeferrableSurface       com.example.aicamera                 D  surface closed,  useCount=2 closed=true androidx.camera.core.SurfaceRequest$2@38647bd
2025-05-30 14:12:42.700 19161-19161 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) disconnect: api -1
2025-05-30 14:12:42.700 19161-19161 BLASTBufferQueue        com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1] destructor()
2025-05-30 14:12:42.700 19161-19161 BufferQueueConsumer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) disconnect
2025-05-30 14:12:42.717 19161-19181 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) queueBuffer: BufferQueue has been abandoned
2025-05-30 14:12:42.731 19161-19181 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) dequeueBuffer: BufferQueue has been abandoned
2025-05-30 14:12:42.732 19161-19230 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:4ad900000000,api:1,p:19161,c:19161) disconnect: api 1
2025-05-30 14:12:42.739 19161-19754 BLASTBufferQueue        com.example.aicamera                 I  releaseBufferCallbackThunk bufferId:82295868358673 framenumber:566 blastBufferQueue is dead
2025-05-30 14:12:42.793 19161-19161 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0] destructor()
2025-05-30 14:12:42.793 19161-19161 BufferQueueConsumer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:4ad900000000,api:0,p:-1,c:19161) disconnect
2025-05-30 14:12:42.796 19161-19754 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) queueBuffer: BufferQueue has been abandoned
2025-05-30 14:12:42.811 19161-19161 DeferrableSurface       com.example.aicamera                 D  surface closed,  useCount=0 closed=true androidx.camera.core.processing.SurfaceEdge$SettableSurface@7b0e481
2025-05-30 14:12:42.812 19161-19161 DeferrableSurface       com.example.aicamera                 D  Surface terminated[total_surfaces=2, used_surfaces=2](androidx.camera.core.processing.SurfaceEdge$SettableSurface@7b0e481}
2025-05-30 14:12:42.812 19161-19161 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=1 closed=true androidx.camera.core.SurfaceRequest$2@38647bd
2025-05-30 14:12:42.848 19161-19754 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) queueBuffer: BufferQueue has been abandoned
2025-05-30 14:12:42.916 19161-19754 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) queueBuffer: BufferQueue has been abandoned
2025-05-30 14:12:42.984 19161-19754 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) queueBuffer: BufferQueue has been abandoned
2025-05-30 14:12:42.987 19161-19293 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onReady() OPENED
2025-05-30 14:12:43.722 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Use cases [androidx.camera.core.Preview-18c0bd51-756e-4734-85f2-56b1d485cbcd245985613, androidx.camera.core.ImageCapture-86c4918f-1add-462b-a21a-9a5b319a005a221832194] now DETACHED for camera
2025-05-30 14:12:43.724 19161-19291 UseCaseAttachState      com.example.aicamera                 D  All use case: [] for camera: 0
2025-05-30 14:12:43.726 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Resetting Capture Session
2025-05-30 14:12:43.740 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Releasing session in state OPENED
2025-05-30 14:12:43.740 19161-19291 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:12:43.748 19161-19291 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:12:43.757 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Closing camera.
2025-05-30 14:12:43.759 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Transitioning camera internal state: OPENED --> CLOSING
2025-05-30 14:12:43.763 19161-19291 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@7eee1bb[id=0]                         CLOSING               
                                                                                                    Camera@adf884[id=1]                          UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 14:12:43.767 19161-19291 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=CLOSING, error=null} from CLOSING and null
2025-05-30 14:12:43.767 19161-19291 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=CLOSING, error=null}
2025-05-30 14:12:43.768 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Resetting Capture Session
2025-05-30 14:12:43.770 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Releasing session in state CLOSING
2025-05-30 14:12:43.776 19161-19291 CaptureSession          com.example.aicamera                 D  onSessionFinished()
2025-05-30 14:12:43.787 19161-19161 StreamStateObserver     com.example.aicamera                 D  Update Preview stream state to IDLE
2025-05-30 14:12:43.824 19161-19183 BLASTBufferQueue        com.example.aicamera                 I  releaseBufferCallbackThunk bufferId:82295868358660 framenumber:789 blastBufferQueue is dead
2025-05-30 14:12:44.297 19161-19291 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:4ad900000002,api:4,p:985,c:19161) disconnect: api 4
2025-05-30 14:12:44.299 19161-19291 BufferQueueProducer     com.example.aicamera                 I  [ImageReader-3264x2448f100m4-19161-0](id:4ad900000001,api:4,p:985,c:19161) disconnect: api 4
2025-05-30 14:12:44.309 19161-19291 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:12:44.314 19161-19291 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=0 closed=true androidx.camera.core.SurfaceRequest$2@38647bd
2025-05-30 14:12:44.315 19161-19291 DeferrableSurface       com.example.aicamera                 D  Surface no longer in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@38647bd}
2025-05-30 14:12:44.316 19161-19291 DeferrableSurface       com.example.aicamera                 D  Surface terminated[total_surfaces=1, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@38647bd}
2025-05-30 14:12:44.317 19161-19291 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=0 closed=false androidx.camera.core.impl.ImmediateSurface@f881e5f
2025-05-30 14:12:44.317 19161-19291 DeferrableSurface       com.example.aicamera                 D  Surface no longer in use[total_surfaces=1, used_surfaces=0](androidx.camera.core.impl.ImmediateSurface@f881e5f}
2025-05-30 14:12:44.318 19161-19161 SurfaceViewImpl         com.example.aicamera                 D  Safe to release surface.
2025-05-30 14:12:44.319 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} CameraDevice.onClosed()
2025-05-30 14:12:44.322 19161-19291 Camera2CameraImpl       com.example.aicamera                 D  {Camera@7eee1bb[id=0]} Transitioning camera internal state: CLOSING --> INITIALIZED
2025-05-30 14:12:44.326 19161-19291 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@7eee1bb[id=0]                         CLOSED                
                                                                                                    Camera@adf884[id=1]                          UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 0 (Max allowed: 1)
2025-05-30 14:12:44.328 19161-19291 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=CLOSED, error=null} from CLOSED and null
2025-05-30 14:12:44.329 19161-19291 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=CLOSED, error=null}
