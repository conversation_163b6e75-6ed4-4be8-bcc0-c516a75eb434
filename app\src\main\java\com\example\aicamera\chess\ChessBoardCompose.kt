package com.example.aicamera.chess

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.github.bhlangonijr.chesslib.*
import com.github.bhlangonijr.chesslib.move.Move

/**
 * Chess Board Compose Component using chesslib
 * Integrates with existing chess models and provides interactive board
 */

@Composable
fun ChessBoardCompose(
    gameState: ChessGameState,
    onSquareClicked: (Square) -> Unit = {},
    onPieceMoved: (Move) -> Unit = {},
    modifier: Modifier = Modifier
) {
    var selectedSquare by remember { mutableStateOf<Square?>(null) }
    val board = gameState.board

    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Board Header
            Text(
                text = "Chess Board",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            // Chess Board Grid
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f)
                    .clip(RoundedCornerShape(12.dp))
                    .border(2.dp, MaterialTheme.colorScheme.outline, RoundedCornerShape(12.dp))
            ) {
                // Draw board background
                Canvas(
                    modifier = Modifier.fillMaxSize()
                ) {
                    drawChessBoard(this)
                }

                // Interactive board overlay
                ChessBoardGrid(
                    board = board,
                    selectedSquare = selectedSquare,
                    possibleMoves = gameState.possibleMoves,
                    onSquareClicked = { square ->
                        when {
                            selectedSquare == null -> {
                                // Select piece if there's one at this square
                                if (board.getPiece(square) != Piece.NONE) {
                                    selectedSquare = square
                                    onSquareClicked(square)
                                }
                            }
                            selectedSquare == square -> {
                                // Deselect if clicking same square
                                selectedSquare = null
                            }
                            else -> {
                                // Try to make a move
                                val move = Move(selectedSquare!!, square)
                                if (gameState.possibleMoves.contains(move) || board.isMoveLegal(move, true)) {
                                    onPieceMoved(move)
                                }
                                selectedSquare = null
                            }
                        }
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }

            // Board Footer with coordinates
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                val files = listOf("a", "b", "c", "d", "e", "f", "g", "h")
                files.forEach { file ->
                    Text(
                        text = file,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun ChessBoardGrid(
    board: Board,
    selectedSquare: Square?,
    possibleMoves: List<Move>,
    onSquareClicked: (Square) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
    ) {
        // Draw from rank 8 to rank 1 (top to bottom)
        val ranks = listOf(Rank.RANK_8, Rank.RANK_7, Rank.RANK_6, Rank.RANK_5, Rank.RANK_4, Rank.RANK_3, Rank.RANK_2, Rank.RANK_1)
        val files = listOf(File.FILE_A, File.FILE_B, File.FILE_C, File.FILE_D, File.FILE_E, File.FILE_F, File.FILE_G, File.FILE_H)

        ranks.forEach { rank ->
            Row(
                modifier = Modifier.weight(1f)
            ) {
                // Draw from file A to file H (left to right)
                files.forEach { file ->
                    val square = Square.encode(rank, file)
                    val piece = board.getPiece(square)
                    val isSelected = selectedSquare == square
                    val isPossibleMove = possibleMoves.any { it.to == square }
                    val isLastMove = false // Could be enhanced to show last move

                    ChessSquare(
                        square = square,
                        piece = piece,
                        isSelected = isSelected,
                        isPossibleMove = isPossibleMove,
                        isLastMove = isLastMove,
                        onClick = { onSquareClicked(square) },
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                    )
                }
            }
        }
    }
}

@Composable
private fun ChessSquare(
    square: Square,
    piece: Piece,
    isSelected: Boolean,
    isPossibleMove: Boolean,
    isLastMove: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isLightSquare = (square.file.ordinal + square.rank.ordinal) % 2 == 0
    val backgroundColor = when {
        isSelected -> MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
        isLastMove -> MaterialTheme.colorScheme.secondary.copy(alpha = 0.4f)
        isPossibleMove -> MaterialTheme.colorScheme.tertiary.copy(alpha = 0.3f)
        isLightSquare -> Color(0xFFF0D9B5)
        else -> Color(0xFFB58863)
    }

    Box(
        modifier = modifier
            .background(backgroundColor)
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (piece != Piece.NONE) {
            val chessPiece = ChessPiece.fromChessLibPiece(piece)
            chessPiece?.let {
                Text(
                    text = it.symbol,
                    fontSize = 28.sp,
                    color = if (it.color == PieceColor.WHITE) Color.White else Color.Black
                )
            }
        }

        // Show possible move indicator
        if (isPossibleMove && piece == Piece.NONE) {
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.7f),
                        androidx.compose.foundation.shape.CircleShape
                    )
            )
        }
    }
}

private fun drawChessBoard(drawScope: DrawScope) {
    val squareSize = drawScope.size.width / 8

    for (rank in 0..7) {
        for (file in 0..7) {
            val isLightSquare = (rank + file) % 2 == 0
            val color = if (isLightSquare) Color(0xFFF0D9B5) else Color(0xFFB58863)

            drawScope.drawRect(
                color = color,
                topLeft = Offset(file * squareSize, rank * squareSize),
                size = Size(squareSize, squareSize)
            )
        }
    }
}

/**
 * Simple chess board that displays FEN position
 */
@Composable
fun SimpleFENBoard(
    fen: String,
    modifier: Modifier = Modifier
) {
    val board = remember(fen) {
        Board().apply {
            loadFromFen(fen)
        }
    }

    val gameState = remember(board) {
        ChessGameState(
            board = board,
            currentFen = fen
        )
    }

    ChessBoardCompose(
        gameState = gameState,
        modifier = modifier
    )
}
