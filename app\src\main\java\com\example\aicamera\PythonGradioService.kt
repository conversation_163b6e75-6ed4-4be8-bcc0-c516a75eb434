package com.example.aicamera

import android.content.Context
import android.util.Log
import com.chaquo.python.Python
import com.chaquo.python.android.AndroidPlatform
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Service that uses Python gradio_client library directly on Android
 * This provides the most reliable integration with Gradio Spaces
 */
class PythonGradioService private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: PythonGradioService? = null
        private const val TAG = "PythonGradioService"
        private var isPreInitializing = false

        fun getInstance(): PythonGradioService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PythonGradioService().also { INSTANCE = it }
            }
        }

        /**
         * EXTREME: Aggressive pre-initialization with module pre-loading
         */
        fun preInitialize(context: Context) {
            if (isPreInitializing) return

            Thread {
                try {
                    isPreInitializing = true
                    Log.d(TAG, "🚀 EXTREME: Aggressive Python pre-initialization starting...")

                    val startTime = System.currentTimeMillis()

                    if (!Python.isStarted()) {
                        Python.start(AndroidPlatform(context))
                        Log.d(TAG, "✅ Python environment pre-started")
                    }

                    // Pre-warm the instance AND pre-load modules
                    val instance = getInstance()
                    if (instance.initialize(context)) {
                        // AGGRESSIVE: Pre-load and test the chess module
                        try {
                            val python = Python.getInstance()
                            val module = python.getModule("chess_fen_client")

                            // Pre-warm the connection (this loads all dependencies)
                            Log.d(TAG, "🔥 Pre-warming API connection...")
                            module.callAttr("test_connection")

                            val totalTime = System.currentTimeMillis() - startTime
                            Log.d(TAG, "🏆 EXTREME pre-initialization complete in ${totalTime}ms")

                        } catch (e: Exception) {
                            Log.w(TAG, "⚠️ Pre-warming failed, but basic init succeeded", e)
                        }
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "❌ Extreme pre-initialization failed", e)
                } finally {
                    isPreInitializing = false
                }
            }.start()
        }
    }

    private var python: Python? = null
    private var isInitialized = false
    private var chessModule: com.chaquo.python.PyObject? = null

    /**
     * Initialize Python environment and gradio_client
     */
    fun initialize(context: Context): Boolean {
        return try {
            if (isInitialized) {
                Log.d(TAG, "✅ Python service already initialized")
                return true
            }

            if (!Python.isStarted()) {
                Log.d(TAG, "🐍 Starting Python environment...")
                Python.start(AndroidPlatform(context))
                Log.d(TAG, "✅ Python environment started")
            }

            python = Python.getInstance()
            Log.d(TAG, "🔗 Python instance obtained")

            // Test if we can import our module
            try {
                val module = python?.getModule("chess_fen_client")
                Log.d(TAG, "📦 chess_fen_client module imported successfully")

                // Test connection
                val testResult = module?.callAttr("test_connection")
                Log.d(TAG, "🔗 Connection test result: $testResult")

                isInitialized = true
                Log.d(TAG, "✅ Python gradio service initialized successfully")
                true

            } catch (moduleError: Exception) {
                Log.e(TAG, "❌ Failed to import chess_fen_client module", moduleError)
                false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize Python gradio service", e)
            Log.e(TAG, "❌ Exception details: ${e.javaClass.simpleName}: ${e.message}")
            isInitialized = false
            false
        }
    }

    /**
     * Generate FEN using Python gradio_client library (NO CACHING for accuracy)
     * @param imageFile The chess board image file
     * @return NetworkResult containing the chess analysis
     */
    suspend fun generateFen(imageFile: File): NetworkResult<AiResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🚀 Starting FEN generation using Python gradio_client...")
            Log.d(TAG, "📁 File: ${imageFile.name}, Size: ${imageFile.length()} bytes")

            if (!isInitialized) {
                Log.e(TAG, "❌ Python service not initialized")
                return@withContext NetworkResult.Error("Python service not initialized")
            }

            if (!imageFile.exists()) {
                Log.e(TAG, "❌ Image file does not exist")
                return@withContext NetworkResult.Error("Image file not found")
            }

            // Validate file size
            if (imageFile.length() == 0L) {
                Log.e(TAG, "❌ Image file is empty")
                return@withContext NetworkResult.Error("Image file is empty")
            }

            // Convert image to base64 with error handling
            val imageBytes = try {
                imageFile.readBytes()
            } catch (e: Exception) {
                Log.e(TAG, "❌ Failed to read image file", e)
                return@withContext NetworkResult.Error("Failed to read image file: ${e.message}")
            }

            val base64Image = try {
                android.util.Base64.encodeToString(imageBytes, android.util.Base64.NO_WRAP)
            } catch (e: Exception) {
                Log.e(TAG, "❌ Failed to encode image to base64", e)
                return@withContext NetworkResult.Error("Failed to encode image: ${e.message}")
            }

            Log.d(TAG, "📦 Converted image to base64: ${base64Image.length} characters")
            Log.d(TAG, "🐍 Calling Python gradio_client...")

            // Call Python function with timeout protection
            val pythonModule = python?.getModule("chess_fen_client")
            if (pythonModule == null) {
                Log.e(TAG, "❌ Python module not available")
                return@withContext NetworkResult.Error("Python module not available")
            }

            val result = try {
                pythonModule.callAttr("generate_fen_from_base64", base64Image)
            } catch (e: Exception) {
                Log.e(TAG, "❌ Python function call failed", e)
                return@withContext NetworkResult.Error("Python function call failed: ${e.message}")
            }

            Log.d(TAG, "📄 Python result: $result")

            // Parse Python result with enhanced error handling
            if (result != null) {
                try {
                    // Safely convert PyObject map to Kotlin map
                    val pyMap = result.asMap()
                    val resultMap = mutableMapOf<String, Any?>()

                    // Safely extract values from PyObject map
                    for ((key, value) in pyMap) {
                        try {
                            val keyStr = key?.toString()
                            if (keyStr != null) {
                                resultMap[keyStr] = when {
                                    value == null -> null
                                    value.toString() == "True" -> true
                                    value.toString() == "False" -> false
                                    else -> value.toString()
                                }
                            }
                        } catch (e: Exception) {
                            Log.w(TAG, "⚠️ Failed to parse key-value pair: $key -> $value", e)
                        }
                    }

                    val success = (resultMap["success"] as? Boolean) ?: false
                    val fen = resultMap["fen"]?.toString()
                    val error = resultMap["error"]?.toString()

                    Log.d(TAG, "📊 Parsed result - Success: $success, FEN: $fen, Error: $error")

                if (success && !fen.isNullOrEmpty()) {
                    Log.d(TAG, "🎯 FEN Result: $fen")

                    val aiResponse = AiResponse(
                        success = true,
                        message = "Chess FEN generated successfully using Python gradio_client",
                        result = fen,
                        provider = "python_gradio_client",
                        processingTime = null
                    )

                    Log.d(TAG, "✅ Successfully processed FEN response")
                    NetworkResult.Success(aiResponse)
                } else {
                    val errorMsg = error ?: "Unknown error from Python gradio_client"
                    Log.e(TAG, "❌ Python gradio_client error: $errorMsg")
                    NetworkResult.Error("Python gradio_client error: $errorMsg")
                }

                } catch (parseException: Exception) {
                    Log.e(TAG, "❌ Failed to parse Python result", parseException)
                    NetworkResult.Error("Failed to parse Python result: ${parseException.message}")
                }
            } else {
                Log.e(TAG, "❌ Null result from Python gradio_client")
                NetworkResult.Error("Null result from Python gradio_client")
            }

        } catch (e: Exception) {
            Log.e(TAG, "💥 Python gradio service failed with exception", e)
            Log.e(TAG, "💥 Exception type: ${e.javaClass.simpleName}")
            Log.e(TAG, "💥 Exception message: ${e.message}")
            e.printStackTrace()
            NetworkResult.Error("Python gradio service error: ${e.message}")
        }
    }

    /**
     * Test the Python gradio_client connection
     */
    suspend fun testConnection(): NetworkResult<String> = withContext(Dispatchers.IO) {
        try {
            if (!isInitialized) {
                return@withContext NetworkResult.Error("Python service not initialized")
            }

            Log.d(TAG, "🔗 Testing Python gradio_client connection...")

            val pythonModule = python?.getModule("chess_fen_client")
            val result = pythonModule?.callAttr("test_connection")

            Log.d(TAG, "📄 Connection test result: $result")

            if (result != null) {
                // Safely convert PyObject map to Kotlin map
                val pyMap = result.asMap()
                val resultMap = mutableMapOf<String, Any?>()

                // Safely extract values from PyObject map
                for ((key, value) in pyMap) {
                    val keyStr = key?.toString()
                    if (keyStr != null) {
                        resultMap[keyStr] = when {
                            value == null -> null
                            value.toString() == "True" -> true
                            value.toString() == "False" -> false
                            else -> value.toString()
                        }
                    }
                }

                val success = (resultMap["success"] as? Boolean) ?: false
                val message = resultMap["message"]?.toString()
                val error = resultMap["error"]?.toString()

                if (success) {
                    NetworkResult.Success(message ?: "Connection successful")
                } else {
                    NetworkResult.Error(error ?: "Connection failed")
                }
            } else {
                NetworkResult.Error("Null result from connection test")
            }

        } catch (e: Exception) {
            Log.e(TAG, "💥 Connection test failed", e)
            NetworkResult.Error("Connection test error: ${e.message}")
        }
    }
}
