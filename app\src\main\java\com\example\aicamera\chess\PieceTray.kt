package com.example.aicamera.chess

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Piece Tray Component for Edit Mode
 * Provides drag-and-drop functionality for chess pieces
 */

@Composable
fun PieceTray(
    selectedPiece: ChessPiece?,
    onPieceSelected: (ChessPiece?) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Piece Tray",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            // White Pieces
            Text(
                text = "White Pieces",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                items(ChessPiece.whitePieces()) { piece ->
                    PieceTrayItem(
                        piece = piece,
                        isSelected = selectedPiece == piece,
                        onClick = { 
                            onPieceSelected(if (selectedPiece == piece) null else piece)
                        }
                    )
                }
            }
            
            // Black Pieces
            Text(
                text = "Black Pieces",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                items(ChessPiece.blackPieces()) { piece ->
                    PieceTrayItem(
                        piece = piece,
                        isSelected = selectedPiece == piece,
                        onClick = { 
                            onPieceSelected(if (selectedPiece == piece) null else piece)
                        }
                    )
                }
            }
            
            // Eraser Tool
            Text(
                text = "Tools",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Eraser
                PieceTrayItem(
                    piece = ChessPiece.ERASER,
                    isSelected = selectedPiece == ChessPiece.ERASER,
                    onClick = { 
                        onPieceSelected(if (selectedPiece == ChessPiece.ERASER) null else ChessPiece.ERASER)
                    }
                )
            }
        }
    }
}

@Composable
private fun PieceTrayItem(
    piece: ChessPiece,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = when {
        isSelected -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
        else -> MaterialTheme.colorScheme.surface
    }
    
    val borderColor = when {
        isSelected -> MaterialTheme.colorScheme.primary
        else -> MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
    }
    
    Box(
        modifier = modifier
            .size(48.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .border(2.dp, borderColor, RoundedCornerShape(8.dp))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (piece.type == PieceType.ERASER) {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = "Eraser",
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(24.dp)
            )
        } else {
            Text(
                text = piece.symbol,
                fontSize = 28.sp,
                color = if (piece.color == PieceColor.WHITE) Color.White else Color.Black
            )
        }
    }
}

/**
 * Edit Mode Controls
 */
@Composable
fun EditModeControls(
    isEditMode: Boolean,
    onToggleEditMode: () -> Unit,
    onClearBoard: () -> Unit,
    onResetBoard: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Board Controls",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            // Edit Mode Toggle
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onToggleEditMode,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isEditMode) MaterialTheme.colorScheme.secondary else MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text(if (isEditMode) "Exit Edit" else "Edit Mode")
                }
            }
            
            if (isEditMode) {
                Spacer(modifier = Modifier.height(8.dp))
                
                // Clear and Reset buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onClearBoard,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Clear Board")
                    }
                    
                    OutlinedButton(
                        onClick = onResetBoard,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Reset")
                    }
                }
            }
        }
    }
}
