package com.example.aicamera.chess

import com.github.bhlangonijr.chesslib.*
import com.github.bhlangonijr.chesslib.move.Move

/**
 * Chess game state and models
 */

data class ChessGameState(
    val board: Board = Board(),
    val currentFen: String = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
    val moveHistory: List<Move> = emptyList(),
    val engineAnalysis: EngineAnalysis? = null,
    val isAnalyzing: Boolean = false,
    val gameMode: GameMode = GameMode.ANALYSIS,
    val selectedSquare: Square? = null,
    val possibleMoves: List<Move> = emptyList(),
    val lastMove: Move? = null,
    val isEditMode: Boolean = false
)

enum class GameMode {
    ANALYSIS,
    PLAY_VS_ENGINE,
    PLAY_VS_HUMAN,
    SCAN_ANALYSIS,
    EDIT_POSITION
}

data class EngineAnalysis(
    val bestMove: String,
    val evaluation: Float, // In centipawns
    val depth: Int,
    val principalVariation: List<String>,
    val analysisLines: List<AnalysisLine> = emptyList(),
    val nodesPerSecond: Long = 0,
    val timeMs: Long = 0
)

data class AnalysisLine(
    val moves: List<String>,
    val evaluation: Float,
    val depth: Int
)

// Chess Piece for UI (different from chesslib Piece)
data class ChessPiece(
    val type: PieceType,
    val color: PieceColor,
    val symbol: String
) {
    companion object {
        val ERASER = ChessPiece(PieceType.ERASER, PieceColor.NONE, "🗑️")

        fun whitePieces() = listOf(
            ChessPiece(PieceType.KING, PieceColor.WHITE, "♔"),
            ChessPiece(PieceType.QUEEN, PieceColor.WHITE, "♕"),
            ChessPiece(PieceType.ROOK, PieceColor.WHITE, "♖"),
            ChessPiece(PieceType.BISHOP, PieceColor.WHITE, "♗"),
            ChessPiece(PieceType.KNIGHT, PieceColor.WHITE, "♘"),
            ChessPiece(PieceType.PAWN, PieceColor.WHITE, "♙")
        )

        fun blackPieces() = listOf(
            ChessPiece(PieceType.KING, PieceColor.BLACK, "♚"),
            ChessPiece(PieceType.QUEEN, PieceColor.BLACK, "♛"),
            ChessPiece(PieceType.ROOK, PieceColor.BLACK, "♜"),
            ChessPiece(PieceType.BISHOP, PieceColor.BLACK, "♝"),
            ChessPiece(PieceType.KNIGHT, PieceColor.BLACK, "♞"),
            ChessPiece(PieceType.PAWN, PieceColor.BLACK, "♟")
        )

        fun fromChessLibPiece(piece: Piece): ChessPiece? {
            return when (piece) {
                Piece.WHITE_KING -> ChessPiece(PieceType.KING, PieceColor.WHITE, "♔")
                Piece.WHITE_QUEEN -> ChessPiece(PieceType.QUEEN, PieceColor.WHITE, "♕")
                Piece.WHITE_ROOK -> ChessPiece(PieceType.ROOK, PieceColor.WHITE, "♖")
                Piece.WHITE_BISHOP -> ChessPiece(PieceType.BISHOP, PieceColor.WHITE, "♗")
                Piece.WHITE_KNIGHT -> ChessPiece(PieceType.KNIGHT, PieceColor.WHITE, "♘")
                Piece.WHITE_PAWN -> ChessPiece(PieceType.PAWN, PieceColor.WHITE, "♙")
                Piece.BLACK_KING -> ChessPiece(PieceType.KING, PieceColor.BLACK, "♚")
                Piece.BLACK_QUEEN -> ChessPiece(PieceType.QUEEN, PieceColor.BLACK, "♛")
                Piece.BLACK_ROOK -> ChessPiece(PieceType.ROOK, PieceColor.BLACK, "♜")
                Piece.BLACK_BISHOP -> ChessPiece(PieceType.BISHOP, PieceColor.BLACK, "♝")
                Piece.BLACK_KNIGHT -> ChessPiece(PieceType.KNIGHT, PieceColor.BLACK, "♞")
                Piece.BLACK_PAWN -> ChessPiece(PieceType.PAWN, PieceColor.BLACK, "♟")
                else -> null
            }
        }

        fun toChessLibPiece(chessPiece: ChessPiece): Piece {
            return when (chessPiece.type to chessPiece.color) {
                PieceType.KING to PieceColor.WHITE -> Piece.WHITE_KING
                PieceType.QUEEN to PieceColor.WHITE -> Piece.WHITE_QUEEN
                PieceType.ROOK to PieceColor.WHITE -> Piece.WHITE_ROOK
                PieceType.BISHOP to PieceColor.WHITE -> Piece.WHITE_BISHOP
                PieceType.KNIGHT to PieceColor.WHITE -> Piece.WHITE_KNIGHT
                PieceType.PAWN to PieceColor.WHITE -> Piece.WHITE_PAWN
                PieceType.KING to PieceColor.BLACK -> Piece.BLACK_KING
                PieceType.QUEEN to PieceColor.BLACK -> Piece.BLACK_QUEEN
                PieceType.ROOK to PieceColor.BLACK -> Piece.BLACK_ROOK
                PieceType.BISHOP to PieceColor.BLACK -> Piece.BLACK_BISHOP
                PieceType.KNIGHT to PieceColor.BLACK -> Piece.BLACK_KNIGHT
                PieceType.PAWN to PieceColor.BLACK -> Piece.BLACK_PAWN
                else -> Piece.NONE
            }
        }
    }
}

enum class PieceType {
    KING, QUEEN, ROOK, BISHOP, KNIGHT, PAWN, ERASER
}

enum class PieceColor {
    WHITE, BLACK, NONE
}

enum class BoardOrientation {
    WHITE, BLACK
}

// Game result
data class GameResult(
    val result: GameResultType,
    val reason: String,
    val winner: PieceColor?
)

enum class GameResultType {
    WHITE_WINS,
    BLACK_WINS,
    DRAW,
    IN_PROGRESS
}

// Quick play options
data class PlayOption(
    val title: String,
    val subtitle: String,
    val icon: String, // We'll use string for icon names
    val gameMode: GameMode
)

// Recent game data
data class RecentGame(
    val id: String,
    val opponent: String,
    val result: GameResult,
    val date: Long,
    val openingName: String?,
    val finalPosition: String
)

// Learning module data
data class LearningModule(
    val title: String,
    val description: String,
    val progress: Float, // 0.0 to 1.0
    val icon: String
)

// Analysis result from scanning
data class ScanResult(
    val fen: String,
    val confidence: Float,
    val processingTimeMs: Long,
    val piecesDetected: Int,
    val analysis: EngineAnalysis?
)

// Position information
data class PositionInfo(
    val fen: String,
    val sideToMove: PieceColor,
    val castlingRights: String,
    val enPassantSquare: String?,
    val halfMoveClock: Int,
    val fullMoveNumber: Int,
    val isCheck: Boolean,
    val isCheckmate: Boolean,
    val isStalemate: Boolean,
    val materialBalance: Int // In centipawns
)
