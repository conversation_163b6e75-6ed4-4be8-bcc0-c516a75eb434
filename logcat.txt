--------- beginning of main
---------------------------- PROCESS STARTED (23238) for package com.example.aicamera ----------------------------
2025-05-30 14:24:57.415 23238-23238 nativeloader            com.example.aicamera                 D  Configuring clns-4 for other apk /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/lib/arm64:/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.example.aicamera
2025-05-30 14:24:57.445 23238-23238 GraphicsEnvironment     com.example.aicamera                 V  ANGLE Developer option for 'com.example.aicamera' set to: 'default'
2025-05-30 14:24:57.445 23238-23238 GraphicsEnvironment     com.example.aicamera                 V  Neither updatable production driver nor prerelease driver is supported.
2025-05-30 14:24:57.453 23238-23238 NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 14:24:57.456 23238-23238 NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 14:24:57.488 23238-23238 libc                    com.example.aicamera                 W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-30 14:24:57.794 23238-23264 PythonGradioService     com.example.aicamera                 D  🚀 EXTREME: Aggressive Python pre-initialization starting...
2025-05-30 14:24:57.858 23238-23238 xample.aicamer          com.example.aicamera                 E  Invalid ID 0x00000000.
2025-05-30 14:24:57.940 23238-23264 nativeloader            com.example.aicamera                 D  Load /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a/libcrypto_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!classes8.dex): ok
2025-05-30 14:24:57.949 23238-23264 nativeloader            com.example.aicamera                 D  Load /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a/libssl_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!classes8.dex): ok
2025-05-30 14:24:57.955 23238-23264 nativeloader            com.example.aicamera                 D  Load /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a/libsqlite3_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!classes8.dex): ok
2025-05-30 14:24:57.959 23238-23264 nativeloader            com.example.aicamera                 D  Load /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a/libcrypto_python.so using ns clns-4 from class loader (caller=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!classes8.dex): ok
2025-05-30 14:24:57.962 23238-23264 nativeloader            com.example.aicamera                 D  Load /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a/libssl_python.so using ns clns-4 from class loader (caller=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!classes8.dex): ok
2025-05-30 14:24:57.965 23238-23264 nativeloader            com.example.aicamera                 D  Load /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a/libsqlite3_python.so using ns clns-4 from class loader (caller=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!classes8.dex): ok
2025-05-30 14:24:57.992 23238-23264 nativeloader            com.example.aicamera                 D  Load /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a/libpython3.11.so using ns clns-4 from class loader (caller=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!classes8.dex): ok
2025-05-30 14:24:57.997 23238-23264 nativeloader            com.example.aicamera                 D  Load /data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!/lib/arm64-v8a/libchaquopy_java.so using ns clns-4 from class loader (caller=/data/app/~~6gFH_ImVLsOZJHktHGt7iA==/com.example.aicamera--8oUnaiEP74t7pD7-vxViA==/base.apk!classes8.dex): ok
2025-05-30 14:24:58.093 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500386): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/zlib.cpython-311.so" dev="dm-10" ino=392625 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:58.112 23238-23238 Choreographer           com.example.aicamera                 I  Skipped 36 frames!  The application may be doing too much work on its main thread.
2025-05-30 14:24:58.118 23238-23238 XDR::VRT                com.example.aicamera                 I  sc is not valid!
2025-05-30 14:24:58.301 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500387): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/java/chaquopy.so" dev="dm-10" ino=392603 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:58.313 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500388): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_ctypes.cpython-311.so" dev="dm-10" ino=392588 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:58.321 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500389): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_struct.cpython-311.so" dev="dm-10" ino=392585 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:58.393 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500390): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/_bz2.cpython-311.so" dev="dm-10" ino=392505 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:58.829 23238-23238 xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateMap.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-30 14:24:58.830 23238-23238 xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateMap.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:24:58.831 23238-23238 xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateMap.removeIf$runtime_release(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:24:59.381 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500398): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/fcntl.cpython-311.so" dev="dm-10" ino=392656 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:59.397 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500399): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_posixsubprocess.cpython-311.so" dev="dm-10" ino=392659 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:59.413 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500400): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/select.cpython-311.so" dev="dm-10" ino=392631 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:59.474 23238-23264 PythonGradioService     com.example.aicamera                 D  ✅ Python environment pre-started
2025-05-30 14:24:59.475 23238-23264 PythonGradioService     com.example.aicamera                 D  🔗 Python instance obtained
2025-05-30 14:24:59.497 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500401): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_json.cpython-311.so" dev="dm-10" ino=392664 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:59.577 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500402): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_hashlib.cpython-311.so" dev="dm-10" ino=392666 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:24:59.621 23238-23238 xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:24:59.622 23238-23238 xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:24:59.623 23238-23238 xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 14:24:59.991 23238-23238 BufferQueueConsumer     com.example.aicamera                 I  [](id:5ac600000000,api:0,p:-1,c:23238) connect: controlledByApp=false
2025-05-30 14:24:59.996 23238-23238 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0] constructor()
2025-05-30 14:25:00.024 23238-23259 hw-ProcessState         com.example.aicamera                 D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-30 14:25:00.052 23238-23259 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:5ac600000000,api:1,p:23238,c:23238) connect: api=1 producerControlledByApp=true
2025-05-30 14:25:00.063 23238-23268 ion                     com.example.aicamera                 E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-30 14:25:00.421 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500411): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_multibytecodec.cpython-311.so" dev="dm-10" ino=392680 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:25:00.825 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500412): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_contextvars.cpython-311.so" dev="dm-10" ino=392687 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:25:00.845 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500413): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_asyncio.cpython-311.so" dev="dm-10" ino=392697 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:25:00.924 23238-23238 Compatibil...geReporter com.example.aicamera                 D  Compat change id reported: 171228096; UID 10746; state: ENABLED
2025-05-30 14:25:01.172 23238-23243 xample.aicamer          com.example.aicamera                 I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-30 14:25:01.985 23238-23238 Thread-2                com.example.aicamera                 W  type=1400 audit(0.0:6500414): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/resource.cpython-311.so" dev="dm-10" ino=392698 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c234,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 14:25:01.997 23238-23264 python.stderr           com.example.aicamera                 W  /data/data/com.example.aicamera/files/chaquopy/AssetFinder/requirements/websockets/legacy/__init__.py:6: DeprecationWarning: websockets.legacy is deprecated; see https://websockets.readthedocs.io/en/stable/howto/upgrade.html for upgrade instructions
2025-05-30 14:25:02.154 23238-23264 PythonGradioService     com.example.aicamera                 D  📦 chess_fen_client module imported successfully
2025-05-30 14:25:02.296 23238-23259 OpenGLRenderer          com.example.aicamera                 E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-30 14:25:02.296 23238-23259 OpenGLRenderer          com.example.aicamera                 E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-30 14:25:02.321 23238-23252 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=4796ms; Flags=1, FrameTimelineVsyncId=31574275, IntendedVsync=580082154858396, Vsync=580082754858408, InputEventId=0, HandleInputStart=580082770956421, AnimationStart=580082770982575, PerformTraversalsStart=580082771454190, DrawStart=580086730457344, FrameDeadline=580082174858396, FrameInterval=580082770413344, FrameStartTime=16666667, SyncQueued=580086868816421, SyncStart=580086878909344, IssueDrawCommandsStart=580086881006883, SwapBuffers=580086953817960, FrameCompleted=580086961474267, DequeueBufferDuration=0, QueueBufferDuration=3404923, GpuCompleted=580086961474267, SwapBuffersCompleted=580086961153344, DisplayPresentTime=0, 
2025-05-30 14:25:02.570 23238-23238 ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@da10fb9[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-30 14:25:02.570 23238-23238 ImeFocusController      com.example.aicamera                 V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-30 14:25:02.571 23238-23238 ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@da10fb9[ComposeMainActivity], mServedView=null
2025-05-30 14:25:02.572 23238-23238 ImeFocusController      com.example.aicamera                 V  checkFocus: view=null next=DecorView@da10fb9[ComposeMainActivity] force=true package=<none>
2025-05-30 14:25:03.397 23238-23244 xample.aicamer          com.example.aicamera                 I  Background concurrent copying GC freed 130KB AllocSpace bytes, 0(0B) LOS objects, 26% free, 5608KB/7656KB, paused 189us,59us total 120.277ms
2025-05-30 14:25:03.987 23238-23264 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 14:25:04.407 23238-23238 Choreographer           com.example.aicamera                 I  Skipped 31 frames!  The application may be doing too much work on its main thread.
2025-05-30 14:25:04.752 23238-23287 ProfileInstaller        com.example.aicamera                 D  Installing profile for com.example.aicamera
2025-05-30 14:25:05.472 23238-23249 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=1010ms; Flags=0, FrameTimelineVsyncId=31574409, IntendedVsync=580089103983195, Vsync=580089420649868, InputEventId=0, HandleInputStart=580089421489960, AnimationStart=580089421493960, PerformTraversalsStart=580089921523498, DrawStart=580090042125652, FrameDeadline=580089123983195, FrameInterval=580089421468652, FrameStartTime=16666667, SyncQueued=580090090031191, SyncStart=580090090342268, IssueDrawCommandsStart=580090091302498, SwapBuffers=580090107265960, FrameCompleted=580090114540037, DequeueBufferDuration=33231, QueueBufferDuration=1741539, GpuCompleted=580090114540037, SwapBuffersCompleted=580090110617652, DisplayPresentTime=0, 
2025-05-30 14:25:05.501 23238-23238 Choreographer           com.example.aicamera                 I  Skipped 43 frames!  The application may be doing too much work on its main thread.
2025-05-30 14:25:05.507 23238-23289 CameraManagerGlobal     com.example.aicamera                 I  Connecting to camera service
2025-05-30 14:25:05.577 23238-23289 CameraRepository        com.example.aicamera                 D  Added camera: 0
2025-05-30 14:25:05.638 23238-23253 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=851ms; Flags=0, FrameTimelineVsyncId=31574448, IntendedVsync=580089436960875, Vsync=580090153627556, InputEventId=0, HandleInputStart=580090160442498, AnimationStart=580090160447729, PerformTraversalsStart=580090266226191, DrawStart=580090266490883, FrameDeadline=580089473627542, FrameInterval=580090160198806, FrameStartTime=16666667, SyncQueued=580090279191729, SyncStart=580090279468037, IssueDrawCommandsStart=580090279720037, SwapBuffers=580090282449883, FrameCompleted=580090289202883, DequeueBufferDuration=41462, QueueBufferDuration=1333539, GpuCompleted=580090289202883, SwapBuffersCompleted=580090285042268, DisplayPresentTime=0, 
2025-05-30 14:25:05.640 23238-23289 Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 14:25:05.656 23238-23289 CameraRepository        com.example.aicamera                 D  Added camera: 1
2025-05-30 14:25:05.659 23238-23289 Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 14:25:05.661 23238-23289 CameraValidator         com.example.aicamera                 D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-30 14:25:05.747 23238-23238 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:25:05.748 23238-23238 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:25:05.798 23238-23238 DynamicRangeResolver    com.example.aicamera                 D  Resolved dynamic range for use case androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d5 to no compatible HDR dynamic ranges.
                                                                                                    DynamicRange@ea9714d{encoding=UNSPECIFIED, bitDepth=0}
                                                                                                    ->
                                                                                                    DynamicRange@6f808e4{encoding=SDR, bitDepth=8}
2025-05-30 14:25:05.818 23238-23238 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:25:05.821 23238-23238 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=1, used_surfaces=0](androidx.camera.core.processing.SurfaceEdge$SettableSurface@4553e4e}
2025-05-30 14:25:05.827 23238-23238 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=2, used_surfaces=0](androidx.camera.core.SurfaceRequest$2@3caf15a}
2025-05-30 14:25:05.831 23238-23238 DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@3caf15a}
2025-05-30 14:25:05.831 23238-23238 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.SurfaceRequest$2@3caf15a
2025-05-30 14:25:05.834 23238-23238 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:25:05.846 23238-23238 ImageCapture            com.example.aicamera                 D  createPipeline(cameraId: 0, streamSpec: StreamSpec{resolution=3264x2448, dynamicRange=DynamicRange@6f808e4{encoding=SDR, bitDepth=8}, expectedFrameRateRange=[0, 0], implementationOptions=androidx.camera.camera2.impl.Camera2ImplConfig@816967})
2025-05-30 14:25:05.861 23238-23238 BufferQueueConsumer     com.example.aicamera                 I  [](id:5ac600000001,api:0,p:-1,c:23238) connect: controlledByApp=true
2025-05-30 14:25:05.862 23238-23238 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=3, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@1e17314}
2025-05-30 14:25:05.878 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451 ACTIVE
2025-05-30 14:25:05.881 23238-23238 PreviewView             com.example.aicamera                 D  Surface requested by Preview.
2025-05-30 14:25:05.881 23238-23290 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:25:05.886 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210 ACTIVE
2025-05-30 14:25:05.887 23238-23290 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:25:05.891 23238-23238 SurfaceFactory          com.example.aicamera                 I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@f881e5f
2025-05-30 14:25:05.893 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451 ACTIVE
2025-05-30 14:25:05.894 23238-23290 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:25:05.899 23238-23290 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 14:25:05.901 23238-23238 PreviewView             com.example.aicamera                 D  Preview transformation info updated. TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false}
2025-05-30 14:25:05.902 23238-23238 PreviewTransform        com.example.aicamera                 D  Transformation info set: TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false} 1024x768 false
2025-05-30 14:25:05.903 23238-23238 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 14:25:05.907 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use cases [androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210, androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451] now ATTACHED
2025-05-30 14:25:05.913 23238-23290 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451, androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210] for camera: 0
2025-05-30 14:25:05.915 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  mMeteringRepeating is ATTACHED, SessionConfig Surfaces: 2, CaptureConfig Surfaces: 1
2025-05-30 14:25:05.920 23238-23290 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451, androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210] for camera: 0
2025-05-30 14:25:05.934 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Resetting Capture Session
2025-05-30 14:25:05.936 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Releasing session in state INITIALIZED
2025-05-30 14:25:05.939 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Attempting to force open the camera.
2025-05-30 14:25:05.941 23238-23290 CameraStateRegistry     com.example.aicamera                 D  tryOpenCamera(Camera@39fde97[id=0]) [Available Cameras: 1, Already Open: false (Previous state: null)] --> SUCCESS
2025-05-30 14:25:05.945 23238-23238 SurfaceViewImpl         com.example.aicamera                 D  Wait for new Surface creation.
2025-05-30 14:25:05.945 23238-23290 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@83ab0ee[id=1]                         UNKNOWN               
                                                                                                    Camera@39fde97[id=0]                         OPENING               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 14:25:05.947 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Opening camera.
2025-05-30 14:25:05.948 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Transitioning camera internal state: INITIALIZED --> OPENING
2025-05-30 14:25:05.950 23238-23290 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPENING, error=null} from OPENING and null
2025-05-30 14:25:05.950 23238-23290 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPENING, error=null}
2025-05-30 14:25:05.954 23238-23238 BufferQueueConsumer     com.example.aicamera                 I  [](id:5ac600000002,api:0,p:-1,c:23238) connect: controlledByApp=false
2025-05-30 14:25:05.955 23238-23238 BLASTBufferQueue        com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1] constructor()
2025-05-30 14:25:05.956 23238-23290 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451, androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210] for camera: 0
--------- beginning of system
2025-05-30 14:25:05.957 23238-23238 SurfaceViewImpl         com.example.aicamera                 D  Surface created.
2025-05-30 14:25:05.962 23238-23238 SurfaceViewImpl         com.example.aicamera                 D  Surface changed. Size: 1024x768
2025-05-30 14:25:05.962 23238-23238 SurfaceViewImpl         com.example.aicamera                 D  Surface set on Preview.
2025-05-30 14:25:05.965 23238-23290 libc                    com.example.aicamera                 W  Access denied finding property "persist.vendor.camera.privapp.list"
2025-05-30 14:25:05.961 23238-23238 CameraX-core_ca         com.example.aicamera                 W  type=1400 audit(0.0:6500416): avc: denied { read } for name="u:object_r:vendor_camera_mtk_prop:s0" dev="tmpfs" ino=13626 scontext=u:r:untrusted_app:s0:c234,c258,c512,c768 tcontext=u:object_r:vendor_camera_mtk_prop:s0 tclass=file permissive=0 app=com.example.aicamera
2025-05-30 14:25:06.016 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210 ACTIVE
2025-05-30 14:25:06.018 23238-23290 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451, androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210] for camera: 0
2025-05-30 14:25:06.025 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Use case androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451 ACTIVE
2025-05-30 14:25:06.028 23238-23290 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451, androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210] for camera: 0
2025-05-30 14:25:06.038 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} CameraDevice.onOpened()
2025-05-30 14:25:06.039 23238-23290 Camera2CameraImpl       com.example.aicamera                 D  {Camera@39fde97[id=0]} Transitioning camera internal state: OPENING --> OPENED
2025-05-30 14:25:06.040 23238-23290 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@83ab0ee[id=1]                         UNKNOWN               
                                                                                                    Camera@39fde97[id=0]                         OPEN                  
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 14:25:06.041 23238-23290 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPEN, error=null} from OPEN and null
2025-05-30 14:25:06.041 23238-23290 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPEN, error=null}
2025-05-30 14:25:06.050 23238-23290 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451, androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210] for camera: 0
2025-05-30 14:25:06.063 23238-23290 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-754905e1-de8e-4c23-adcf-7bc118b12efa96983451, androidx.camera.core.Preview-5265281b-ed7c-440f-b273-b41b8e0089d568471210] for camera: 0
2025-05-30 14:25:06.069 23238-23290 SyncCaptureSessionBase  com.example.aicamera                 D  [androidx.camera.camera2.internal.SynchronizedCaptureSessionBaseImpl@56b761] getSurface...done
2025-05-30 14:25:06.070 23238-23290 CaptureSession          com.example.aicamera                 D  Opening capture session.
2025-05-30 14:25:06.083 23238-23290 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=2 androidx.camera.core.SurfaceRequest$2@3caf15a
2025-05-30 14:25:06.083 23238-23290 DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=3, used_surfaces=2](androidx.camera.core.impl.ImmediateSurface@1e17314}
2025-05-30 14:25:06.083 23238-23290 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.impl.ImmediateSurface@1e17314
2025-05-30 14:25:06.201 23238-23290 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:5ac600000002,api:4,p:985,c:23238) connect: api=4 producerControlledByApp=true
2025-05-30 14:25:06.202 23238-23290 BufferQueueProducer     com.example.aicamera                 I  [ImageReader-3264x2448f100m4-23238-0](id:5ac600000001,api:4,p:985,c:23238) connect: api=4 producerControlledByApp=false
2025-05-30 14:25:06.212 23238-23290 CaptureSession          com.example.aicamera                 D  Attempting to send capture request onConfigured
2025-05-30 14:25:06.212 23238-23290 CaptureSession          com.example.aicamera                 D  Issuing request for session.
2025-05-30 14:25:06.214 23238-23290 Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 14:25:06.225 23238-23290 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onConfigured() mState=OPENED
2025-05-30 14:25:06.226 23238-23290 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onReady() OPENED
2025-05-30 14:25:06.571 23238-23290 StreamStateObserver     com.example.aicamera                 D  Update Preview stream state to STREAMING
2025-05-30 14:25:08.170 23238-23264 PythonGradioService     com.example.aicamera                 D  🔗 Connection test result: {'success': True, 'message': 'Successfully connected to HF Space', 'space': 'yamero999/chess-fen-generation-api'}
2025-05-30 14:25:08.171 23238-23264 PythonGradioService     com.example.aicamera                 D  ✅ Python gradio service initialized successfully
2025-05-30 14:25:08.172 23238-23264 PythonGradioService     com.example.aicamera                 D  🔥 Pre-warming API connection...
2025-05-30 14:25:09.070 23238-23264 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 14:25:12.143 23238-23238 XDR::VRT                com.example.aicamera                 I  sc is not valid!
2025-05-30 14:25:12.356 23238-23238 BufferQueueConsumer     com.example.aicamera                 I  [](id:5ac600000003,api:0,p:-1,c:23238) connect: controlledByApp=false
2025-05-30 14:25:12.358 23238-23238 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#2] constructor()
2025-05-30 14:25:12.362 23238-23259 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#2(BLAST Consumer)2](id:5ac600000003,api:1,p:23238,c:23238) connect: api=1 producerControlledByApp=true
2025-05-30 14:25:12.531 23238-23238 ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@3b5aac4[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN
2025-05-30 14:25:12.532 23238-23238 ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@3b5aac4[ComposeMainActivity], mServedView=null
2025-05-30 14:25:12.532 23238-23238 ImeFocusController      com.example.aicamera                 V  checkFocus: view=null next=DecorView@3b5aac4[ComposeMainActivity] force=false package=<none>
2025-05-30 14:25:12.615 23238-23264 PythonGradioService     com.example.aicamera                 D  🏆 EXTREME pre-initialization complete in 14821ms
2025-05-30 14:25:15.391 23238-23238 SurfaceComposerClient   com.example.aicamera                 I  XDR setRegion is not supported 0xb400007c3175e8f0, 1, 0
2025-05-30 14:25:15.391 23238-23238 XDR::SC                 com.example.aicamera                 D  no effect, flags = 1
2025-05-30 14:25:15.407 23238-23259 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#2(BLAST Consumer)2](id:5ac600000003,api:1,p:23238,c:23238) disconnect: api 1
2025-05-30 14:25:15.409 23238-23259 OpenGLRenderer          com.example.aicamera                 D  endAllActiveAnimators on 0xb400007cd186d680 (UnprojectedRipple) with handle 0xb400007be183e010
2025-05-30 14:25:15.424 23238-23412 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#2] destructor()
2025-05-30 14:25:15.424 23238-23412 BufferQueueConsumer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#2(BLAST Consumer)2](id:5ac600000003,api:0,p:-1,c:23238) disconnect
2025-05-30 14:25:15.488 23238-23238 ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@da10fb9[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN
2025-05-30 14:25:15.490 23238-23238 ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@da10fb9[ComposeMainActivity], mServedView=DecorView@da10fb9[ComposeMainActivity]
2025-05-30 14:25:15.490 23238-23238 ImeFocusController      com.example.aicamera                 V  checkFocus: view=DecorView@da10fb9[ComposeMainActivity] next=DecorView@da10fb9[ComposeMainActivity] force=true package=com.example.aicamera
2025-05-30 14:25:15.536 23238-23252 BLASTBufferQueue        com.example.aicamera                 I  releaseBufferCallbackThunk bufferId:99806450024471 framenumber:20 blastBufferQueue is dead
2025-05-30 14:25:15.933 23238-23238 XDR::VRT                com.example.aicamera                 I  sc is not valid!
2025-05-30 14:25:16.065 23238-23238 BufferQueueConsumer     com.example.aicamera                 I  [](id:5ac600000004,api:0,p:-1,c:23238) connect: controlledByApp=false
2025-05-30 14:25:16.066 23238-23238 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#3] constructor()
2025-05-30 14:25:16.071 23238-23259 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#3(BLAST Consumer)3](id:5ac600000004,api:1,p:23238,c:23238) connect: api=1 producerControlledByApp=true
2025-05-30 14:25:16.239 23238-23238 ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@e97c1fe[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN
2025-05-30 14:25:16.240 23238-23238 ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@e97c1fe[ComposeMainActivity], mServedView=null
2025-05-30 14:25:16.240 23238-23238 ImeFocusController      com.example.aicamera                 V  checkFocus: view=null next=DecorView@e97c1fe[ComposeMainActivity] force=false package=<none>
2025-05-30 14:25:17.532 23238-23238 SurfaceComposerClient   com.example.aicamera                 I  XDR setRegion is not supported 0xb400007c3175bd10, 1, 0
2025-05-30 14:25:17.533 23238-23238 XDR::SC                 com.example.aicamera                 D  no effect, flags = 1
2025-05-30 14:25:17.539 23238-23259 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#3(BLAST Consumer)3](id:5ac600000004,api:1,p:23238,c:23238) disconnect: api 1
2025-05-30 14:25:17.542 23238-23259 OpenGLRenderer          com.example.aicamera                 D  endAllActiveAnimators on 0xb400007cd1877100 (UnprojectedRipple) with handle 0xb400007be183e730
2025-05-30 14:25:17.556 23238-23412 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#3] destructor()
2025-05-30 14:25:17.557 23238-23412 BufferQueueConsumer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#3(BLAST Consumer)3](id:5ac600000004,api:0,p:-1,c:23238) disconnect
2025-05-30 14:25:17.602 23238-23238 ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@da10fb9[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN
2025-05-30 14:25:17.602 23238-23238 ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@da10fb9[ComposeMainActivity], mServedView=DecorView@da10fb9[ComposeMainActivity]
2025-05-30 14:25:17.603 23238-23238 ImeFocusController      com.example.aicamera                 V  checkFocus: view=DecorView@da10fb9[ComposeMainActivity] next=DecorView@da10fb9[ComposeMainActivity] force=true package=com.example.aicamera
2025-05-30 14:25:17.669 23238-23249 BLASTBufferQueue        com.example.aicamera                 I  releaseBufferCallbackThunk bufferId:99806450024476 framenumber:21 blastBufferQueue is dead
