package com.example.aicamera.ui.screens

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.aicamera.chess.SimpleFENBoard
import com.example.aicamera.chess.ChessGameState
import com.example.aicamera.chess.PieceTray
import com.example.aicamera.chess.EditModeControls
import com.example.aicamera.chess.ChessPiece
import com.github.bhlangonijr.chesslib.Board
import com.example.aicamera.ui.theme.getDynamicColorScheme

/**
 * External app integration functions
 */
fun openChessisApp(context: Context, fen: String) {
    try {
        // Try to open Chessis app directly to Board Editor
        val intent = Intent().apply {
            action = Intent.ACTION_MAIN
            setClassName("com.chessis.analysis", "com.chessis.analysis.BoardEditorActivity")
            putExtra("fen", fen)
            putExtra("mode", "editor")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        context.startActivity(intent)
    } catch (e: Exception) {
        try {
            // Fallback: Try generic intent to Chessis
            val intent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, "FEN: $fen")
                putExtra(Intent.EXTRA_SUBJECT, "Chess Position")
                setPackage("com.chessis.analysis")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e2: Exception) {
            // Last fallback: try to open any chess app
            openAnyChessApp(context, fen)
        }
    }
}

fun openAnyChessApp(context: Context, fen: String) {
    try {
        // Generic intent for chess apps
        val intent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, "FEN: $fen")
            putExtra(Intent.EXTRA_SUBJECT, "Chess Position")
        }
        val chooser = Intent.createChooser(intent, "Open with Chess App")
        context.startActivity(chooser)
    } catch (e: Exception) {
        // Last resort: copy to clipboard
        copyFenToClipboard(context, fen)
    }
}

fun copyFenToClipboard(context: Context, fen: String) {
    val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
    val clip = android.content.ClipData.newPlainText("Chess FEN", fen)
    clipboard.setPrimaryClip(clip)
    // Show toast or snackbar here if needed
}

/**
 * Chess Board Screen - Displays the chess board with FEN from camera
 * Features interactive board, move history, and analysis integration
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChessBoardScreen(
    fen: String?,
    onBackPressed: () -> Unit,
    onAnalyzePosition: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val dynamicColorScheme = getDynamicColorScheme()
    var currentFEN by remember { mutableStateOf(fen ?: "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1") }
    var editableFEN by remember { mutableStateOf(currentFEN) }
    var isEditingFEN by remember { mutableStateOf(false) }
    var fenError by remember { mutableStateOf<String?>(null) }
    var moveHistory by remember { mutableStateOf(listOf<String>()) }
    var isEditMode by remember { mutableStateOf(false) }
    var selectedPiece by remember { mutableStateOf<ChessPiece?>(null) }

    // Function to convert AI piece-only FEN to complete FEN
    fun completeAIFen(pieceFen: String, turn: String = "w", castling: String = "KQkq", enPassant: String = "-"): String {
        // If already complete FEN, return as is
        if (pieceFen.split(" ").size >= 4) {
            return pieceFen
        }

        // AI returns only piece positions, add the rest with defaults or user choices
        val halfMove = "0"
        val fullMove = "1"

        return "$pieceFen $turn $castling $enPassant $halfMove $fullMove"
    }

    // Create complete FEN with default values
    val completeFEN = remember(currentFEN) {
        completeAIFen(currentFEN)
    }

    val board = remember(completeFEN) {
        try {
            Board().apply {
                loadFromFen(completeFEN)
            }
        } catch (e: Exception) {
            // If FEN is invalid, return default board
            Board()
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = dynamicColorScheme.background,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Chess Board",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.SemiBold
                        )
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = { onAnalyzePosition(currentFEN) }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Analytics,
                            contentDescription = "Analyze",
                            tint = dynamicColorScheme.primary
                        )
                    }
                    IconButton(
                        onClick = {
                            // Reset to starting position
                            currentFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
                            moveHistory = emptyList()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.RestartAlt,
                            contentDescription = "Reset"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = dynamicColorScheme.surface,
                    titleContentColor = dynamicColorScheme.onSurface
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Chess Board
            item {
                SimpleFENBoard(
                    fen = currentFEN,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Position Information
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.surface
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Position Information",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = dynamicColorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "FEN:",
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = if (fen != null) "From Camera" else "Default",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = dynamicColorScheme.primary
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                IconButton(
                                    onClick = {
                                        isEditingFEN = !isEditingFEN
                                        if (isEditingFEN) {
                                            editableFEN = currentFEN
                                        }
                                    }
                                ) {
                                    Icon(
                                        imageVector = if (isEditingFEN) Icons.Default.Check else Icons.Default.Edit,
                                        contentDescription = if (isEditingFEN) "Save FEN" else "Edit FEN",
                                        tint = dynamicColorScheme.primary
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        if (isEditingFEN) {
                            // Editable FEN input
                            OutlinedTextField(
                                value = editableFEN,
                                onValueChange = { newFEN ->
                                    editableFEN = newFEN
                                    // Validate FEN in real-time
                                    try {
                                        Board().loadFromFen(newFEN)
                                        fenError = null
                                        currentFEN = newFEN
                                    } catch (e: Exception) {
                                        fenError = "Invalid FEN: ${e.message}"
                                    }
                                },
                                label = { Text("FEN Position") },
                                isError = fenError != null,
                                supportingText = {
                                    fenError?.let { error ->
                                        Text(
                                            text = error,
                                            color = MaterialTheme.colorScheme.error
                                        )
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                textStyle = MaterialTheme.typography.bodySmall.copy(
                                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                )
                            )
                        } else {
                            // Display-only FEN
                            Text(
                                text = currentFEN,
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                ),
                                color = dynamicColorScheme.onSurfaceVariant,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(
                                        dynamicColorScheme.surface.copy(alpha = 0.3f),
                                        RoundedCornerShape(8.dp)
                                    )
                                    .padding(12.dp)
                            )
                        }
                    }
                }
            }

            // Edit Mode Controls
            item {
                EditModeControls(
                    isEditMode = isEditMode,
                    onToggleEditMode = {
                        isEditMode = !isEditMode
                        selectedPiece = null
                    },
                    onClearBoard = {
                        currentFEN = "8/8/8/8/8/8/8/8 w - - 0 1"
                        editableFEN = currentFEN
                    },
                    onResetBoard = {
                        currentFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
                        editableFEN = currentFEN
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }



            // External Chess Apps
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "Open in Chess App",
                            style = MaterialTheme.typography.titleSmall.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = dynamicColorScheme.onPrimaryContainer,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        // External app buttons
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Button(
                                onClick = { openChessisApp(context, completeFEN) },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = dynamicColorScheme.primary
                                )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = "Chessis Board Editor",
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("Chessis", style = MaterialTheme.typography.bodySmall)
                            }

                            OutlinedButton(
                                onClick = { openAnyChessApp(context, completeFEN) },
                                modifier = Modifier.weight(1f)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Share,
                                    contentDescription = "Other Apps",
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("Other", style = MaterialTheme.typography.bodySmall)
                            }
                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        // Copy FEN button
                        OutlinedButton(
                            onClick = { copyFenToClipboard(context, completeFEN) },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(
                                imageVector = Icons.Default.ContentCopy,
                                contentDescription = "Copy FEN",
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Copy FEN to Clipboard", style = MaterialTheme.typography.bodySmall)
                        }
                    }
                }
            }

            // Piece Tray (only in edit mode)
            if (isEditMode) {
                item {
                    PieceTray(
                        selectedPiece = selectedPiece,
                        onPieceSelected = { piece ->
                            selectedPiece = piece
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            // Move History
            if (moveHistory.isNotEmpty()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = dynamicColorScheme.surfaceVariant
                        ),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Move History",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.SemiBold
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                            Spacer(modifier = Modifier.height(8.dp))

                            moveHistory.forEachIndexed { index, move ->
                                Text(
                                    text = "${index + 1}. $move",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = dynamicColorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(vertical = 2.dp)
                                )
                            }
                        }
                    }
                }
            }



            // Instructions
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer.copy(alpha = 0.3f)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = "Info",
                                tint = dynamicColorScheme.primary,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "How to use",
                                style = MaterialTheme.typography.titleSmall.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = dynamicColorScheme.onSurface
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "• Use camera to scan chess boards\n• AI detects piece positions automatically\n• Tap 'Chessis' to open Board Editor with position\n• Use external app for analysis and editing",
                            style = MaterialTheme.typography.bodySmall,
                            color = dynamicColorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}
