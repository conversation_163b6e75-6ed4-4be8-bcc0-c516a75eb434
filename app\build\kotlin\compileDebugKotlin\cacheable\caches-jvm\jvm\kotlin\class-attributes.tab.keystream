com.example.aicamera.AiResponsecom.example.aicamera.ApiConfig*com.example.aicamera.ApiConfig.ApiProvider*com.example.aicamera.ApiConfig.LocalServer+com.example.aicamera.ApiConfig.PythonBridge*com.example.aicamera.ApiConfig.HuggingFace&com.example.aicamera.ApiConfig.Network"com.example.aicamera.CameraManager,com.example.aicamera.CameraManager.Companion%com.example.aicamera.ChessApplication/com.example.aicamera.ChessApplication.Companion(com.example.aicamera.ComposeMainActivity!com.example.aicamera.MainActivity+com.example.aicamera.MainActivity.Companion#com.example.aicamera.NetworkManager-com.example.aicamera.NetworkManager.Companion"com.example.aicamera.NetworkResult*com.example.aicamera.NetworkResult.Success(com.example.aicamera.NetworkResult.Error*com.example.aicamera.NetworkResult.Loading(com.example.aicamera.PythonGradioService2com.example.aicamera.PythonGradioService.Companion%com.example.aicamera.SettingsActivity/com.example.aicamera.SettingsActivity.Companion0com.example.aicamera.chess.ChessAnalysisActivity:com.example.aicamera.chess.ChessAnalysisActivity.Companion.com.example.aicamera.chess.ChessBoardInterface*com.example.aicamera.chess.ChessBoardUtils)com.example.aicamera.chess.ChessGameState#com.example.aicamera.chess.GameMode)com.example.aicamera.chess.EngineAnalysis'com.example.aicamera.chess.AnalysisLine%com.example.aicamera.chess.ChessPiece/com.example.aicamera.chess.ChessPiece.Companion$com.example.aicamera.chess.PieceType%com.example.aicamera.chess.PieceColor+com.example.aicamera.chess.BoardOrientation%com.example.aicamera.chess.GameResult)com.example.aicamera.chess.GameResultType%com.example.aicamera.chess.PlayOption%com.example.aicamera.chess.RecentGame)com.example.aicamera.chess.LearningModule%com.example.aicamera.chess.ScanResult'com.example.aicamera.chess.PositionInfo)com.example.aicamera.chess.ChessViewModel3com.example.aicamera.chess.ChessViewModel.Companion+com.example.aicamera.chess.StockfishManager5com.example.aicamera.chess.StockfishManager.Companion/com.example.aicamera.chess.ui.theme.ChessColors4com.example.aicamera.databinding.ActivityMainBinding8com.example.aicamera.databinding.ActivitySettingsBinding3com.example.aicamera.integration.ExternalAppManager=com.example.aicamera.integration.ExternalAppManager.Companion7com.example.aicamera.preferences.ExternalAppPreferencesAcom.example.aicamera.preferences.ExternalAppPreferences.Companion1com.example.aicamera.preferences.ExternalChessApp3com.example.aicamera.preferences.SupportedChessApps)com.example.aicamera.utils.GalleryManager3com.example.aicamera.utils.GalleryManager.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     