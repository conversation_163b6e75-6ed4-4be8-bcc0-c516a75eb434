package com.example.aicamera

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Apps
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.aicamera.ui.screens.CameraScreen
import com.example.aicamera.ui.screens.ChessBoardScreen
import com.example.aicamera.ui.screens.HomeScreen
import com.example.aicamera.ui.screens.AppSelectionScreen
import com.example.aicamera.preferences.ExternalAppPreferences
import com.example.aicamera.ui.theme.ChessTheme

/**
 * New Compose-based MainActivity with Chess.com inspired UI
 * Replaces the old View-based MainActivity with modern Compose design
 */
class ComposeMainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Load settings from preferences
        SettingsActivity.loadSettingsOnStartup(this)

        setContent {
            ChessTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    ChessApp()
                }
            }
        }
    }
}

@Composable
fun ChessApp() {
    val navController = rememberNavController()
    var capturedFEN by remember { mutableStateOf<String?>(null) }

    // Check if this is first launch
    val context = androidx.compose.ui.platform.LocalContext.current
    val appPreferences = remember { ExternalAppPreferences(context) }

    // Determine start destination based on first launch
    val startDestination = if (appPreferences.isFirstLaunch) "app_selection" else "home"

    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // App Selection Screen (First Launch)
        composable("app_selection") {
            AppSelectionScreen(
                onAppSelected = { selectedAppId ->
                    appPreferences.updateSelectedApp(selectedAppId)
                    appPreferences.setFirstLaunchCompleted()
                    navController.navigate("home") {
                        popUpTo("app_selection") { inclusive = true }
                    }
                },
                onSkip = {
                    appPreferences.setFirstLaunchCompleted()
                    navController.navigate("home") {
                        popUpTo("app_selection") { inclusive = true }
                    }
                }
            )
        }
        composable("home") {
            HomeScreen(
                onNavigateToCamera = {
                    navController.navigate("camera")
                },
                onNavigateToAnalysis = {
                    navController.navigate("chessboard")
                },
                onNavigateToSettings = {
                    navController.navigate("settings")
                }
            )
        }

        composable("camera") {
            CameraScreen(
                onBackPressed = {
                    navController.popBackStack()
                },
                onFENGenerated = { fen ->
                    capturedFEN = fen
                    // Navigate to chess board screen with FEN
                    navController.navigate("chessboard")
                }
            )
        }

        composable("chessboard") {
            ChessBoardScreen(
                fen = capturedFEN,
                onBackPressed = {
                    navController.popBackStack()
                },
                onAnalyzePosition = { fen ->
                    // Could integrate with Stockfish analysis here
                },
                onNavigateToAppSelection = {
                    navController.navigate("app_selection")
                }
            )
        }

        composable("analysis") {
            AnalysisScreen(
                fen = capturedFEN,
                onBackPressed = {
                    navController.popBackStack()
                }
            )
        }

        composable("settings") {
            SettingsScreen(
                onBackPressed = {
                    navController.popBackStack()
                },
                onChangeApp = {
                    navController.navigate("app_selection")
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnalysisScreen(
    fen: String?,
    onBackPressed: () -> Unit
) {
    // Placeholder for analysis screen
    // This would integrate with your existing ChessAnalysisActivity
    Column {
        TopAppBar(
            title = { Text("Analysis") },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            }
        )

        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (fen != null) "FEN: $fen" else "No FEN available",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackPressed: () -> Unit,
    onChangeApp: () -> Unit = {}
) {
    Column {
        TopAppBar(
            title = { Text("Settings") },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            }
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                onClick = onChangeApp
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Apps,
                        contentDescription = "Change App"
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Text(
                            text = "Preferred Chess App",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "Choose which app to open for analysis",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}
