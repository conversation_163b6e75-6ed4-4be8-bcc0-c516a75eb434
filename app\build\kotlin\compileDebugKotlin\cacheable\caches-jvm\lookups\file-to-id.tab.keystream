4app/src/main/java/com/example/aicamera/AiResponse.kt3app/src/main/java/com/example/aicamera/ApiConfig.kt7app/src/main/java/com/example/aicamera/CameraManager.kt:app/src/main/java/com/example/aicamera/ChessApplication.kt=app/src/main/java/com/example/aicamera/ComposeMainActivity.kt6app/src/main/java/com/example/aicamera/MainActivity.kt8app/src/main/java/com/example/aicamera/NetworkManager.kt=app/src/main/java/com/example/aicamera/PythonGradioService.kt:app/src/main/java/com/example/aicamera/SettingsActivity.ktEapp/src/main/java/com/example/aicamera/chess/ChessAnalysisActivity.ktAapp/src/main/java/com/example/aicamera/chess/ChessBoardWebView.kt;app/src/main/java/com/example/aicamera/chess/ChessModels.ktAapp/src/main/java/com/example/aicamera/chess/ChessUIComponents.kt>app/src/main/java/com/example/aicamera/chess/ChessViewModel.kt@app/src/main/java/com/example/aicamera/chess/StockfishManager.ktCapp/src/main/java/com/example/aicamera/chess/ui/theme/ChessTheme.ktAapp/src/main/java/com/example/aicamera/ui/screens/CameraScreen.kt?app/src/main/java/com/example/aicamera/ui/screens/HomeScreen.kt8app/src/main/java/com/example/aicamera/ui/theme/Color.kt8app/src/main/java/com/example/aicamera/ui/theme/Theme.kt7app/src/main/java/com/example/aicamera/ui/theme/Type.ktAapp/src/main/java/com/example/aicamera/chess/ChessBoardCompose.ktEapp/src/main/java/com/example/aicamera/ui/screens/ChessBoardScreen.kt9app/src/main/java/com/example/aicamera/chess/PieceTray.ktHapp/src/main/java/com/example/aicamera/integration/ExternalAppManager.ktLapp/src/main/java/com/example/aicamera/preferences/ExternalAppPreferences.ktGapp/src/main/java/com/example/aicamera/ui/screens/AppSelectionScreen.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       