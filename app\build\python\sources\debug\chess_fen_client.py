"""
Chess FEN Generation Client using real gradio_client library
This runs directly on Android using Chaquopy (Python for Android)
"""

import os
import json
import base64
import tempfile
from gradio_client import Client, handle_file

# Configuration
HF_SPACE = "yamero999/chess-fen-generation-api"
HF_TOKEN = "*************************************"

def initialize_client():
    """Initialize the gradio client with HF token"""
    try:
        # Set HF token as environment variable for authentication
        os.environ['HF_TOKEN'] = HF_TOKEN

        # Initialize client with HF Space
        client = Client(HF_SPACE)
        return client, None

    except Exception as e:
        return None, str(e)

def generate_fen_from_base64(base64_image_data):
    """
    Generate FEN from base64 encoded chess board image

    Args:
        base64_image_data (str): Base64 encoded image data

    Returns:
        dict: Result containing success status, FEN, and any errors
    """
    try:
        # Initialize gradio client
        client, error = initialize_client()
        if client is None:
            return {
                "success": False,
                "error": f"Failed to initialize gradio client: {error}",
                "fen": None
            }

        # Handle data URI prefix if present
        if base64_image_data.startswith('data:image/'):
            base64_image_data = base64_image_data.split(',')[1]

        # Decode base64 image and save to temporary file
        try:
            image_bytes = base64.b64decode(base64_image_data)

            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
                tmp_file.write(image_bytes)
                temp_image_path = tmp_file.name

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to decode base64 image: {str(e)}",
                "fen": None
            }

        # Use gradio_client to call the HF Space
        try:
            # Use the exact format from the HF Space API documentation
            result = client.predict(
                image=handle_file(temp_image_path),
                api_name="/predict"
            )

            # Clean up temporary file
            os.unlink(temp_image_path)

            # Parse the result
            try:
                if isinstance(result, str):
                    # Try to parse as JSON
                    try:
                        result_data = json.loads(result)
                        if isinstance(result_data, dict):
                            fen = result_data.get('fen')
                            success = result_data.get('success', True)

                            return {
                                "success": success,
                                "fen": fen,
                                "error": result_data.get('error') if not success else None,
                                "raw_result": result_data
                            }
                    except json.JSONDecodeError:
                        # If not JSON, treat as plain FEN string
                        pass

                    # Treat as plain FEN string
                    return {
                        "success": True,
                        "fen": result.strip(),
                        "error": None,
                        "raw_result": result
                    }
                else:
                    # Handle other result types
                    return {
                        "success": True,
                        "fen": str(result).strip(),
                        "error": None,
                        "raw_result": result
                    }

            except Exception as e:
                return {
                    "success": False,
                    "error": f"Failed to parse result: {str(e)}",
                    "fen": None,
                    "raw_result": result
                }

        except Exception as e:
            # Clean up temporary file
            if os.path.exists(temp_image_path):
                os.unlink(temp_image_path)

            return {
                "success": False,
                "error": f"Gradio client call failed: {str(e)}",
                "fen": None
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "fen": None
        }

def test_connection():
    """Test if the gradio client can connect to the HF Space"""
    try:
        client, error = initialize_client()
        if client is None:
            return {
                "success": False,
                "error": error
            }

        return {
            "success": True,
            "message": "Successfully connected to HF Space",
            "space": HF_SPACE
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# Main function for testing
if __name__ == "__main__":
    # Test connection
    test_result = test_connection()
    print("Connection test:", test_result)
