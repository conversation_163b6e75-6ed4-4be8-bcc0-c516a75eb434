package com.chessvision.app.ui.screens

import android.Manifest
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.chessvision.app.ui.theme.getDynamicColorScheme
import com.chessvision.app.ai.ChessAI
import com.chessvision.app.ai.ChessAnalysisResult
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * Camera Screen - Chess.com inspired camera interface for chess board scanning
 * Features 416x416 preview optimized for AI processing with enhanced UI
 */
@Composable
fun CameraScreen(
    onBackPressed: () -> Unit,
    onImageCaptured: (String) -> Unit,
    onFENGenerated: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val dynamicColorScheme = getDynamicColorScheme()
    val scope = rememberCoroutineScope()

    var isProcessing by remember { mutableStateOf(false) }
    var capturedFEN by remember { mutableStateOf<String?>(null) }
    var hasCameraPermission by remember { mutableStateOf(false) }
    var isFlashOn by remember { mutableStateOf(false) }
    var isAIProcessing by remember { mutableStateOf(false) }
    var aiStatus by remember { mutableStateOf("Initializing AI...") }
    var showAIError by remember { mutableStateOf(false) }
    var aiErrorMessage by remember { mutableStateOf("") }

    // Initialize Chess AI
    val chessAI = remember { ChessAI(context) }
    var isAIInitialized by remember { mutableStateOf(false) }

    val cameraExecutor: ExecutorService = remember { Executors.newSingleThreadExecutor() }
    val previewView = remember { PreviewView(context) }
    var imageCapture: ImageCapture? by remember { mutableStateOf(null) }
    var camera: Camera? by remember { mutableStateOf(null) }

    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasCameraPermission = isGranted
    }

    LaunchedEffect(Unit) {
        permissionLauncher.launch(Manifest.permission.CAMERA)

        // Initialize AI models
        scope.launch {
            aiStatus = "Initializing AI models..."
            isAIInitialized = chessAI.initializeModels()
            aiStatus = if (isAIInitialized) "AI Ready" else "AI Initialization Failed"
        }
    }

    LaunchedEffect(hasCameraPermission) {
        if (hasCameraPermission) {
            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
            cameraProviderFuture.addListener({
                val cameraProvider = cameraProviderFuture.get()

                val preview = Preview.Builder().build().also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }

                imageCapture = ImageCapture.Builder()
                    .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                    .build()

                val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

                try {
                    cameraProvider.unbindAll()
                    camera = cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageCapture
                    )
                } catch (exc: Exception) {
                    // Handle camera binding error
                }
            }, ContextCompat.getMainExecutor(context))
        }
    }

    if (!hasCameraPermission) {
        // Permission request UI
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(dynamicColorScheme.background),
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier.padding(32.dp),
                colors = CardDefaults.cardColors(
                    containerColor = dynamicColorScheme.surface
                ),
                shape = RoundedCornerShape(24.dp)
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.CameraAlt,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = dynamicColorScheme.primary
                    )
                    Text(
                        text = "Camera Permission Required",
                        style = MaterialTheme.typography.headlineSmall,
                        color = dynamicColorScheme.onSurface
                    )
                    Text(
                        text = "Please grant camera permission to scan chess boards",
                        style = MaterialTheme.typography.bodyMedium,
                        color = dynamicColorScheme.onSurfaceVariant
                    )
                    Button(
                        onClick = { permissionLauncher.launch(Manifest.permission.CAMERA) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = dynamicColorScheme.primary
                        )
                    ) {
                        Text("Grant Permission")
                    }
                }
            }
        }
        return
    }

    // Chess.com inspired camera layout with integrated preview and controls
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(dynamicColorScheme.background)
    ) {
        // Top App Bar - Chess.com style
        TopAppBar(
            title = {
                Text(
                    text = "Scan Chess Board",
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.SemiBold
                    )
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            actions = {
                IconButton(
                    onClick = {
                        camera?.let { cam ->
                            isFlashOn = !isFlashOn
                            cam.cameraControl.enableTorch(isFlashOn)
                        }
                    }
                ) {
                    Icon(
                        imageVector = if (isFlashOn) Icons.Default.FlashOn else Icons.Default.FlashOff,
                        contentDescription = "Flash",
                        tint = if (isFlashOn) dynamicColorScheme.primary else dynamicColorScheme.onSurface
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = dynamicColorScheme.surface,
                titleContentColor = dynamicColorScheme.onSurface
            )
        )

        // Main Content Area
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Camera Preview Card - 416x416 optimized
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f), // Square aspect ratio for chess board
                colors = CardDefaults.cardColors(
                    containerColor = dynamicColorScheme.surface
                ),
                shape = RoundedCornerShape(20.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // Camera Preview
                    AndroidView(
                        factory = { previewView },
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(20.dp))
                    )

                    // Chess Board Guide Overlay
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(24.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize(0.85f)
                                .background(Color.Transparent)
                                .border(
                                    2.dp,
                                    Color.White.copy(alpha = 0.8f),
                                    RoundedCornerShape(12.dp)
                                )
                        )
                    }

                    // Guide Text
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.TopCenter
                    ) {
                        Text(
                            text = "Align chess board within frame",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.White,
                            modifier = Modifier
                                .background(
                                    Color.Black.copy(alpha = 0.7f),
                                    RoundedCornerShape(8.dp)
                                )
                                .padding(horizontal = 12.dp, vertical = 6.dp)
                        )
                    }
                }
            }

            // FEN Output Card - Chess.com style
            if (capturedFEN != null) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = dynamicColorScheme.primaryContainer
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Generated FEN",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold,
                                color = dynamicColorScheme.onPrimaryContainer
                            )
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = capturedFEN!!,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                                color = dynamicColorScheme.onPrimaryContainer
                            ),
                            modifier = Modifier
                                .background(
                                    dynamicColorScheme.surface.copy(alpha = 0.3f),
                                    RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp)
                        )
                    }
                }
            }

            // AI Status Card
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = dynamicColorScheme.surface
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (isAIProcessing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = dynamicColorScheme.primary
                        )
                    } else {
                        Icon(
                            imageVector = if (isAIInitialized) Icons.Default.CheckCircle else Icons.Default.Error,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                            tint = if (isAIInitialized) dynamicColorScheme.primary else dynamicColorScheme.error
                        )
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = aiStatus,
                        style = MaterialTheme.typography.bodyMedium,
                        color = dynamicColorScheme.onSurface
                    )
                }
            }

            // Action Buttons - Chess.com style
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Gallery Button
                OutlinedButton(
                    onClick = { /* TODO: Open gallery */ },
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.PhotoLibrary,
                        contentDescription = "Gallery",
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Gallery")
                }

                // Capture Button
                Button(
                    onClick = {
                        if (!isProcessing && isAIInitialized) {
                            isProcessing = true
                            val outputDirectory = File(context.filesDir, "chess_images")
                            if (!outputDirectory.exists()) {
                                outputDirectory.mkdirs()
                            }

                            val photoFile = File(
                                outputDirectory,
                                SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
                                    .format(System.currentTimeMillis()) + ".jpg"
                            )

                            val outputFileOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

                            imageCapture?.takePicture(
                                outputFileOptions,
                                ContextCompat.getMainExecutor(context),
                                object : ImageCapture.OnImageSavedCallback {
                                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                                        isProcessing = false

                                        // Start AI processing
                                        isAIProcessing = true
                                        aiStatus = "Processing image..."

                                        scope.launch {
                                            try {
                                                val result = chessAI.generateFEN(photoFile.absolutePath)

                                                when (result) {
                                                    is ChessAnalysisResult.Success -> {
                                                        aiStatus = "FEN Generated Successfully"
                                                        capturedFEN = result.fen
                                                        onFENGenerated(result.fen)
                                                        onImageCaptured(photoFile.absolutePath)
                                                    }
                                                    is ChessAnalysisResult.Error -> {
                                                        aiStatus = "AI Processing Failed"
                                                        aiErrorMessage = result.message
                                                        showAIError = true
                                                        // Still navigate to board for manual editing
                                                        onImageCaptured(photoFile.absolutePath)
                                                    }
                                                }
                                            } catch (e: Exception) {
                                                aiStatus = "AI Processing Error"
                                                aiErrorMessage = "Unexpected error: ${e.message}"
                                                showAIError = true
                                                onImageCaptured(photoFile.absolutePath)
                                            } finally {
                                                isAIProcessing = false
                                            }
                                        }
                                    }

                                    override fun onError(exception: ImageCaptureException) {
                                        isProcessing = false
                                        aiErrorMessage = "Camera error: ${exception.message}"
                                        showAIError = true
                                    }
                                }
                            )
                        }
                    },
                    modifier = Modifier.weight(2f),
                    enabled = !isProcessing && isAIInitialized,
                    shape = RoundedCornerShape(12.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = dynamicColorScheme.primary
                    )
                ) {
                    if (isProcessing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(18.dp),
                            color = dynamicColorScheme.onPrimary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Processing...")
                    } else {
                        Icon(
                            imageVector = Icons.Default.CameraAlt,
                            contentDescription = "Capture",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Capture Board")
                    }
                }
            }
        }

        // AI Error Dialog
        if (showAIError) {
            AlertDialog(
                onDismissRequest = { showAIError = false },
                title = {
                    Text(
                        text = "AI Processing Error",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                text = {
                    Text(
                        text = aiErrorMessage,
                        style = MaterialTheme.typography.bodyMedium
                    )
                },
                confirmButton = {
                    TextButton(
                        onClick = { showAIError = false }
                    ) {
                        Text("OK")
                    }
                },
                shape = RoundedCornerShape(16.dp)
            )
        }
    }
}
