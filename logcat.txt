--------- beginning of main
---------------------------- PROCESS STARTED (5842) for package com.example.aicamera ----------------------------
2025-05-30 11:38:30.522  5842-5842  ziparchive              com.example.aicamera                 W  Unable to open '/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.dm': No such file or directory
2025-05-30 11:38:30.522  5842-5842  ziparchive              com.example.aicamera                 W  Unable to open '/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.dm': No such file or directory
2025-05-30 11:38:31.792  5842-5842  nativeloader            com.example.aicamera                 D  Configuring clns-4 for other apk /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/lib/arm64:/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.example.aicamera
2025-05-30 11:38:31.832  5842-5842  GraphicsEnvironment     com.example.aicamera                 V  ANGLE Developer option for 'com.example.aicamera' set to: 'default'
2025-05-30 11:38:31.832  5842-5842  GraphicsEnvironment     com.example.aicamera                 V  Neither updatable production driver nor prerelease driver is supported.
2025-05-30 11:38:31.841  5842-5842  NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 11:38:31.844  5842-5842  NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 11:38:31.880  5842-5842  libc                    com.example.aicamera                 W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-30 11:38:32.340  5842-5842  xample.aicamer          com.example.aicamera                 E  Invalid ID 0x00000000.
2025-05-30 11:38:32.705  5842-5842  Choreographer           com.example.aicamera                 I  Skipped 49 frames!  The application may be doing too much work on its main thread.
2025-05-30 11:38:32.712  5842-5842  XDR::VRT                com.example.aicamera                 I  sc is not valid!
2025-05-30 11:38:33.563  5842-5842  xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateMap.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-30 11:38:33.563  5842-5842  xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateMap.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 11:38:33.565  5842-5842  xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateMap.removeIf$runtime_release(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 11:38:34.384  5842-5842  xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 11:38:34.385  5842-5842  xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 11:38:34.386  5842-5842  xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 11:38:34.758  5842-5842  BufferQueueConsumer     com.example.aicamera                 I  [](id:16d200000000,api:0,p:-1,c:5842) connect: controlledByApp=false
2025-05-30 11:38:34.764  5842-5842  BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0] constructor()
2025-05-30 11:38:34.791  5842-5862  hw-ProcessState         com.example.aicamera                 D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-30 11:38:34.821  5842-5862  BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:16d200000000,api:1,p:5842,c:5842) connect: api=1 producerControlledByApp=true
2025-05-30 11:38:34.835  5842-5919  ion                     com.example.aicamera                 E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-30 11:38:35.417  5842-5848  xample.aicamer          com.example.aicamera                 I  Background concurrent copying GC freed 89KB AllocSpace bytes, 0(0B) LOS objects, 34% free, 3970KB/6018KB, paused 224us,48us total 109.953ms
2025-05-30 11:38:35.519  5842-5847  xample.aicamer          com.example.aicamera                 I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-30 11:38:35.890  5842-5842  Compatibil...geReporter com.example.aicamera                 D  Compat change id reported: 171228096; UID 10731; state: ENABLED
2025-05-30 11:38:37.226  5842-5862  OpenGLRenderer          com.example.aicamera                 E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-30 11:38:37.227  5842-5862  OpenGLRenderer          com.example.aicamera                 E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-30 11:38:37.246  5842-5858  OpenGLRenderer          com.example.aicamera                 I  Davey! duration=5333ms; Flags=1, FrameTimelineVsyncId=31239244, IntendedVsync=570096544549680, Vsync=570097361216363, InputEventId=0, HandleInputStart=570097364984749, AnimationStart=570097365031133, PerformTraversalsStart=570097365781903, DrawStart=570101648373980, FrameDeadline=570096564549680, FrameInterval=570097363617980, FrameStartTime=16666667, SyncQueued=570101793498518, SyncStart=570101809017288, IssueDrawCommandsStart=570101811847749, SwapBuffers=570101883837980, FrameCompleted=570101893800057, DequeueBufferDuration=0, QueueBufferDuration=4291077, GpuCompleted=570101892802903, SwapBuffersCompleted=570101893800057, DisplayPresentTime=1898760152, 
2025-05-30 11:38:37.551  5842-5842  ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@36fe229[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-30 11:38:37.551  5842-5842  ImeFocusController      com.example.aicamera                 V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-30 11:38:37.552  5842-5842  ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@36fe229[ComposeMainActivity], mServedView=null
2025-05-30 11:38:37.552  5842-5842  ImeFocusController      com.example.aicamera                 V  checkFocus: view=null next=DecorView@36fe229[ComposeMainActivity] force=true package=<none>
2025-05-30 11:38:38.049  5842-5933  ProfileInstaller        com.example.aicamera                 D  Installing profile for com.example.aicamera
2025-05-30 11:38:40.432  5842-5848  xample.aicamer          com.example.aicamera                 I  Background concurrent copying GC freed 154KB AllocSpace bytes, 2(48KB) LOS objects, 26% free, 5732KB/7780KB, paused 222us,84us total 100.747ms
2025-05-30 11:38:41.258  5842-5852  OpenGLRenderer          com.example.aicamera                 I  Davey! duration=1295ms; Flags=0, FrameTimelineVsyncId=31239377, IntendedVsync=570104609845016, Vsync=570105009845024, InputEventId=0, HandleInputStart=570105014975519, AnimationStart=570105014980749, PerformTraversalsStart=570105676610288, DrawStart=570105822393365, FrameDeadline=570104646511683, FrameInterval=570105014949365, FrameStartTime=16666667, SyncQueued=570105879049903, SyncStart=570105879541980, IssueDrawCommandsStart=570105880566288, SwapBuffers=570105897384365, FrameCompleted=570105905886903, DequeueBufferDuration=47769, QueueBufferDuration=2085308, GpuCompleted=570105905886903, SwapBuffersCompleted=570105901025903, DisplayPresentTime=0, 
2025-05-30 11:38:41.295  5842-5941  CameraManagerGlobal     com.example.aicamera                 I  Connecting to camera service
2025-05-30 11:38:41.302  5842-5842  Choreographer           com.example.aicamera                 I  Skipped 56 frames!  The application may be doing too much work on its main thread.
2025-05-30 11:38:41.365  5842-5941  CameraRepository        com.example.aicamera                 D  Added camera: 0
2025-05-30 11:38:41.436  5842-5941  Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 11:38:41.458  5842-5941  CameraRepository        com.example.aicamera                 D  Added camera: 1
2025-05-30 11:38:41.461  5842-5941  Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 11:38:41.463  5842-5941  CameraValidator         com.example.aicamera                 D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-30 11:38:41.476  5842-5852  OpenGLRenderer          com.example.aicamera                 I  Davey! duration=1091ms; Flags=0, FrameTimelineVsyncId=31239427, IntendedVsync=570105026539649, Vsync=570105959873001, InputEventId=0, HandleInputStart=570105960846826, AnimationStart=570105960852672, PerformTraversalsStart=570106092371749, DrawStart=570106092585596, FrameDeadline=570105063206316, FrameInterval=570105960611134, FrameStartTime=16666667, SyncQueued=570106106117903, SyncStart=570106106482903, IssueDrawCommandsStart=570106106768365, SwapBuffers=570106110639826, FrameCompleted=570106118301903, DequeueBufferDuration=43000, QueueBufferDuration=1453461, GpuCompleted=570106118301903, SwapBuffersCompleted=570106114285057, DisplayPresentTime=0, 
2025-05-30 11:38:41.552  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:41.554  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:41.601  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:41.603  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:41.617  5842-5842  DynamicRangeResolver    com.example.aicamera                 D  Resolved dynamic range for use case androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df to no compatible HDR dynamic ranges.
                                                                                                    DynamicRange@bf007b4{encoding=UNSPECIFIED, bitDepth=0}
                                                                                                    ->
                                                                                                    DynamicRange@250ec87{encoding=SDR, bitDepth=8}
2025-05-30 11:38:41.644  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:41.648  5842-5842  DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=1, used_surfaces=0](androidx.camera.core.processing.SurfaceEdge$SettableSurface@c3b0cd9}
2025-05-30 11:38:41.659  5842-5842  DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=2, used_surfaces=0](androidx.camera.core.SurfaceRequest$2@cc69d95}
2025-05-30 11:38:41.667  5842-5842  DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@cc69d95}
2025-05-30 11:38:41.667  5842-5842  DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.SurfaceRequest$2@cc69d95
2025-05-30 11:38:41.671  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:41.689  5842-5842  ImageCapture            com.example.aicamera                 D  createPipeline(cameraId: 0, streamSpec: StreamSpec{resolution=720x720, dynamicRange=DynamicRange@250ec87{encoding=SDR, bitDepth=8}, expectedFrameRateRange=[0, 0], implementationOptions=androidx.camera.camera2.impl.Camera2ImplConfig@7050b76})
2025-05-30 11:38:41.712  5842-5842  BufferQueueConsumer     com.example.aicamera                 I  [](id:16d200000001,api:0,p:-1,c:5842) connect: controlledByApp=true
2025-05-30 11:38:41.715  5842-5842  DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=3, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@9e80577}
2025-05-30 11:38:41.738  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Use case androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946 ACTIVE
2025-05-30 11:38:41.743  5842-5942  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 11:38:41.751  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Use case androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765 ACTIVE
2025-05-30 11:38:41.753  5842-5942  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 11:38:41.761  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Use case androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946 ACTIVE
2025-05-30 11:38:41.764  5842-5942  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 11:38:41.768  5842-5942  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 11:38:41.776  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Use cases [androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765, androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946] now ATTACHED
2025-05-30 11:38:41.782  5842-5942  UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946, androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765] for camera: 0
2025-05-30 11:38:41.784  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  mMeteringRepeating is ATTACHED, SessionConfig Surfaces: 2, CaptureConfig Surfaces: 1
2025-05-30 11:38:41.784  5842-5842  PreviewView             com.example.aicamera                 D  Surface requested by Preview.
2025-05-30 11:38:41.796  5842-5942  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946, androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765] for camera: 0
2025-05-30 11:38:41.803  5842-5842  SurfaceFactory          com.example.aicamera                 I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@1842c6f
2025-05-30 11:38:41.805  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Resetting Capture Session
2025-05-30 11:38:41.807  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Releasing session in state INITIALIZED
2025-05-30 11:38:41.809  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Attempting to force open the camera.
2025-05-30 11:38:41.815  5842-5942  CameraStateRegistry     com.example.aicamera                 D  tryOpenCamera(Camera@d003f9a[id=0]) [Available Cameras: 1, Already Open: false (Previous state: null)] --> SUCCESS
2025-05-30 11:38:41.817  5842-5942  CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e7bee43[id=1]                         UNKNOWN               
                                                                                                    Camera@d003f9a[id=0]                         OPENING               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 11:38:41.818  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Opening camera.
2025-05-30 11:38:41.819  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Transitioning camera internal state: INITIALIZED --> OPENING
2025-05-30 11:38:41.820  5842-5942  CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPENING, error=null} from OPENING and null
2025-05-30 11:38:41.820  5842-5942  CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPENING, error=null}
2025-05-30 11:38:41.823  5842-5942  UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946, androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765] for camera: 0
2025-05-30 11:38:41.830  5842-5942  libc                    com.example.aicamera                 W  Access denied finding property "persist.vendor.camera.privapp.list"
2025-05-30 11:38:41.829  5842-5842  CameraX-core_ca         com.example.aicamera                 W  type=1400 audit(0.0:6452441): avc: denied { read } for name="u:object_r:vendor_camera_mtk_prop:s0" dev="tmpfs" ino=13626 scontext=u:r:untrusted_app:s0:c219,c258,c512,c768 tcontext=u:object_r:vendor_camera_mtk_prop:s0 tclass=file permissive=0 app=com.example.aicamera
2025-05-30 11:38:41.835  5842-5842  PreviewView             com.example.aicamera                 D  Preview transformation info updated. TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false}
2025-05-30 11:38:41.837  5842-5842  PreviewTransform        com.example.aicamera                 D  Transformation info set: TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false} 1024x768 false
2025-05-30 11:38:41.839  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:41.842  5842-5842  SurfaceViewImpl         com.example.aicamera                 D  Wait for new Surface creation.
2025-05-30 11:38:41.859  5842-5842  BufferQueueConsumer     com.example.aicamera                 I  [](id:16d200000002,api:0,p:-1,c:5842) connect: controlledByApp=false
2025-05-30 11:38:41.860  5842-5842  BLASTBufferQueue        com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1] constructor()
--------- beginning of system
2025-05-30 11:38:41.862  5842-5842  SurfaceViewImpl         com.example.aicamera                 D  Surface created.
2025-05-30 11:38:41.865  5842-5842  SurfaceViewImpl         com.example.aicamera                 D  Surface changed. Size: 1024x768
2025-05-30 11:38:41.865  5842-5842  SurfaceViewImpl         com.example.aicamera                 D  Surface set on Preview.
2025-05-30 11:38:41.889  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Use case androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765 ACTIVE
2025-05-30 11:38:41.892  5842-5942  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946, androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765] for camera: 0
2025-05-30 11:38:41.899  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Use case androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946 ACTIVE
2025-05-30 11:38:41.902  5842-5942  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946, androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765] for camera: 0
2025-05-30 11:38:41.909  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} CameraDevice.onOpened()
2025-05-30 11:38:41.910  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Transitioning camera internal state: OPENING --> OPENED
2025-05-30 11:38:41.912  5842-5942  CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e7bee43[id=1]                         UNKNOWN               
                                                                                                    Camera@d003f9a[id=0]                         OPEN                  
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 11:38:41.912  5842-5942  CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPEN, error=null} from OPEN and null
2025-05-30 11:38:41.913  5842-5942  CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPEN, error=null}
2025-05-30 11:38:41.915  5842-5942  UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946, androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765] for camera: 0
2025-05-30 11:38:41.927  5842-5942  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946, androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765] for camera: 0
2025-05-30 11:38:41.935  5842-5942  SyncCaptureSessionBase  com.example.aicamera                 D  [androidx.camera.camera2.internal.SynchronizedCaptureSessionBaseImpl@d02f898] getSurface...done
2025-05-30 11:38:41.936  5842-5942  CaptureSession          com.example.aicamera                 D  Opening capture session.
2025-05-30 11:38:41.949  5842-5942  DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=2 androidx.camera.core.SurfaceRequest$2@cc69d95
2025-05-30 11:38:41.949  5842-5942  DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=3, used_surfaces=2](androidx.camera.core.impl.ImmediateSurface@9e80577}
2025-05-30 11:38:41.949  5842-5942  DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.impl.ImmediateSurface@9e80577
2025-05-30 11:38:42.087  5842-5942  BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:16d200000002,api:4,p:985,c:5842) connect: api=4 producerControlledByApp=true
2025-05-30 11:38:42.089  5842-5942  BufferQueueProducer     com.example.aicamera                 I  [ImageReader-720x720f100m4-5842-0](id:16d200000001,api:4,p:985,c:5842) connect: api=4 producerControlledByApp=false
2025-05-30 11:38:42.097  5842-5942  CaptureSession          com.example.aicamera                 D  Attempting to send capture request onConfigured
2025-05-30 11:38:42.097  5842-5942  CaptureSession          com.example.aicamera                 D  Issuing request for session.
2025-05-30 11:38:42.099  5842-5942  Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 11:38:42.109  5842-5942  CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onConfigured() mState=OPENED
2025-05-30 11:38:42.112  5842-5942  CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onReady() OPENED
2025-05-30 11:38:42.361  5842-5942  StreamStateObserver     com.example.aicamera                 D  Update Preview stream state to STREAMING
2025-05-30 11:38:49.712  5842-5842  ImageCapture            com.example.aicamera                 D  takePictureInternal
2025-05-30 11:38:49.721  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:49.724  5842-5842  CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 11:38:49.731  5842-5842  TakePictureManager      com.example.aicamera                 D  Issue the next TakePictureRequest.
2025-05-30 11:38:49.757  5842-5942  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Issue capture request
2025-05-30 11:38:49.760  5842-5942  CaptureSession          com.example.aicamera                 D  Issuing capture request.
2025-05-30 11:38:49.770  5842-5942  Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 11:38:49.810  5842-5858  GraphicBuffer           com.example.aicamera                 D  initWithSize w=1806881, h=1
2025-05-30 11:38:49.811  5842-5858  GraphicBuffer           com.example.aicamera                 D  flatten w=1806881, h=1
2025-05-30 11:38:50.290  5842-5842  TakePictureManager      com.example.aicamera                 D  Issue the next TakePictureRequest.
2025-05-30 11:38:50.290  5842-5842  TakePictureManager      com.example.aicamera                 D  No new request.
2025-05-30 11:38:50.488  5842-5842  CameraManager           com.example.aicamera                 D  Photo capture succeeded: /storage/emulated/0/Android/data/com.example.aicamera/files/2025-05-30-11-38-49-657.jpg
2025-05-30 11:38:50.500  5842-5842  NetworkManager          com.example.aicamera                 D  🐍 Starting Python gradio_client upload...
2025-05-30 11:38:50.501  5842-5842  NetworkManager          com.example.aicamera                 D  📁 File: 2025-05-30-11-38-49-657.jpg, Size: 202924 bytes
2025-05-30 11:38:50.502  5842-5842  PythonGradioService     com.example.aicamera                 D  🐍 Starting Python environment...
2025-05-30 11:38:50.718  5842-5842  nativeloader            com.example.aicamera                 D  Load /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a/libcrypto_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!classes8.dex): ok
2025-05-30 11:38:50.741  5842-5842  nativeloader            com.example.aicamera                 D  Load /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a/libssl_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!classes8.dex): ok
2025-05-30 11:38:50.758  5842-5842  nativeloader            com.example.aicamera                 D  Load /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a/libsqlite3_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!classes8.dex): ok
2025-05-30 11:38:50.764  5842-5842  nativeloader            com.example.aicamera                 D  Load /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a/libcrypto_python.so using ns clns-4 from class loader (caller=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!classes8.dex): ok
2025-05-30 11:38:50.769  5842-5842  nativeloader            com.example.aicamera                 D  Load /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a/libssl_python.so using ns clns-4 from class loader (caller=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!classes8.dex): ok
2025-05-30 11:38:50.773  5842-5842  nativeloader            com.example.aicamera                 D  Load /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a/libsqlite3_python.so using ns clns-4 from class loader (caller=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!classes8.dex): ok
2025-05-30 11:38:50.800  5842-5842  nativeloader            com.example.aicamera                 D  Load /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a/libpython3.11.so using ns clns-4 from class loader (caller=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!classes8.dex): ok
2025-05-30 11:38:50.807  5842-5842  nativeloader            com.example.aicamera                 D  Load /data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!/lib/arm64-v8a/libchaquopy_java.so using ns clns-4 from class loader (caller=/data/app/~~Ho4rcYgcjmIl-nTcxpPRsw==/com.example.aicamera-YEoXOl0l36LHX9F_mTGZSw==/base.apk!classes8.dex): ok
2025-05-30 11:38:51.009  5842-5848  xample.aicamer          com.example.aicamera                 I  Background concurrent copying GC freed 931KB AllocSpace bytes, 13(6584KB) LOS objects, 24% free, 9023KB/11MB, paused 177us,101us total 119.104ms
2025-05-30 11:38:52.005  5842-5842  xample.aicamera         com.example.aicamera                 W  type=1400 audit(0.0:6452762): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/binascii.cpython-311.so" dev="dm-10" ino=343465 scontext=u:r:untrusted_app:s0:c219,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c219,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 11:38:52.025  5842-5842  xample.aicamera         com.example.aicamera                 W  type=1400 audit(0.0:6452763): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/bootstrap-native/arm64-v8a/mmap.cpython-311.so" dev="dm-10" ino=343427 scontext=u:r:untrusted_app:s0:c219,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c219,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 11:38:52.720  5842-5842  PythonGradioService     com.example.aicamera                 D  ✅ Python environment started
2025-05-30 11:38:52.720  5842-5842  PythonGradioService     com.example.aicamera                 D  🔗 Python instance obtained
2025-05-30 11:38:53.093  5842-5842  xample.aicamera         com.example.aicamera                 W  type=1400 audit(0.0:6452802): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_socket.cpython-311.so" dev="dm-10" ino=392670 scontext=u:r:untrusted_app:s0:c219,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c219,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 11:38:55.661  5842-5842  python.stderr           com.example.aicamera                 W  /data/data/com.example.aicamera/files/chaquopy/AssetFinder/requirements/websockets/legacy/__init__.py:6: DeprecationWarning: websockets.legacy is deprecated; see https://websockets.readthedocs.io/en/stable/howto/upgrade.html for upgrade instructions
2025-05-30 11:38:55.857  5842-5842  PythonGradioService     com.example.aicamera                 D  📦 chess_fen_client module imported successfully
2025-05-30 11:38:57.408  5842-5842  python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 11:39:00.503  5842-5848  xample.aicamer          com.example.aicamera                 I  NativeAlloc concurrent copying GC freed 591KB AllocSpace bytes, 2(104KB) LOS objects, 25% free, 6052KB/8100KB, paused 363us,50us total 119.506ms
2025-05-30 11:39:02.108  5842-5842  PythonGradioService     com.example.aicamera                 D  🔗 Connection test result: {'success': True, 'message': 'Successfully connected to HF Space', 'space': 'yamero999/chess-fen-generation-api'}
2025-05-30 11:39:02.108  5842-5842  PythonGradioService     com.example.aicamera                 D  ✅ Python gradio service initialized successfully
2025-05-30 11:39:02.119  5842-6077  PythonGradioService     com.example.aicamera                 D  🚀 Starting FEN generation using Python gradio_client...
2025-05-30 11:39:02.120  5842-5842  Choreographer           com.example.aicamera                 I  Skipped 698 frames!  The application may be doing too much work on its main thread.
2025-05-30 11:39:02.120  5842-6077  PythonGradioService     com.example.aicamera                 D  📁 File: 2025-05-30-11-38-49-657.jpg, Size: 202924 bytes
2025-05-30 11:39:02.189  5842-6046  OpenGLRenderer          com.example.aicamera                 I  Davey! duration=11697ms; Flags=0, FrameTimelineVsyncId=31239986, IntendedVsync=570115141535987, Vsync=570126774869553, InputEventId=0, HandleInputStart=570126778921366, AnimationStart=570126778928135, PerformTraversalsStart=570126799857289, DrawStart=570126800229058, FrameDeadline=570115178202654, FrameInterval=570126778417058, FrameStartTime=16666667, SyncQueued=570126826709366, SyncStart=570126826832981, IssueDrawCommandsStart=570126827073904, SwapBuffers=570126831073674, FrameCompleted=570126839155212, DequeueBufferDuration=41539, QueueBufferDuration=1241307, GpuCompleted=570126839155212, SwapBuffersCompleted=570126833869597, DisplayPresentTime=1901577384, 
2025-05-30 11:39:02.257  5842-6077  PythonGradioService     com.example.aicamera                 D  📦 Converted image to base64: 270568 characters
2025-05-30 11:39:02.257  5842-6077  PythonGradioService     com.example.aicamera                 D  🐍 Calling Python gradio_client...
2025-05-30 11:39:03.212  5842-6077  python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 11:39:12.962  5842-6077  PythonGradioService     com.example.aicamera                 D  📄 Python result: {'success': True, 'fen': '1K4B1/1PP1RRPP/P1Q2N2/2B1b3/4b3/p1n3qp/1pp3p1/1k3r1r', 'error': None, 'raw_result': {'success': True, 'fen': '1K4B1/1PP1RRPP/P1Q2N2/2B1b3/4b3/p1n3qp/1pp3p1/1k3r1r', 'analysis': {'pieces_detected': 24, 'processing_time_ms': 859.69, 'processing_time_seconds': 0.86, 'v6_inference_time_ms': 733.2100868225098, 'board_detection_time_ms': 739.14, 'piece_detection_time_ms': 118.38, 'mapping_time_ms': 0.28, 'save_time_ms': 1.86, 'board_detection': {'corners_found': [[144.0, 172.0], [550.0, 166.0], [553.0, 567.0], [150.0, 567.0]], 'perspective_corrected': True}, 'piece_analysis': {'total_pieces': 24, 'piece_counts': {'black_rook': 2, 'black_pawn': 5, 'white_pawn': 5, 'black_bishop': 2, 'white_king': 1, 'white_queen': 1, 'white_bishop': 2, 'white_knight': 1, 'black_knight': 1, 'black_queen': 1, 'white_rook': 2, 'black_king': 1}, 'confidence_stats': {'avg': 0.938, 'min': 0.775, 'max': 0.994}}, 'grid_mapping': {'occupied_squares': 24, 'empty_squares': 40}}, 'provider': 'huggingface_spaces', 'model_info': {'v6_segmentation': 'yamero999/chess-board-segmentation-v6', 'piece_detection': 'yamero999/chess-piece-detection-yolo11n'}}}
2025-05-30 11:39:13.027  5842-6077  PythonGradioService     com.example.aicamera                 D  📊 Parsed result - Success: true, FEN: 1K4B1/1PP1RRPP/P1Q2N2/2B1b3/4b3/p1n3qp/1pp3p1/1k3r1r, Error: null
2025-05-30 11:39:13.027  5842-6077  PythonGradioService     com.example.aicamera                 D  🎯 FEN Result: 1K4B1/1PP1RRPP/P1Q2N2/2B1b3/4b3/p1n3qp/1pp3p1/1k3r1r
2025-05-30 11:39:13.028  5842-6077  PythonGradioService     com.example.aicamera                 D  ✅ Successfully processed FEN response
2025-05-30 11:39:13.045  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Use cases [androidx.camera.core.Preview-bab4c794-7a15-4073-9bdf-d543eab6e2df244457765, androidx.camera.core.ImageCapture-dfb34976-ba39-4561-a854-edb7141c344732477946] now DETACHED for camera
2025-05-30 11:39:13.046  5842-5941  UseCaseAttachState      com.example.aicamera                 D  All use case: [] for camera: 0
2025-05-30 11:39:13.048  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Resetting Capture Session
2025-05-30 11:39:13.056  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Releasing session in state OPENED
2025-05-30 11:39:13.058  5842-5941  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 11:39:13.064  5842-5941  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 11:39:13.072  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Closing camera.
2025-05-30 11:39:13.073  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Transitioning camera internal state: OPENED --> CLOSING
2025-05-30 11:39:13.076  5842-5941  CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e7bee43[id=1]                         UNKNOWN               
                                                                                                    Camera@d003f9a[id=0]                         CLOSING               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 11:39:13.077  5842-5941  CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=CLOSING, error=null} from CLOSING and null
2025-05-30 11:39:13.078  5842-5941  CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=CLOSING, error=null}
2025-05-30 11:39:13.079  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Resetting Capture Session
2025-05-30 11:39:13.080  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Releasing session in state CLOSING
2025-05-30 11:39:13.082  5842-5941  CaptureSession          com.example.aicamera                 D  onSessionFinished()
2025-05-30 11:39:13.422  5842-5941  BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:16d200000002,api:4,p:985,c:5842) disconnect: api 4
2025-05-30 11:39:13.428  5842-5941  BufferQueueProducer     com.example.aicamera                 I  [ImageReader-720x720f100m4-5842-0](id:16d200000001,api:4,p:985,c:5842) disconnect: api 4
2025-05-30 11:39:13.433  5842-5941  UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 11:39:13.435  5842-5941  DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=1 closed=false androidx.camera.core.SurfaceRequest$2@cc69d95
2025-05-30 11:39:13.436  5842-5941  DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=0 closed=false androidx.camera.core.impl.ImmediateSurface@9e80577
2025-05-30 11:39:13.436  5842-5941  DeferrableSurface       com.example.aicamera                 D  Surface no longer in use[total_surfaces=3, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@9e80577}
2025-05-30 11:39:13.436  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} CameraDevice.onClosed()
2025-05-30 11:39:13.437  5842-5941  Camera2CameraImpl       com.example.aicamera                 D  {Camera@d003f9a[id=0]} Transitioning camera internal state: CLOSING --> INITIALIZED
2025-05-30 11:39:13.438  5842-5941  CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e7bee43[id=1]                         UNKNOWN               
                                                                                                    Camera@d003f9a[id=0]                         CLOSED                
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 0 (Max allowed: 1)
2025-05-30 11:39:13.439  5842-5941  CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=CLOSED, error=null} from CLOSED and null
2025-05-30 11:39:13.439  5842-5941  CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=CLOSED, error=null}
--------- beginning of crash
2025-05-30 11:39:13.595  5842-5842  AndroidRuntime          com.example.aicamera                 D  Shutting down VM
2025-05-30 11:39:13.603  5842-5842  AndroidRuntime          com.example.aicamera                 E  FATAL EXCEPTION: main (Ask Gemini)
                                                                                                    Process: com.example.aicamera, PID: 5842
                                                                                                    java.lang.StringIndexOutOfBoundsException: begin 0, end -1, length 52
                                                                                                    	at java.lang.String.checkBoundsBeginEnd(String.java:4500)
                                                                                                    	at java.lang.String.substring(String.java:2527)
                                                                                                    	at com.github.bhlangonijr.chesslib.Board.loadFromFen(Board.java:709)
                                                                                                    	at com.example.aicamera.chess.ChessBoardComposeKt.SimpleFENBoard(ChessBoardCompose.kt:251)
                                                                                                    	at com.example.aicamera.ui.screens.ChessBoardScreenKt$ChessBoardScreen$3$1$1$1.invoke(ChessBoardScreen.kt:119)
                                                                                                    	at com.example.aicamera.ui.screens.ChessBoardScreenKt$ChessBoardScreen$3$1$1$1.invoke(ChessBoardScreen.kt:118)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:117)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListIntervalContent$item$3.invoke(LazyListIntervalContent.kt:59)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListIntervalContent$item$3.invoke(LazyListIntervalContent.kt:59)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:138)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListItemProviderImpl$Item$1.invoke(LazyListItemProvider.kt:79)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListItemProviderImpl$Item$1.invoke(LazyListItemProvider.kt:77)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:108)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:228)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutPinnableItemKt.LazyLayoutPinnableItem(LazyLayoutPinnableItem.kt:54)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListItemProviderImpl.Item(LazyListItemProvider.kt:77)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt$SkippableItem$1.invoke(LazyLayoutItemContentFactory.kt:135)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt$SkippableItem$1.invoke(LazyLayoutItemContentFactory.kt:134)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:108)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
                                                                                                    	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:228)
                                                                                                    	at androidx.compose.runtime.saveable.SaveableStateHolderImpl.SaveableStateProvider(SaveableStateHolder.kt:84)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazySaveableStateHolder.SaveableStateProvider(LazySaveableStateHolder.kt:84)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt.SkippableItem-JVlU9Rs(LazyLayoutItemContentFactory.kt:134)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt.access$SkippableItem-JVlU9Rs(LazyLayoutItemContentFactory.kt:1)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.invoke(LazyLayoutItemContentFactory.kt:101)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.invoke(LazyLayoutItemContentFactory.kt:91)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:108)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:1067)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:701)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:108)
                                                                                                    	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
                                                                                                    	at androidx.compose.runtime.ActualJvm_jvmKt.invokeComposable(ActualJvm.jvm.kt:78)
2025-05-30 11:39:13.605  5842-5842  AndroidRuntime          com.example.aicamera                 E  	at androidx.compose.runtime.ComposerImpl.doCompose(Composer.kt:3340) (Ask Gemini)
                                                                                                    	at androidx.compose.runtime.ComposerImpl.composeContent$runtime_release(Composer.kt:3273)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.composeContent(Composition.kt:588)
                                                                                                    	at androidx.compose.runtime.Recomposer.composeInitial$runtime_release(Recomposer.kt:1013)
                                                                                                    	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:4007)
                                                                                                    	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:4007)
                                                                                                    	at androidx.compose.runtime.CompositionImpl.setContent(Composition.kt:520)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcomposeInto(SubcomposeLayout.kt:721)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:694)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:685)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:669)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$Scope.subcompose(SubcomposeLayout.kt:1014)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutMeasureScopeImpl.measure-0kLqBqw(LazyLayoutMeasureScope.kt:121)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListMeasuredItemProvider.getAndMeasure(LazyListMeasuredItemProvider.kt:48)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListMeasureKt.measureLazyList-CD5nmq0(LazyListMeasure.kt:159)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke-0kLqBqw(LazyList.kt:306)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke(LazyList.kt:184)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke-0kLqBqw(LazyLayout.kt:87)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke(LazyLayout.kt:80)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:866)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:126)
                                                                                                    	at androidx.compose.foundation.AndroidOverscrollKt$StretchOverscrollNonClippingLayer$2.invoke-3p2s80s(AndroidOverscroll.kt:578)
                                                                                                    	at androidx.compose.foundation.AndroidOverscrollKt$StretchOverscrollNonClippingLayer$2.invoke(AndroidOverscroll.kt:577)
                                                                                                    	at androidx.compose.ui.layout.LayoutModifierImpl.measure-3p2s80s(LayoutModifier.kt:291)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.foundation.AndroidOverscrollKt$StretchOverscrollNonClippingLayer$1.invoke-3p2s80s(AndroidOverscroll.kt:562)
                                                                                                    	at androidx.compose.foundation.AndroidOverscrollKt$StretchOverscrollNonClippingLayer$1.invoke(AndroidOverscroll.kt:561)
                                                                                                    	at androidx.compose.ui.layout.LayoutModifierImpl.measure-3p2s80s(LayoutModifier.kt:291)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:646)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.foundation.layout.PaddingValuesModifier.measure-3p2s80s(Padding.kt:455)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.foundation.layout.FillNode.measure-3p2s80s(Size.kt:698)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasure$2.invoke(LayoutNodeLayoutDelegate.kt:1499)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasure$2.invoke(LayoutNodeLayoutDelegate.kt:1495)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
2025-05-30 11:39:13.606  5842-5842  AndroidRuntime          com.example.aicamera                 E  	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467) (Ask Gemini)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1495)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:35)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:560)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.measure-BRTryo0(LayoutNodeLayoutDelegate.kt:539)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$1.invoke(Scaffold.kt:240)
                                                                                                    	at androidx.compose.material3.ScaffoldKt$ScaffoldLayout$1$1$1.invoke(Scaffold.kt:128)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure$1.placeChildren(SubcomposeLayout.kt:879)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildren$1$1.invoke(LayoutNodeLayoutDelegate.kt:365)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildren$1$1.invoke(LayoutNodeLayoutDelegate.kt:357)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeLayoutSnapshotReads$ui_release(OwnerSnapshotObserver.kt:83)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.layoutChildren(LayoutNodeLayoutDelegate.kt:357)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.onNodePlaced$ui_release(LayoutNodeLayoutDelegate.kt:493)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.placeAt-f8xVGno(InnerNodeCoordinator.kt:160)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place-70tqf50(Placeable.kt:450)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinator$1.invoke(LayoutNodeLayoutDelegate.kt:683)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinator$1.invoke(LayoutNodeLayoutDelegate.kt:678)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeLayoutModifierSnapshotReads$ui_release(OwnerSnapshotObserver.kt:98)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.placeOuterCoordinator-f8xVGno(LayoutNodeLayoutDelegate.kt:678)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.placeAt-f8xVGno(LayoutNodeLayoutDelegate.kt:656)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place-70tqf50(Placeable.kt:450)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place-70tqf50$default(Placeable.kt:225)
2025-05-30 11:39:13.608  5842-5842  AndroidRuntime          com.example.aicamera                 E  	at androidx.compose.foundation.layout.BoxKt.placeInBox(Box.kt:185) (Ask Gemini)
                                                                                                    	at androidx.compose.foundation.layout.BoxKt.access$placeInBox(Box.kt:1)
                                                                                                    	at androidx.compose.foundation.layout.BoxKt$boxMeasurePolicy$1$measure$2.invoke(Box.kt:125)
                                                                                                    	at androidx.compose.foundation.layout.BoxKt$boxMeasurePolicy$1$measure$2.invoke(Box.kt:124)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildren$1$1.invoke(LayoutNodeLayoutDelegate.kt:365)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildren$1$1.invoke(LayoutNodeLayoutDelegate.kt:357)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeLayoutSnapshotReads$ui_release(OwnerSnapshotObserver.kt:83)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.layoutChildren(LayoutNodeLayoutDelegate.kt:357)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.onNodePlaced$ui_release(LayoutNodeLayoutDelegate.kt:493)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.placeAt-f8xVGno(InnerNodeCoordinator.kt:160)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.placeWithLayer(Placeable.kt:473)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.placeWithLayer$default(Placeable.kt:288)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier$measure$1.invoke(GraphicsLayerModifier.kt:648)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier$measure$1.invoke(GraphicsLayerModifier.kt:647)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.placeAt-f8xVGno(LayoutModifierNodeCoordinator.kt:172)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.placeRelative(Placeable.kt:439)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.placeRelative$default(Placeable.kt:201)
                                                                                                    	at androidx.compose.foundation.layout.FillNode$measure$1.invoke(Size.kt:703)
                                                                                                    	at androidx.compose.foundation.layout.FillNode$measure$1.invoke(Size.kt:702)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.placeAt-f8xVGno(LayoutModifierNodeCoordinator.kt:172)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place-70tqf50(Placeable.kt:450)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinator$1.invoke(LayoutNodeLayoutDelegate.kt:683)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinator$1.invoke(LayoutNodeLayoutDelegate.kt:678)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeLayoutModifierSnapshotReads$ui_release(OwnerSnapshotObserver.kt:98)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.placeOuterCoordinator-f8xVGno(LayoutNodeLayoutDelegate.kt:678)
2025-05-30 11:39:13.609  5842-5842  AndroidRuntime          com.example.aicamera                 E  	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.placeAt-f8xVGno(LayoutNodeLayoutDelegate.kt:656) (Ask Gemini)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place(Placeable.kt:447)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place$default(Placeable.kt:213)
                                                                                                    	at androidx.compose.animation.AnimatedEnterExitMeasurePolicy$measure$1.invoke(AnimatedVisibility.kt:802)
                                                                                                    	at androidx.compose.animation.AnimatedEnterExitMeasurePolicy$measure$1.invoke(AnimatedVisibility.kt:800)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildren$1$1.invoke(LayoutNodeLayoutDelegate.kt:365)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildren$1$1.invoke(LayoutNodeLayoutDelegate.kt:357)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeLayoutSnapshotReads$ui_release(OwnerSnapshotObserver.kt:83)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.layoutChildren(LayoutNodeLayoutDelegate.kt:357)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.onNodePlaced$ui_release(LayoutNodeLayoutDelegate.kt:493)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.placeAt-f8xVGno(InnerNodeCoordinator.kt:160)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.placeWithLayer(Placeable.kt:473)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.placeWithLayer$default(Placeable.kt:288)
                                                                                                    	at androidx.compose.ui.graphics.BlockGraphicsLayerModifier$measure$1.invoke(GraphicsLayerModifier.kt:580)
                                                                                                    	at androidx.compose.ui.graphics.BlockGraphicsLayerModifier$measure$1.invoke(GraphicsLayerModifier.kt:579)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.placeAt-f8xVGno(LayoutModifierNodeCoordinator.kt:172)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place(Placeable.kt:447)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$1$1.invoke(AnimatedContent.kt:765)
                                                                                                    	at androidx.compose.animation.AnimatedContentKt$AnimatedContent$6$1$1$1.invoke(AnimatedContent.kt:764)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.placeAt-f8xVGno(LayoutModifierNodeCoordinator.kt:172)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place-70tqf50(Placeable.kt:450)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinator$1.invoke(LayoutNodeLayoutDelegate.kt:683)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinator$1.invoke(LayoutNodeLayoutDelegate.kt:678)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
2025-05-30 11:39:13.611  5842-5842  AndroidRuntime          com.example.aicamera                 E  	at androidx.compose.ui.node.OwnerSnapshotObserver.observeLayoutModifierSnapshotReads$ui_release(OwnerSnapshotObserver.kt:98) (Ask Gemini)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.placeOuterCoordinator-f8xVGno(LayoutNodeLayoutDelegate.kt:678)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.placeAt-f8xVGno(LayoutNodeLayoutDelegate.kt:656)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place(Placeable.kt:447)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place$default(Placeable.kt:213)
                                                                                                    	at androidx.compose.animation.AnimatedContentMeasurePolicy$measure$3.invoke(AnimatedContent.kt:837)
                                                                                                    	at androidx.compose.animation.AnimatedContentMeasurePolicy$measure$3.invoke(AnimatedContent.kt:829)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildren$1$1.invoke(LayoutNodeLayoutDelegate.kt:365)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildren$1$1.invoke(LayoutNodeLayoutDelegate.kt:357)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeLayoutSnapshotReads$ui_release(OwnerSnapshotObserver.kt:83)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.layoutChildren(LayoutNodeLayoutDelegate.kt:357)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.onNodePlaced$ui_release(LayoutNodeLayoutDelegate.kt:493)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.placeAt-f8xVGno(InnerNodeCoordinator.kt:160)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place-70tqf50(Placeable.kt:450)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place-70tqf50$default(Placeable.kt:225)
                                                                                                    	at androidx.compose.animation.AnimatedContentTransitionScopeImpl$SizeModifier$measure$1.invoke(AnimatedContent.kt:614)
                                                                                                    	at androidx.compose.animation.AnimatedContentTransitionScopeImpl$SizeModifier$measure$1.invoke(AnimatedContent.kt:613)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.placeAt-f8xVGno(LayoutModifierNodeCoordinator.kt:172)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.placeWithLayer(Placeable.kt:473)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.placeWithLayer$default(Placeable.kt:288)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier$measure$1.invoke(GraphicsLayerModifier.kt:648)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier$measure$1.invoke(GraphicsLayerModifier.kt:647)
                                                                                                    	at androidx.compose.ui.layout.MeasureScope$layout$1.placeChildren(MeasureScope.kt:70)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.placeAt-f8xVGno(LayoutModifierNodeCoordinator.kt:172)
                                                                                                    	at androidx.compose.ui.layout.Placeable.access$placeAt-f8xVGno(Placeable.kt:34)
                                                                                                    	at androidx.compose.ui.layout.Placeable$PlacementScope.place-70tqf50(Placeable.kt:450)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinator$1.invoke(LayoutNodeLayoutDelegate.kt:683)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinator$1.invoke(LayoutNodeLayoutDelegate.kt:678)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2299)
2025-05-30 11:39:13.612  5842-5842  AndroidRuntime          com.example.aicamera                 E  	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:467) (Ask Gemini)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:230)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeLayoutModifierSnapshotReads$ui_release(OwnerSnapshotObserver.kt:98)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.placeOuterCoordinator-f8xVGno(LayoutNodeLayoutDelegate.kt:678)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.replace(LayoutNodeLayoutDelegate.kt:703)
                                                                                                    	at androidx.compose.ui.node.LayoutNode.replace$ui_release(LayoutNode.kt:910)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureAndRelayoutIfNeeded(MeasureAndLayoutDelegate.kt:469)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.access$remeasureAndRelayoutIfNeeded(MeasureAndLayoutDelegate.kt:39)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.measureAndLayout(MeasureAndLayoutDelegate.kt:344)
                                                                                                    	at androidx.compose.ui.platform.AndroidComposeView.measureAndLayout(AndroidComposeView.android.kt:879)
                                                                                                    	at androidx.compose.ui.node.Owner.measureAndLayout$default(Owner.kt:223)
                                                                                                    	at androidx.compose.ui.platform.AndroidComposeView.dispatchDraw(AndroidComposeView.android.kt:1127)
                                                                                                    	at android.view.View.draw(View.java:22940)
                                                                                                    	at android.view.View.updateDisplayListIfDirty(View.java:21775)
                                                                                                    	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4603)
                                                                                                    	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4576)
                                                                                                    	at android.view.View.updateDisplayListIfDirty(View.java:21726)
                                                                                                    	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4603)
                                                                                                    	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4576)
                                                                                                    	at android.view.View.updateDisplayListIfDirty(View.java:21726)
                                                                                                    	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4603)
                                                                                                    	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4576)
                                                                                                    	at android.view.View.updateDisplayListIfDirty(View.java:21726)
                                                                                                    	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4603)
                                                                                                    	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4576)
                                                                                                    	at android.view.View.updateDisplayListIfDirty(View.java:21726)
                                                                                                    	at android.view.ThreadedRenderer.updateViewTreeDisplayList(ThreadedRenderer.java:561)
                                                                                                    	at android.view.ThreadedRenderer.updateRootDisplayList(ThreadedRenderer.java:567)
                                                                                                    	at android.view.ThreadedRenderer.draw(ThreadedRenderer.java:669)
                                                                                                    	at android.view.ViewRootImpl.draw(ViewRootImpl.java:4827)
                                                                                                    	at android.view.ViewRootImpl.performDraw(ViewRootImpl.java:4551)
                                                                                                    	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:3684)
                                                                                                    	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:2394)
                                                                                                    	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:9331)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1186)
                                                                                                    	at android.view.Choreographer.doCallbacks(Choreographer.java:986)
                                                                                                    	at android.view.Choreographer.doFrame(Choreographer.java:912)
                                                                                                    	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1171)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:938)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:233)
                                                                                                    	at android.os.Looper.loop(Looper.java:334)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8399)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:582)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1068)
---------------------------- PROCESS ENDED (5842) for package com.example.aicamera ----------------------------