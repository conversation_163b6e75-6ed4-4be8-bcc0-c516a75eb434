<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_settings" modulePackage="com.example.aicamera" filePath="app\src\main\res\layout\activity_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_settings_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="204" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="30"/></Target><Target id="@+id/apiProviderSpinner" view="Spinner"><Expressions/><location startLine="65" startOffset="20" endLine="68" endOffset="62"/></Target><Target id="@+id/huggingFaceSection" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="75" startOffset="12" endLine="137" endOffset="63"/></Target><Target id="@+id/tokenEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="105" startOffset="24" endLine="109" endOffset="62"/></Target><Target id="@+id/modelSpinner" view="Spinner"><Expressions/><location startLine="130" startOffset="20" endLine="133" endOffset="62"/></Target><Target id="@+id/localServerSection" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="140" startOffset="12" endLine="172" endOffset="63"/></Target><Target id="@+id/testConnectionButton" view="Button"><Expressions/><location startLine="181" startOffset="16" endLine="188" endOffset="75"/></Target><Target id="@+id/saveButton" view="Button"><Expressions/><location startLine="190" startOffset="16" endLine="196" endOffset="58"/></Target></Targets></Layout>