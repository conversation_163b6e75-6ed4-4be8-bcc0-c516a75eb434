<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- Camera Preview -->
    <androidx.camera.view.PreviewView
        android:id="@+id/previewView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/controlsContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Controls Container -->
    <LinearLayout
        android:id="@+id/controlsContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Capture Button -->
        <Button
            android:id="@+id/captureButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="📷 Capture Chess Board (416x416)"
            android:textSize="16sp" />

        <!-- Send to AI Button -->
        <Button
            android:id="@+id/sendToAiButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:enabled="false"
            android:text="🤖 Generate FEN"
            android:textSize="16sp" />

        <!-- Status Text -->
        <TextView
            android:id="@+id/statusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:gravity="center"
            android:text="♟️ Ready to capture chess board"
            android:textSize="14sp"
            android:textStyle="italic" />

        <!-- FEN Output Label -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="FEN Output:"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <!-- FEN Response Container -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:background="#F5F5F5"
            android:padding="8dp">

            <TextView
                android:id="@+id/aiResponseText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Chess FEN notation will appear here after analysis..."
                android:textSize="14sp"
                android:textColor="@color/black"
                android:fontFamily="monospace"
                android:textIsSelectable="true" />
        </ScrollView>

    </LinearLayout>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/previewView"
        app:layout_constraintEnd_toEndOf="@+id/previewView"
        app:layout_constraintStart_toStartOf="@+id/previewView"
        app:layout_constraintTop_toTopOf="@+id/previewView" />

</androidx.constraintlayout.widget.ConstraintLayout>
