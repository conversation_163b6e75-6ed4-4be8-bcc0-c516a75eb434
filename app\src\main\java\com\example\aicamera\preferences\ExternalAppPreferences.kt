package com.example.aicamera.preferences

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue

/**
 * External Chess App Preferences Manager
 * Handles user's preferred external chess app selection
 */
class ExternalAppPreferences(context: Context) {

    companion object {
        private const val PREFS_NAME = "external_app_prefs"
        private const val KEY_SELECTED_APP = "selected_chess_app"
        private const val KEY_FIRST_LAUNCH = "is_first_launch"

        // Default app is Chessis
        const val DEFAULT_APP = "chessis"
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    var currentApp by mutableStateOf(loadSelectedApp())
        private set

    var isFirstLaunch by mutableStateOf(loadFirstLaunch())
        private set

    /**
     * Get currently selected chess app
     */
    private fun loadSelectedApp(): String {
        return prefs.getString(KEY_SELECTED_APP, DEFAULT_APP) ?: DEFAULT_APP
    }

    /**
     * Check if this is first launch
     */
    private fun loadFirstLaunch(): Boolean {
        return prefs.getBoolean(KEY_FIRST_LAUNCH, true)
    }

    /**
     * Set selected chess app
     */
    fun updateSelectedApp(appId: String) {
        currentApp = appId
        prefs.edit().putString(KEY_SELECTED_APP, appId).apply()
    }

    /**
     * Mark first launch as completed
     */
    fun setFirstLaunchCompleted() {
        isFirstLaunch = false
        prefs.edit().putBoolean(KEY_FIRST_LAUNCH, false).apply()
    }

    /**
     * Reset to first launch state (for testing)
     */
    fun resetToFirstLaunch() {
        isFirstLaunch = true
        currentApp = DEFAULT_APP
        prefs.edit()
            .putBoolean(KEY_FIRST_LAUNCH, true)
            .putString(KEY_SELECTED_APP, DEFAULT_APP)
            .apply()
    }
}

/**
 * Supported external chess apps
 */
data class ExternalChessApp(
    val id: String,
    val name: String,
    val packageName: String,
    val description: String,
    val icon: String, // Material icon name
    val isRecommended: Boolean = false
)

/**
 * Available external chess apps
 */
object SupportedChessApps {

    val CHESSIS = ExternalChessApp(
        id = "chessis",
        name = "Chessis",
        packageName = "com.chessis.analysis",
        description = "Professional chess analysis with Stockfish engine, board editor, and advanced features",
        icon = "analytics",
        isRecommended = true
    )

    val CHESS_COM = ExternalChessApp(
        id = "chess_com",
        name = "Chess.com",
        packageName = "com.chess",
        description = "World's largest chess platform with analysis, puzzles, and online play",
        icon = "sports_esports"
    )

    val LICHESS = ExternalChessApp(
        id = "lichess",
        name = "Lichess",
        packageName = "org.lichess.mobileapp",
        description = "Free and open-source chess platform with powerful analysis tools",
        icon = "psychology"
    )

    val DROIDFISH = ExternalChessApp(
        id = "droidfish",
        name = "DroidFish",
        packageName = "org.petero.droidfish",
        description = "Advanced chess app with multiple engines and analysis features",
        icon = "memory"
    )

    val GENERIC = ExternalChessApp(
        id = "generic",
        name = "Any Chess App",
        packageName = "",
        description = "Show app chooser to select from any installed chess app",
        icon = "apps"
    )

    /**
     * Get all supported apps
     */
    fun getAllApps(): List<ExternalChessApp> {
        return listOf(CHESSIS, CHESS_COM, LICHESS, DROIDFISH, GENERIC)
    }

    /**
     * Get app by ID
     */
    fun getAppById(id: String): ExternalChessApp? {
        return getAllApps().find { it.id == id }
    }

    /**
     * Get recommended apps
     */
    fun getRecommendedApps(): List<ExternalChessApp> {
        return getAllApps().filter { it.isRecommended }
    }
}
