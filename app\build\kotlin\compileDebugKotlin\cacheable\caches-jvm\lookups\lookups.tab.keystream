  Manifest android  R android  
permission android.Manifest  CAMERA android.Manifest.permission  INTERNET android.Manifest.permission  layout 	android.R  simple_spinner_dropdown_item android.R.layout  simple_spinner_item android.R.layout  Activity android.app  Application android.app  ActivityMainBinding android.app.Activity  ActivityResultContracts android.app.Activity  ActivitySettingsBinding android.app.Activity  AdapterView android.app.Activity  	ApiConfig android.app.Activity  ArrayAdapter android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  
CameraManager android.app.Activity  ChessAnalysisScreen android.app.Activity  ChessApp android.app.Activity  
ChessTheme android.app.Activity  Context android.app.Activity  
ContextCompat android.app.Activity  EXTRA_SCANNED_FEN android.app.Activity  File android.app.Activity  Int android.app.Activity  Intent android.app.Activity  KEY_API_PROVIDER android.app.Activity  KEY_HF_MODEL android.app.Activity  KEY_HF_TOKEN android.app.Activity  Log android.app.Activity  Long android.app.Activity  MainActivity android.app.Activity  Manifest android.app.Activity  
MaterialTheme android.app.Activity  Menu android.app.Activity  MenuItem android.app.Activity  Modifier android.app.Activity  NetworkManager android.app.Activity  
NetworkResult android.app.Activity  
PREFS_NAME android.app.Activity  PackageManager android.app.Activity  R android.app.Activity  REQUIRED_PERMISSIONS android.app.Activity  SettingsActivity android.app.Activity  SharedPreferences android.app.Activity  String android.app.Activity  Surface android.app.Activity  TAG android.app.Activity  Toast android.app.Activity  View android.app.Activity  all android.app.Activity  allPermissionsGranted android.app.Activity  android android.app.Activity  
applySettings android.app.Activity  arrayOf android.app.Activity  binding android.app.Activity  capturePhoto android.app.Activity  displayAIResponse android.app.Activity  fillMaxSize android.app.Activity  finish android.app.Activity  	getOrNull android.app.Activity  getSharedPreferences android.app.Activity  	getString android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  invoke android.app.Activity  isEmpty android.app.Activity  java android.app.Activity  launch android.app.Activity  let android.app.Activity  lifecycleScope android.app.Activity  loadSettings android.app.Activity  map android.app.Activity  
mutableListOf android.app.Activity  networkManager android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  onOptionsItemSelected android.app.Activity  registerForActivityResult android.app.Activity  saveSettings android.app.Activity  
sendImageToAI android.app.Activity  
setContent android.app.Activity  setContentView android.app.Activity  setSupportActionBar android.app.Activity  	setWindow android.app.Activity  setupUI android.app.Activity  	showError android.app.Activity  showProgressBar android.app.Activity  	showToast android.app.Activity  
startActivity android.app.Activity  startCamera android.app.Activity  
startsWith android.app.Activity  testConnection android.app.Activity  to android.app.Activity  toString android.app.Activity  toTypedArray android.app.Activity  trim android.app.Activity  updateStatus android.app.Activity  updateUIForProvider android.app.Activity  window android.app.Activity  ChessApplication android.app.Application  INSTANCE android.app.Application  Volatile android.app.Application  onCreate android.app.Application  Context android.content  Intent android.content  SharedPreferences android.content  ActivityMainBinding android.content.Context  ActivityResultContracts android.content.Context  ActivitySettingsBinding android.content.Context  AdapterView android.content.Context  	ApiConfig android.content.Context  ArrayAdapter android.content.Context  Boolean android.content.Context  Bundle android.content.Context  
CameraManager android.content.Context  ChessAnalysisScreen android.content.Context  ChessApp android.content.Context  ChessApplication android.content.Context  
ChessTheme android.content.Context  Context android.content.Context  
ContextCompat android.content.Context  EXTRA_SCANNED_FEN android.content.Context  File android.content.Context  INSTANCE android.content.Context  Int android.content.Context  Intent android.content.Context  KEY_API_PROVIDER android.content.Context  KEY_HF_MODEL android.content.Context  KEY_HF_TOKEN android.content.Context  Log android.content.Context  Long android.content.Context  MODE_PRIVATE android.content.Context  MainActivity android.content.Context  Manifest android.content.Context  
MaterialTheme android.content.Context  Menu android.content.Context  MenuItem android.content.Context  Modifier android.content.Context  NetworkManager android.content.Context  
NetworkResult android.content.Context  
PREFS_NAME android.content.Context  PackageManager android.content.Context  R android.content.Context  REQUIRED_PERMISSIONS android.content.Context  SettingsActivity android.content.Context  SharedPreferences android.content.Context  String android.content.Context  Surface android.content.Context  TAG android.content.Context  Toast android.content.Context  View android.content.Context  Volatile android.content.Context  all android.content.Context  allPermissionsGranted android.content.Context  android android.content.Context  
applySettings android.content.Context  arrayOf android.content.Context  binding android.content.Context  capturePhoto android.content.Context  displayAIResponse android.content.Context  fillMaxSize android.content.Context  finish android.content.Context  getExternalFilesDir android.content.Context  	getOrNull android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  invoke android.content.Context  isEmpty android.content.Context  java android.content.Context  launch android.content.Context  let android.content.Context  lifecycleScope android.content.Context  loadSettings android.content.Context  map android.content.Context  
mutableListOf android.content.Context  networkManager android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  onOptionsItemSelected android.content.Context  registerForActivityResult android.content.Context  saveSettings android.content.Context  
sendImageToAI android.content.Context  
setContent android.content.Context  setContentView android.content.Context  setSupportActionBar android.content.Context  setupUI android.content.Context  	showError android.content.Context  showProgressBar android.content.Context  	showToast android.content.Context  
startActivity android.content.Context  startCamera android.content.Context  
startsWith android.content.Context  testConnection android.content.Context  to android.content.Context  toString android.content.Context  toTypedArray android.content.Context  trim android.content.Context  updateStatus android.content.Context  updateUIForProvider android.content.Context  ActivityMainBinding android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  ActivitySettingsBinding android.content.ContextWrapper  AdapterView android.content.ContextWrapper  	ApiConfig android.content.ContextWrapper  ArrayAdapter android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  
CameraManager android.content.ContextWrapper  ChessAnalysisScreen android.content.ContextWrapper  ChessApp android.content.ContextWrapper  ChessApplication android.content.ContextWrapper  
ChessTheme android.content.ContextWrapper  Context android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  EXTRA_SCANNED_FEN android.content.ContextWrapper  File android.content.ContextWrapper  INSTANCE android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  KEY_API_PROVIDER android.content.ContextWrapper  KEY_HF_MODEL android.content.ContextWrapper  KEY_HF_TOKEN android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  MainActivity android.content.ContextWrapper  Manifest android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Menu android.content.ContextWrapper  MenuItem android.content.ContextWrapper  Modifier android.content.ContextWrapper  NetworkManager android.content.ContextWrapper  
NetworkResult android.content.ContextWrapper  
PREFS_NAME android.content.ContextWrapper  PackageManager android.content.ContextWrapper  R android.content.ContextWrapper  REQUIRED_PERMISSIONS android.content.ContextWrapper  SettingsActivity android.content.ContextWrapper  SharedPreferences android.content.ContextWrapper  String android.content.ContextWrapper  Surface android.content.ContextWrapper  TAG android.content.ContextWrapper  Toast android.content.ContextWrapper  View android.content.ContextWrapper  Volatile android.content.ContextWrapper  all android.content.ContextWrapper  allPermissionsGranted android.content.ContextWrapper  android android.content.ContextWrapper  
applySettings android.content.ContextWrapper  arrayOf android.content.ContextWrapper  binding android.content.ContextWrapper  capturePhoto android.content.ContextWrapper  displayAIResponse android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  finish android.content.ContextWrapper  	getOrNull android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  	getString android.content.ContextWrapper  invoke android.content.ContextWrapper  isEmpty android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  loadSettings android.content.ContextWrapper  map android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  networkManager android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  onOptionsItemSelected android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  saveSettings android.content.ContextWrapper  
sendImageToAI android.content.ContextWrapper  
setContent android.content.ContextWrapper  setContentView android.content.ContextWrapper  setSupportActionBar android.content.ContextWrapper  setupUI android.content.ContextWrapper  	showError android.content.ContextWrapper  showProgressBar android.content.ContextWrapper  	showToast android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startCamera android.content.ContextWrapper  
startsWith android.content.ContextWrapper  testConnection android.content.ContextWrapper  to android.content.ContextWrapper  toString android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  trim android.content.ContextWrapper  updateStatus android.content.ContextWrapper  updateUIForProvider android.content.ContextWrapper  getStringExtra android.content.Intent  putExtra android.content.Intent  Editor !android.content.SharedPreferences  edit !android.content.SharedPreferences  getInt !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  getTOString android.text.Editable  getToString android.text.Editable  toString android.text.Editable  Base64 android.util  Log android.util  NO_WRAP android.util.Base64  encodeToString android.util.Base64  d android.util.Log  e android.util.Log  w android.util.Log  LayoutInflater android.view  Menu android.view  MenuInflater android.view  MenuItem android.view  View android.view  Window android.view  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityResultContracts  android.view.ContextThemeWrapper  ActivitySettingsBinding  android.view.ContextThemeWrapper  AdapterView  android.view.ContextThemeWrapper  	ApiConfig  android.view.ContextThemeWrapper  ArrayAdapter  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  
CameraManager  android.view.ContextThemeWrapper  ChessAnalysisScreen  android.view.ContextThemeWrapper  ChessApp  android.view.ContextThemeWrapper  
ChessTheme  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  EXTRA_SCANNED_FEN  android.view.ContextThemeWrapper  File  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  KEY_API_PROVIDER  android.view.ContextThemeWrapper  KEY_HF_MODEL  android.view.ContextThemeWrapper  KEY_HF_TOKEN  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Menu  android.view.ContextThemeWrapper  MenuItem  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  NetworkManager  android.view.ContextThemeWrapper  
NetworkResult  android.view.ContextThemeWrapper  
PREFS_NAME  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  REQUIRED_PERMISSIONS  android.view.ContextThemeWrapper  SettingsActivity  android.view.ContextThemeWrapper  SharedPreferences  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  all  android.view.ContextThemeWrapper  allPermissionsGranted  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  
applySettings  android.view.ContextThemeWrapper  arrayOf  android.view.ContextThemeWrapper  binding  android.view.ContextThemeWrapper  capturePhoto  android.view.ContextThemeWrapper  displayAIResponse  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  	getOrNull  android.view.ContextThemeWrapper  getSharedPreferences  android.view.ContextThemeWrapper  	getString  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  loadSettings  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  networkManager  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  	onDestroy  android.view.ContextThemeWrapper  onOptionsItemSelected  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  saveSettings  android.view.ContextThemeWrapper  
sendImageToAI  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  setSupportActionBar  android.view.ContextThemeWrapper  setupUI  android.view.ContextThemeWrapper  	showError  android.view.ContextThemeWrapper  showProgressBar  android.view.ContextThemeWrapper  	showToast  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  startCamera  android.view.ContextThemeWrapper  
startsWith  android.view.ContextThemeWrapper  testConnection  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  toString  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  updateStatus  android.view.ContextThemeWrapper  updateUIForProvider  android.view.ContextThemeWrapper  inflate android.view.MenuInflater  	getITEMId android.view.MenuItem  	getItemId android.view.MenuItem  itemId android.view.MenuItem  	setItemId android.view.MenuItem  GONE android.view.View  VISIBLE android.view.View  addJavascriptInterface android.view.View  also android.view.View  apply android.view.View  context android.view.View  evaluateJavascript android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  isInEditMode android.view.View  let android.view.View  loadUrl android.view.View  
setContext android.view.View  
setInEditMode android.view.View  setOnClickListener android.view.View  setSelection android.view.View  setText android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  addJavascriptInterface android.view.ViewGroup  also android.view.ViewGroup  apply android.view.ViewGroup  evaluateJavascript android.view.ViewGroup  let android.view.ViewGroup  loadUrl android.view.ViewGroup  setSelection android.view.ViewGroup  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  JavascriptInterface android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  apply android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  getALLOWContentAccess android.webkit.WebSettings  getALLOWFileAccess android.webkit.WebSettings  getAPPLY android.webkit.WebSettings  getAllowContentAccess android.webkit.WebSettings  getAllowFileAccess android.webkit.WebSettings  getApply android.webkit.WebSettings  getDOMStorageEnabled android.webkit.WebSettings  getDomStorageEnabled android.webkit.WebSettings  getJAVAScriptEnabled android.webkit.WebSettings  getJavaScriptEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  setAllowContentAccess android.webkit.WebSettings  setAllowFileAccess android.webkit.WebSettings  setDomStorageEnabled android.webkit.WebSettings  setJavaScriptEnabled android.webkit.WebSettings  ChessBoardInterface android.webkit.WebView  Log android.webkit.WebView  addJavascriptInterface android.webkit.WebView  also android.webkit.WebView  apply android.webkit.WebView  evaluateJavascript android.webkit.WebView  getALSO android.webkit.WebView  getAPPLY android.webkit.WebView  getAlso android.webkit.WebView  getApply android.webkit.WebView  getLET android.webkit.WebView  getLet android.webkit.WebView  getSETTINGS android.webkit.WebView  getSettings android.webkit.WebView  getWEBViewClient android.webkit.WebView  getWebViewClient android.webkit.WebView  let android.webkit.WebView  loadUrl android.webkit.WebView  setSettings android.webkit.WebView  setWebViewClient android.webkit.WebView  settings android.webkit.WebView  
webViewClient android.webkit.WebView  Log android.webkit.WebViewClient  String android.webkit.WebViewClient  WebView android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  AdapterView android.widget  ArrayAdapter android.widget  SpinnerAdapter android.widget  Toast android.widget  setSelection android.widget.AbsSpinner  addJavascriptInterface android.widget.AbsoluteLayout  also android.widget.AbsoluteLayout  apply android.widget.AbsoluteLayout  evaluateJavascript android.widget.AbsoluteLayout  let android.widget.AbsoluteLayout  loadUrl android.widget.AbsoluteLayout  OnItemSelectedListener android.widget.AdapterView  setSelection android.widget.AdapterView  setDropDownViewResource android.widget.ArrayAdapter  setDropDownViewResource android.widget.BaseAdapter  getISEnabled android.widget.Button  getIsEnabled android.widget.Button  	isEnabled android.widget.Button  
setEnabled android.widget.Button  setOnClickListener android.widget.Button  setText android.widget.EditText  
getVISIBILITY android.widget.ProgressBar  
getVisibility android.widget.ProgressBar  
setVisibility android.widget.ProgressBar  
visibility android.widget.ProgressBar  adapter android.widget.Spinner  
getADAPTER android.widget.Spinner  
getAdapter android.widget.Spinner  getONItemSelectedListener android.widget.Spinner  getOnItemSelectedListener android.widget.Spinner  getSELECTEDItemPosition android.widget.Spinner  getSelectedItemPosition android.widget.Spinner  onItemSelectedListener android.widget.Spinner  selectedItemPosition android.widget.Spinner  
setAdapter android.widget.Spinner  setOnItemSelectedListener android.widget.Spinner  setSelectedItemPosition android.widget.Spinner  setSelection android.widget.Spinner  getTEXT android.widget.TextView  getText android.widget.TextView  setOnClickListener android.widget.TextView  setText android.widget.TextView  text android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityResultContracts #androidx.activity.ComponentActivity  ActivitySettingsBinding #androidx.activity.ComponentActivity  AdapterView #androidx.activity.ComponentActivity  	ApiConfig #androidx.activity.ComponentActivity  ArrayAdapter #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  
CameraManager #androidx.activity.ComponentActivity  ChessAnalysisScreen #androidx.activity.ComponentActivity  ChessApp #androidx.activity.ComponentActivity  
ChessTheme #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  EXTRA_SCANNED_FEN #androidx.activity.ComponentActivity  File #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  KEY_API_PROVIDER #androidx.activity.ComponentActivity  KEY_HF_MODEL #androidx.activity.ComponentActivity  KEY_HF_TOKEN #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Menu #androidx.activity.ComponentActivity  MenuItem #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  NetworkManager #androidx.activity.ComponentActivity  
NetworkResult #androidx.activity.ComponentActivity  
PREFS_NAME #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  REQUIRED_PERMISSIONS #androidx.activity.ComponentActivity  SettingsActivity #androidx.activity.ComponentActivity  SharedPreferences #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  all #androidx.activity.ComponentActivity  allPermissionsGranted #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  
applySettings #androidx.activity.ComponentActivity  arrayOf #androidx.activity.ComponentActivity  binding #androidx.activity.ComponentActivity  capturePhoto #androidx.activity.ComponentActivity  displayAIResponse #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  	getOrNull #androidx.activity.ComponentActivity  getSharedPreferences #androidx.activity.ComponentActivity  	getString #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  loadSettings #androidx.activity.ComponentActivity  map #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  networkManager #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  onOptionsItemSelected #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  saveSettings #androidx.activity.ComponentActivity  
sendImageToAI #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  setSupportActionBar #androidx.activity.ComponentActivity  setupUI #androidx.activity.ComponentActivity  	showError #androidx.activity.ComponentActivity  showProgressBar #androidx.activity.ComponentActivity  	showToast #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  startCamera #androidx.activity.ComponentActivity  
startsWith #androidx.activity.ComponentActivity  testConnection #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  toString #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  updateStatus #androidx.activity.ComponentActivity  updateUIForProvider #androidx.activity.ComponentActivity  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  invoke ^androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions.Companion  	ActionBar androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  getTITLE  androidx.appcompat.app.ActionBar  getTitle  androidx.appcompat.app.ActionBar  setDisplayHomeAsUpEnabled  androidx.appcompat.app.ActionBar  setTitle  androidx.appcompat.app.ActionBar  title  androidx.appcompat.app.ActionBar  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultContracts (androidx.appcompat.app.AppCompatActivity  ActivitySettingsBinding (androidx.appcompat.app.AppCompatActivity  AdapterView (androidx.appcompat.app.AppCompatActivity  	ApiConfig (androidx.appcompat.app.AppCompatActivity  ArrayAdapter (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  
CameraManager (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  File (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  KEY_API_PROVIDER (androidx.appcompat.app.AppCompatActivity  KEY_HF_MODEL (androidx.appcompat.app.AppCompatActivity  KEY_HF_TOKEN (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  Long (androidx.appcompat.app.AppCompatActivity  Manifest (androidx.appcompat.app.AppCompatActivity  Menu (androidx.appcompat.app.AppCompatActivity  MenuItem (androidx.appcompat.app.AppCompatActivity  NetworkManager (androidx.appcompat.app.AppCompatActivity  
NetworkResult (androidx.appcompat.app.AppCompatActivity  
PREFS_NAME (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  REQUIRED_PERMISSIONS (androidx.appcompat.app.AppCompatActivity  SettingsActivity (androidx.appcompat.app.AppCompatActivity  SharedPreferences (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  all (androidx.appcompat.app.AppCompatActivity  allPermissionsGranted (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  
applySettings (androidx.appcompat.app.AppCompatActivity  arrayOf (androidx.appcompat.app.AppCompatActivity  binding (androidx.appcompat.app.AppCompatActivity  capturePhoto (androidx.appcompat.app.AppCompatActivity  displayAIResponse (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  	getOrNull (androidx.appcompat.app.AppCompatActivity  getSharedPreferences (androidx.appcompat.app.AppCompatActivity  	getString (androidx.appcompat.app.AppCompatActivity  invoke (androidx.appcompat.app.AppCompatActivity  isEmpty (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  loadSettings (androidx.appcompat.app.AppCompatActivity  map (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  networkManager (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onOptionsItemSelected (androidx.appcompat.app.AppCompatActivity  registerForActivityResult (androidx.appcompat.app.AppCompatActivity  saveSettings (androidx.appcompat.app.AppCompatActivity  
sendImageToAI (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  setSupportActionBar (androidx.appcompat.app.AppCompatActivity  setupUI (androidx.appcompat.app.AppCompatActivity  	showError (androidx.appcompat.app.AppCompatActivity  showProgressBar (androidx.appcompat.app.AppCompatActivity  	showToast (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  startCamera (androidx.appcompat.app.AppCompatActivity  
startsWith (androidx.appcompat.app.AppCompatActivity  testConnection (androidx.appcompat.app.AppCompatActivity  to (androidx.appcompat.app.AppCompatActivity  toString (androidx.appcompat.app.AppCompatActivity  toTypedArray (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  updateStatus (androidx.appcompat.app.AppCompatActivity  updateUIForProvider (androidx.appcompat.app.AppCompatActivity  Toolbar androidx.appcompat.widget  setText +androidx.appcompat.widget.AppCompatEditText  ActivityResultContracts androidx.camera.core  AlertDialog androidx.camera.core  	Alignment androidx.camera.core  AndroidView androidx.camera.core  Arrangement androidx.camera.core  	ArrowBack androidx.camera.core  Box androidx.camera.core  Button androidx.camera.core  ButtonDefaults androidx.camera.core  Camera androidx.camera.core  	CameraAlt androidx.camera.core  CameraSelector androidx.camera.core  Card androidx.camera.core  CardDefaults androidx.camera.core  CheckCircle androidx.camera.core  CircularProgressIndicator androidx.camera.core  Color androidx.camera.core  Column androidx.camera.core  
Composable androidx.camera.core  
ContextCompat androidx.camera.core  	Exception androidx.camera.core  	Executors androidx.camera.core  ExperimentalMaterial3Api androidx.camera.core  FILENAME_FORMAT androidx.camera.core  File androidx.camera.core  FlashOff androidx.camera.core  FlashOn androidx.camera.core  
FontWeight androidx.camera.core  Icon androidx.camera.core  
IconButton androidx.camera.core  Icons androidx.camera.core  IllegalStateException androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  LaunchedEffect androidx.camera.core  Locale androidx.camera.core  Log androidx.camera.core  Manifest androidx.camera.core  
MaterialTheme androidx.camera.core  Modifier androidx.camera.core  OutlinedButton androidx.camera.core  PhotoLibrary androidx.camera.core  Preview androidx.camera.core  ProcessCameraProvider androidx.camera.core  RoundedCornerShape androidx.camera.core  Row androidx.camera.core  SimpleDateFormat androidx.camera.core  Spacer androidx.camera.core  System androidx.camera.core  TAG androidx.camera.core  Text androidx.camera.core  
TextButton androidx.camera.core  	TopAppBar androidx.camera.core  TopAppBarDefaults androidx.camera.core  addListener androidx.camera.core  also androidx.camera.core  androidx androidx.camera.core  aspectRatio androidx.camera.core  
background androidx.camera.core  border androidx.camera.core  clip androidx.camera.core  fillMaxSize androidx.camera.core  fillMaxWidth androidx.camera.core  getValue androidx.camera.core  height androidx.camera.core  launch androidx.camera.core  let androidx.camera.core  mutableStateOf androidx.camera.core  padding androidx.camera.core  provideDelegate androidx.camera.core  remember androidx.camera.core  rememberCoroutineScope androidx.camera.core  run androidx.camera.core  setValue androidx.camera.core  size androidx.camera.core  width androidx.camera.core  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  Builder !androidx.camera.core.ImageCapture  CAPTURE_MODE_MINIMIZE_LATENCY !androidx.camera.core.ImageCapture  OnImageSavedCallback !androidx.camera.core.ImageCapture  OutputFileOptions !androidx.camera.core.ImageCapture  OutputFileResults !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setCaptureMode )androidx.camera.core.ImageCapture.Builder  setJpegQuality )androidx.camera.core.ImageCapture.Builder  Builder 3androidx.camera.core.ImageCapture.OutputFileOptions  build ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  message *androidx.camera.core.ImageCaptureException  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  getALSO androidx.camera.core.Preview  getAlso androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  also androidx.camera.core.UseCase  setSurfaceProvider androidx.camera.core.UseCase  takePicture androidx.camera.core.UseCase  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  PreviewView androidx.camera.view  getSURFACEProvider  androidx.camera.view.PreviewView  getSurfaceProvider  androidx.camera.view.PreviewView  setSurfaceProvider  androidx.camera.view.PreviewView  surfaceProvider  androidx.camera.view.PreviewView  AnimatedContentScope androidx.compose.animation  AnalysisScreen /androidx.compose.animation.AnimatedContentScope  CameraScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  
background androidx.compose.foundation  border androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AnalysisScreen "androidx.compose.foundation.layout  	Analytics "androidx.compose.foundation.layout  AndroidView "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  	ArrowBack "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Camera "androidx.compose.foundation.layout  	CameraAlt "androidx.compose.foundation.layout  CameraScreen "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CheckCircle "androidx.compose.foundation.layout  ChessAnalysisScreen "androidx.compose.foundation.layout  ChessApp "androidx.compose.foundation.layout  ChessBoardCard "androidx.compose.foundation.layout  ChessBoardWebView "androidx.compose.foundation.layout  ChessColors "androidx.compose.foundation.layout  
ChessTheme "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Close "androidx.compose.foundation.layout  Cloud "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  ColorScheme "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  EXTRA_SCANNED_FEN "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  EngineAnalysisCard "androidx.compose.foundation.layout  
EvaluationBar "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FeatureItem "androidx.compose.foundation.layout  FlashOff "androidx.compose.foundation.layout  FlashOn "androidx.compose.foundation.layout  
FlipToBack "androidx.compose.foundation.layout  
FontFamily "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Home "androidx.compose.foundation.layout  
HomeScreen "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  MainActivity "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  MoreVert "androidx.compose.foundation.layout  MoveHistoryCard "androidx.compose.foundation.layout  
NavigationBar "androidx.compose.foundation.layout  NavigationBarItem "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Pause "androidx.compose.foundation.layout  PhotoLibrary "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  PositionInfoCard "androidx.compose.foundation.layout  
Psychology "androidx.compose.foundation.layout  
RestartAlt "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  SettingsActivity "androidx.compose.foundation.layout  SettingsScreen "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Speed "androidx.compose.foundation.layout  StockfishManager "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  aspectRatio "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  border "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  coerceIn "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  
composable "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  systemBarsPadding "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  	CameraAlt +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Manifest +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  StockfishManager +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  border +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  
fillMaxHeight +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  
getBACKGROUND +androidx.compose.foundation.layout.BoxScope  	getBORDER +androidx.compose.foundation.layout.BoxScope  
getBackground +androidx.compose.foundation.layout.BoxScope  	getBorder +androidx.compose.foundation.layout.BoxScope  getCLIP +androidx.compose.foundation.layout.BoxScope  getClip +androidx.compose.foundation.layout.BoxScope  getFILLMaxHeight +androidx.compose.foundation.layout.BoxScope  getFILLMaxSize +androidx.compose.foundation.layout.BoxScope  getFILLMaxWidth +androidx.compose.foundation.layout.BoxScope  getFillMaxHeight +androidx.compose.foundation.layout.BoxScope  getFillMaxSize +androidx.compose.foundation.layout.BoxScope  getFillMaxWidth +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  getSIZE +androidx.compose.foundation.layout.BoxScope  getSize +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  AlertDialog .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  	Analytics .androidx.compose.foundation.layout.ColumnScope  AndroidView .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  	CameraAlt .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CheckCircle .androidx.compose.foundation.layout.ColumnScope  ChessBoardCard .androidx.compose.foundation.layout.ColumnScope  ChessBoardWebView .androidx.compose.foundation.layout.ColumnScope  ChessColors .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Close .androidx.compose.foundation.layout.ColumnScope  Cloud .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  EngineAnalysisCard .androidx.compose.foundation.layout.ColumnScope  
EvaluationBar .androidx.compose.foundation.layout.ColumnScope  FeatureItem .androidx.compose.foundation.layout.ColumnScope  FlashOff .androidx.compose.foundation.layout.ColumnScope  FlashOn .androidx.compose.foundation.layout.ColumnScope  
FlipToBack .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  Manifest .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  MoreVert .androidx.compose.foundation.layout.ColumnScope  MoveHistoryCard .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  Pause .androidx.compose.foundation.layout.ColumnScope  PhotoLibrary .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  PositionInfoCard .androidx.compose.foundation.layout.ColumnScope  
Psychology .androidx.compose.foundation.layout.ColumnScope  
RestartAlt .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Speed .androidx.compose.foundation.layout.ColumnScope  StockfishManager .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  	TopAppBar .androidx.compose.foundation.layout.ColumnScope  TopAppBarDefaults .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  aspectRatio .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  border .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getANDROIDX .androidx.compose.foundation.layout.ColumnScope  getASPECTRatio .androidx.compose.foundation.layout.ColumnScope  getAndroidx .androidx.compose.foundation.layout.ColumnScope  getAspectRatio .androidx.compose.foundation.layout.ColumnScope  
getBACKGROUND .androidx.compose.foundation.layout.ColumnScope  	getBORDER .androidx.compose.foundation.layout.ColumnScope  
getBackground .androidx.compose.foundation.layout.ColumnScope  	getBorder .androidx.compose.foundation.layout.ColumnScope  getCLIP .androidx.compose.foundation.layout.ColumnScope  getClip .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getISNotEmpty .androidx.compose.foundation.layout.ColumnScope  
getIsNotEmpty .androidx.compose.foundation.layout.ColumnScope  	getLAUNCH .androidx.compose.foundation.layout.ColumnScope  getLET .androidx.compose.foundation.layout.ColumnScope  	getLaunch .androidx.compose.foundation.layout.ColumnScope  getLet .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getTAKE .androidx.compose.foundation.layout.ColumnScope  getTake .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  take .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  	Analytics +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  	CameraAlt +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  CheckCircle +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Close +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  FlashOff +androidx.compose.foundation.layout.RowScope  FlashOn +androidx.compose.foundation.layout.RowScope  
FlipToBack +androidx.compose.foundation.layout.RowScope  
FontFamily +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Home +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MoreVert +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Pause +androidx.compose.foundation.layout.RowScope  PhotoLibrary +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  
Psychology +androidx.compose.foundation.layout.RowScope  
RestartAlt +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  StockfishManager +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  getFILLMaxSize +androidx.compose.foundation.layout.RowScope  getFillMaxSize +androidx.compose.foundation.layout.RowScope  	getHEIGHT +androidx.compose.foundation.layout.RowScope  	getHeight +androidx.compose.foundation.layout.RowScope  	getLAUNCH +androidx.compose.foundation.layout.RowScope  getLET +androidx.compose.foundation.layout.RowScope  	getLaunch +androidx.compose.foundation.layout.RowScope  getLet +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  invoke +androidx.compose.foundation.layout.RowScope  launch +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  	Analytics .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  	CameraAlt .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  ChessBoardCard .androidx.compose.foundation.lazy.LazyItemScope  ChessColors .androidx.compose.foundation.lazy.LazyItemScope  Cloud .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  EngineAnalysisCard .androidx.compose.foundation.lazy.LazyItemScope  FeatureItem .androidx.compose.foundation.lazy.LazyItemScope  
FontFamily .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  MoveHistoryCard .androidx.compose.foundation.lazy.LazyItemScope  PositionInfoCard .androidx.compose.foundation.lazy.LazyItemScope  
Psychology .androidx.compose.foundation.lazy.LazyItemScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Speed .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  	TextAlign .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxSize .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getFILLMaxSize .androidx.compose.foundation.lazy.LazyItemScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getFillMaxSize .androidx.compose.foundation.lazy.LazyItemScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyItemScope  	getHeight .androidx.compose.foundation.lazy.LazyItemScope  	getLAUNCH .androidx.compose.foundation.lazy.LazyItemScope  	getLaunch .androidx.compose.foundation.lazy.LazyItemScope  
getPADDING .androidx.compose.foundation.lazy.LazyItemScope  
getPadding .androidx.compose.foundation.lazy.LazyItemScope  getSIZE .androidx.compose.foundation.lazy.LazyItemScope  getSize .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  launch .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  	Analytics .androidx.compose.foundation.lazy.LazyListScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  	CameraAlt .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  ChessBoardCard .androidx.compose.foundation.lazy.LazyListScope  ChessColors .androidx.compose.foundation.lazy.LazyListScope  Cloud .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  EngineAnalysisCard .androidx.compose.foundation.lazy.LazyListScope  FeatureItem .androidx.compose.foundation.lazy.LazyListScope  
FontFamily .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  MoveHistoryCard .androidx.compose.foundation.lazy.LazyListScope  PositionInfoCard .androidx.compose.foundation.lazy.LazyListScope  
Psychology .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Speed .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  	TextAlign .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxSize .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getFILLMaxSize .androidx.compose.foundation.lazy.LazyListScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getFillMaxSize .androidx.compose.foundation.lazy.LazyListScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyListScope  	getHeight .androidx.compose.foundation.lazy.LazyListScope  
getISNotEmpty .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  
getIsNotEmpty .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  	getLAUNCH .androidx.compose.foundation.lazy.LazyListScope  	getLaunch .androidx.compose.foundation.lazy.LazyListScope  
getPADDING .androidx.compose.foundation.lazy.LazyListScope  
getPadding .androidx.compose.foundation.lazy.LazyListScope  getSIZE .androidx.compose.foundation.lazy.LazyListScope  getSize .androidx.compose.foundation.lazy.LazyListScope  getTAKE .androidx.compose.foundation.lazy.LazyListScope  getTake .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  launch .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  take .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  	Analytics ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  	CameraAlt ,androidx.compose.material.icons.Icons.Filled  CheckCircle ,androidx.compose.material.icons.Icons.Filled  Close ,androidx.compose.material.icons.Icons.Filled  Cloud ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  FlashOff ,androidx.compose.material.icons.Icons.Filled  FlashOn ,androidx.compose.material.icons.Icons.Filled  
FlipToBack ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  MoreVert ,androidx.compose.material.icons.Icons.Filled  Pause ,androidx.compose.material.icons.Icons.Filled  PhotoLibrary ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  
Psychology ,androidx.compose.material.icons.Icons.Filled  
RestartAlt ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Speed ,androidx.compose.material.icons.Icons.Filled  ActivityResultContracts &androidx.compose.material.icons.filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  	Analytics &androidx.compose.material.icons.filled  AndroidView &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Camera &androidx.compose.material.icons.filled  	CameraAlt &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  ChessAnalysisScreen &androidx.compose.material.icons.filled  ChessBoardCard &androidx.compose.material.icons.filled  ChessBoardWebView &androidx.compose.material.icons.filled  ChessColors &androidx.compose.material.icons.filled  
ChessTheme &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  Cloud &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  ColorScheme &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  EXTRA_SCANNED_FEN &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  EngineAnalysisCard &androidx.compose.material.icons.filled  
EvaluationBar &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  FeatureItem &androidx.compose.material.icons.filled  FlashOff &androidx.compose.material.icons.filled  FlashOn &androidx.compose.material.icons.filled  
FlipToBack &androidx.compose.material.icons.filled  
FontFamily &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Intent &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LazyRow &androidx.compose.material.icons.filled  MainActivity &androidx.compose.material.icons.filled  Manifest &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  MoveHistoryCard &androidx.compose.material.icons.filled  
NavigationBar &androidx.compose.material.icons.filled  NavigationBarItem &androidx.compose.material.icons.filled  OutlinedButton &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  
PaddingValues &androidx.compose.material.icons.filled  Pause &androidx.compose.material.icons.filled  PhotoLibrary &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  PositionInfoCard &androidx.compose.material.icons.filled  
Psychology &androidx.compose.material.icons.filled  
RestartAlt &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Scaffold &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Speed &androidx.compose.material.icons.filled  StockfishManager &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  	TopAppBar &androidx.compose.material.icons.filled  TopAppBarDefaults &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  aspectRatio &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  border &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  coerceIn &androidx.compose.material.icons.filled  collectAsState &androidx.compose.material.icons.filled  
fillMaxHeight &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  items &androidx.compose.material.icons.filled  java &androidx.compose.material.icons.filled  launch &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  rememberCoroutineScope &androidx.compose.material.icons.filled  
setContent &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  systemBarsPadding &androidx.compose.material.icons.filled  take &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  AnalysisScreen androidx.compose.material3  	Analytics androidx.compose.material3  AndroidView androidx.compose.material3  Arrangement androidx.compose.material3  	ArrowBack androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Camera androidx.compose.material3  	CameraAlt androidx.compose.material3  CameraScreen androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CheckCircle androidx.compose.material3  ChessAnalysisBlue androidx.compose.material3  ChessAnalysisGreen androidx.compose.material3  ChessAnalysisRed androidx.compose.material3  ChessAnalysisScreen androidx.compose.material3  ChessApp androidx.compose.material3  ChessBoardCard androidx.compose.material3  ChessBoardWebView androidx.compose.material3  ChessColors androidx.compose.material3  ChessPrimary androidx.compose.material3  ChessScanAccent androidx.compose.material3  ChessSecondary androidx.compose.material3  ChessSurface androidx.compose.material3  ChessSurfaceVariant androidx.compose.material3  
ChessTheme androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Close androidx.compose.material3  Cloud androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  EXTRA_SCANNED_FEN androidx.compose.material3  Edit androidx.compose.material3  EngineAnalysisCard androidx.compose.material3  
EvaluationBar androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FeatureItem androidx.compose.material3  FlashOff androidx.compose.material3  FlashOn androidx.compose.material3  
FlipToBack androidx.compose.material3  
FontFamily androidx.compose.material3  
FontWeight androidx.compose.material3  Home androidx.compose.material3  
HomeScreen androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Intent androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  MainActivity androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  MoreVert androidx.compose.material3  MoveHistoryCard androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  
PaddingValues androidx.compose.material3  Pause androidx.compose.material3  PhotoLibrary androidx.compose.material3  	PlayArrow androidx.compose.material3  PositionInfoCard androidx.compose.material3  
Psychology androidx.compose.material3  
RestartAlt androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Settings androidx.compose.material3  SettingsActivity androidx.compose.material3  SettingsScreen androidx.compose.material3  Shapes androidx.compose.material3  Spacer androidx.compose.material3  Speed androidx.compose.material3  StockfishManager androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  androidx androidx.compose.material3  aspectRatio androidx.compose.material3  
background androidx.compose.material3  border androidx.compose.material3  clip androidx.compose.material3  coerceIn androidx.compose.material3  collectAsState androidx.compose.material3  
composable androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  
fillMaxHeight androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  invoke androidx.compose.material3  
isNotEmpty androidx.compose.material3  items androidx.compose.material3  java androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  
setContent androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  systemBarsPadding androidx.compose.material3  take androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSecondary &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  
onTertiary &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  tertiary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
labelLarge %androidx.compose.material3.Typography  labelMedium %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  ActivityResultContracts androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  AnalysisScreen androidx.compose.runtime  AndroidView androidx.compose.runtime  Arrangement androidx.compose.runtime  	ArrowBack androidx.compose.runtime  BoardOrientation androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Camera androidx.compose.runtime  	CameraAlt androidx.compose.runtime  CameraScreen androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CheckCircle androidx.compose.runtime  ChessAnalysisScreen androidx.compose.runtime  ChessApp androidx.compose.runtime  ChessBoardCard androidx.compose.runtime  ChessBoardInterface androidx.compose.runtime  ChessBoardWebView androidx.compose.runtime  ChessColors androidx.compose.runtime  
ChessTheme androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Close androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  EXTRA_SCANNED_FEN androidx.compose.runtime  Edit androidx.compose.runtime  EngineAnalysisCard androidx.compose.runtime  
EvaluationBar androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FlashOff androidx.compose.runtime  FlashOn androidx.compose.runtime  
FlipToBack androidx.compose.runtime  
FontFamily androidx.compose.runtime  
FontWeight androidx.compose.runtime  
HomeScreen androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Intent androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  Log androidx.compose.runtime  MainActivity androidx.compose.runtime  Manifest androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MoreVert androidx.compose.runtime  MoveHistoryCard androidx.compose.runtime  MutableState androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  
PaddingValues androidx.compose.runtime  Pause androidx.compose.runtime  PhotoLibrary androidx.compose.runtime  
PieceColor androidx.compose.runtime  	PlayArrow androidx.compose.runtime  PositionInfo androidx.compose.runtime  PositionInfoCard androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
Psychology androidx.compose.runtime  
RestartAlt androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  SettingsActivity androidx.compose.runtime  SettingsScreen androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  Square androidx.compose.runtime  State androidx.compose.runtime  StockfishManager androidx.compose.runtime  Surface androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  also androidx.compose.runtime  androidx androidx.compose.runtime  apply androidx.compose.runtime  aspectRatio androidx.compose.runtime  
background androidx.compose.runtime  border androidx.compose.runtime  clip androidx.compose.runtime  coerceIn androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  
composable androidx.compose.runtime  	emptyList androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  filter androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  items androidx.compose.runtime  java androidx.compose.runtime  joinToString androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  	lowercase androidx.compose.runtime  map androidx.compose.runtime  minusAssign androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  
plusAssign androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  
setContent androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  	substring androidx.compose.runtime  systemBarsPadding androidx.compose.runtime  take androidx.compose.runtime  	uppercase androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  End androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  	TopCenter androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  End 'androidx.compose.ui.Alignment.Companion  	TopCenter 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  aspectRatio androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  getASPECTRatio androidx.compose.ui.Modifier  getAspectRatio androidx.compose.ui.Modifier  
getBACKGROUND androidx.compose.ui.Modifier  	getBORDER androidx.compose.ui.Modifier  
getBackground androidx.compose.ui.Modifier  	getBorder androidx.compose.ui.Modifier  getCLIP androidx.compose.ui.Modifier  getClip androidx.compose.ui.Modifier  getFILLMaxWidth androidx.compose.ui.Modifier  getFillMaxWidth androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  getSYSTEMBarsPadding androidx.compose.ui.Modifier  getSystemBarsPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  systemBarsPadding androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  
fillMaxHeight &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getALIGN &androidx.compose.ui.Modifier.Companion  getAlign &androidx.compose.ui.Modifier.Companion  
getBACKGROUND &androidx.compose.ui.Modifier.Companion  
getBackground &androidx.compose.ui.Modifier.Companion  getFILLMaxHeight &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxHeight &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Light (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Light 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  ConstraintLayout  androidx.constraintlayout.widget  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityResultContracts #androidx.core.app.ComponentActivity  ActivitySettingsBinding #androidx.core.app.ComponentActivity  AdapterView #androidx.core.app.ComponentActivity  	ApiConfig #androidx.core.app.ComponentActivity  ArrayAdapter #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
CameraManager #androidx.core.app.ComponentActivity  ChessAnalysisScreen #androidx.core.app.ComponentActivity  ChessApp #androidx.core.app.ComponentActivity  
ChessTheme #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  EXTRA_SCANNED_FEN #androidx.core.app.ComponentActivity  File #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  KEY_API_PROVIDER #androidx.core.app.ComponentActivity  KEY_HF_MODEL #androidx.core.app.ComponentActivity  KEY_HF_TOKEN #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Menu #androidx.core.app.ComponentActivity  MenuItem #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  NetworkManager #androidx.core.app.ComponentActivity  
NetworkResult #androidx.core.app.ComponentActivity  
PREFS_NAME #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  REQUIRED_PERMISSIONS #androidx.core.app.ComponentActivity  SettingsActivity #androidx.core.app.ComponentActivity  SharedPreferences #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  all #androidx.core.app.ComponentActivity  allPermissionsGranted #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  
applySettings #androidx.core.app.ComponentActivity  arrayOf #androidx.core.app.ComponentActivity  binding #androidx.core.app.ComponentActivity  capturePhoto #androidx.core.app.ComponentActivity  displayAIResponse #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  	getOrNull #androidx.core.app.ComponentActivity  getSharedPreferences #androidx.core.app.ComponentActivity  	getString #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  loadSettings #androidx.core.app.ComponentActivity  map #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  networkManager #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  	onDestroy #androidx.core.app.ComponentActivity  onOptionsItemSelected #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  saveSettings #androidx.core.app.ComponentActivity  
sendImageToAI #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  setSupportActionBar #androidx.core.app.ComponentActivity  setupUI #androidx.core.app.ComponentActivity  	showError #androidx.core.app.ComponentActivity  showProgressBar #androidx.core.app.ComponentActivity  	showToast #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  startCamera #androidx.core.app.ComponentActivity  
startsWith #androidx.core.app.ComponentActivity  testConnection #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  toString #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  updateStatus #androidx.core.app.ComponentActivity  updateUIForProvider #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityResultContracts &androidx.fragment.app.FragmentActivity  ActivitySettingsBinding &androidx.fragment.app.FragmentActivity  AdapterView &androidx.fragment.app.FragmentActivity  	ApiConfig &androidx.fragment.app.FragmentActivity  ArrayAdapter &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  
CameraManager &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  File &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  KEY_API_PROVIDER &androidx.fragment.app.FragmentActivity  KEY_HF_MODEL &androidx.fragment.app.FragmentActivity  KEY_HF_TOKEN &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  Long &androidx.fragment.app.FragmentActivity  Manifest &androidx.fragment.app.FragmentActivity  Menu &androidx.fragment.app.FragmentActivity  MenuItem &androidx.fragment.app.FragmentActivity  NetworkManager &androidx.fragment.app.FragmentActivity  
NetworkResult &androidx.fragment.app.FragmentActivity  
PREFS_NAME &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  REQUIRED_PERMISSIONS &androidx.fragment.app.FragmentActivity  SettingsActivity &androidx.fragment.app.FragmentActivity  SharedPreferences &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  all &androidx.fragment.app.FragmentActivity  allPermissionsGranted &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  
applySettings &androidx.fragment.app.FragmentActivity  arrayOf &androidx.fragment.app.FragmentActivity  binding &androidx.fragment.app.FragmentActivity  capturePhoto &androidx.fragment.app.FragmentActivity  displayAIResponse &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  	getOrNull &androidx.fragment.app.FragmentActivity  getSharedPreferences &androidx.fragment.app.FragmentActivity  	getString &androidx.fragment.app.FragmentActivity  invoke &androidx.fragment.app.FragmentActivity  isEmpty &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  loadSettings &androidx.fragment.app.FragmentActivity  map &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  networkManager &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  	onDestroy &androidx.fragment.app.FragmentActivity  onOptionsItemSelected &androidx.fragment.app.FragmentActivity  registerForActivityResult &androidx.fragment.app.FragmentActivity  saveSettings &androidx.fragment.app.FragmentActivity  
sendImageToAI &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  setSupportActionBar &androidx.fragment.app.FragmentActivity  setupUI &androidx.fragment.app.FragmentActivity  	showError &androidx.fragment.app.FragmentActivity  showProgressBar &androidx.fragment.app.FragmentActivity  	showToast &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  startCamera &androidx.fragment.app.FragmentActivity  
startsWith &androidx.fragment.app.FragmentActivity  testConnection &androidx.fragment.app.FragmentActivity  to &androidx.fragment.app.FragmentActivity  toString &androidx.fragment.app.FragmentActivity  toTypedArray &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  updateStatus &androidx.fragment.app.FragmentActivity  updateUIForProvider &androidx.fragment.app.FragmentActivity  LifecycleCoroutineScope androidx.lifecycle  LifecycleOwner androidx.lifecycle  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  	getLAUNCH *androidx.lifecycle.LifecycleCoroutineScope  	getLaunch *androidx.lifecycle.LifecycleCoroutineScope  launch *androidx.lifecycle.LifecycleCoroutineScope  Board androidx.lifecycle.ViewModel  ChessBoardUtils androidx.lifecycle.ViewModel  ChessGameState androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  Log androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  Square androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  StockfishManager androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  TAG androidx.lifecycle.ViewModel  
_gameState androidx.lifecycle.ViewModel  analyzeCurrentPosition androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  goToMove androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  
lastOrNull androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  loadPosition androidx.lifecycle.ViewModel  makeMove androidx.lifecycle.ViewModel  	onCleared androidx.lifecycle.ViewModel  plus androidx.lifecycle.ViewModel  selectSquare androidx.lifecycle.ViewModel  
startAnalysis androidx.lifecycle.ViewModel  stopAnalysis androidx.lifecycle.ViewModel  take androidx.lifecycle.ViewModel  	uppercase androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  AnalysisScreen #androidx.navigation.NavGraphBuilder  CameraScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  
getCOMPOSABLE #androidx.navigation.NavGraphBuilder  
getComposable #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  PyObject com.chaquo.python  Python com.chaquo.python  asMap com.chaquo.python.PyObject  callAttr com.chaquo.python.PyObject  equals com.chaquo.python.PyObject  toString com.chaquo.python.PyObject  getInstance com.chaquo.python.Python  	getModule com.chaquo.python.Python  	isStarted com.chaquo.python.Python  start com.chaquo.python.Python  AndroidPlatform com.chaquo.python.android  ActivityMainBinding com.example.aicamera  ActivityResultContracts com.example.aicamera  ActivitySettingsBinding com.example.aicamera  
AiResponse com.example.aicamera  	Alignment com.example.aicamera  AnalysisScreen com.example.aicamera  AndroidPlatform com.example.aicamera  Any com.example.aicamera  	ApiConfig com.example.aicamera  ApiProvider com.example.aicamera  ArrayAdapter com.example.aicamera  Boolean com.example.aicamera  Box com.example.aicamera  
CameraManager com.example.aicamera  CameraScreen com.example.aicamera  CameraSelector com.example.aicamera  ChessApp com.example.aicamera  ChessApplication com.example.aicamera  
ChessTheme com.example.aicamera  Column com.example.aicamera  
Composable com.example.aicamera  ComposeMainActivity com.example.aicamera  Context com.example.aicamera  
ContextCompat com.example.aicamera  Dispatchers com.example.aicamera  Double com.example.aicamera  	Exception com.example.aicamera  	Executors com.example.aicamera  ExperimentalMaterial3Api com.example.aicamera  FILENAME_FORMAT com.example.aicamera  File com.example.aicamera  
HomeScreen com.example.aicamera  HuggingFace com.example.aicamera  INSTANCE com.example.aicamera  Icon com.example.aicamera  
IconButton com.example.aicamera  Icons com.example.aicamera  IllegalStateException com.example.aicamera  ImageCapture com.example.aicamera  ImageCaptureException com.example.aicamera  Int com.example.aicamera  Intent com.example.aicamera  KEY_API_PROVIDER com.example.aicamera  KEY_HF_MODEL com.example.aicamera  KEY_HF_TOKEN com.example.aicamera  LocalServer com.example.aicamera  Locale com.example.aicamera  Log com.example.aicamera  Long com.example.aicamera  MainActivity com.example.aicamera  Manifest com.example.aicamera  
MaterialTheme com.example.aicamera  Modifier com.example.aicamera  NetworkManager com.example.aicamera  
NetworkResult com.example.aicamera  OptIn com.example.aicamera  
PREFS_NAME com.example.aicamera  PackageManager com.example.aicamera  Preview com.example.aicamera  ProcessCameraProvider com.example.aicamera  Python com.example.aicamera  PythonGradioService com.example.aicamera  R com.example.aicamera  REQUIRED_PERMISSIONS com.example.aicamera  SettingsActivity com.example.aicamera  SettingsScreen com.example.aicamera  SimpleDateFormat com.example.aicamera  String com.example.aicamera  Surface com.example.aicamera  System com.example.aicamera  TAG com.example.aicamera  Text com.example.aicamera  Toast com.example.aicamera  	TopAppBar com.example.aicamera  Unit com.example.aicamera  View com.example.aicamera  Volatile com.example.aicamera  addListener com.example.aicamera  all com.example.aicamera  also com.example.aicamera  android com.example.aicamera  arrayOf com.example.aicamera  binding com.example.aicamera  
component1 com.example.aicamera  
component2 com.example.aicamera  
composable com.example.aicamera  displayAIResponse com.example.aicamera  fillMaxSize com.example.aicamera  forEach com.example.aicamera  	getOrNull com.example.aicamera  getValue com.example.aicamera  invoke com.example.aicamera  isEmpty com.example.aicamera  
isInitialized com.example.aicamera  
isNullOrEmpty com.example.aicamera  iterator com.example.aicamera  java com.example.aicamera  	javaClass com.example.aicamera  launch com.example.aicamera  let com.example.aicamera  lifecycleScope com.example.aicamera  map com.example.aicamera  
mutableListOf com.example.aicamera  mutableMapOf com.example.aicamera  mutableStateOf com.example.aicamera  networkManager com.example.aicamera  provideDelegate com.example.aicamera  python com.example.aicamera  	readBytes com.example.aicamera  remember com.example.aicamera  run com.example.aicamera  set com.example.aicamera  
setContent com.example.aicamera  setValue com.example.aicamera  	showError com.example.aicamera  showProgressBar com.example.aicamera  	showToast com.example.aicamera  
startsWith com.example.aicamera  synchronized com.example.aicamera  to com.example.aicamera  toString com.example.aicamera  toTypedArray com.example.aicamera  trim com.example.aicamera  updateStatus com.example.aicamera  updateUIForProvider com.example.aicamera  withContext com.example.aicamera  Boolean com.example.aicamera.AiResponse  Double com.example.aicamera.AiResponse  String com.example.aicamera.AiResponse  
confidence com.example.aicamera.AiResponse  error com.example.aicamera.AiResponse  provider com.example.aicamera.AiResponse  result com.example.aicamera.AiResponse  success com.example.aicamera.AiResponse  ApiProvider com.example.aicamera.ApiConfig  Boolean com.example.aicamera.ApiConfig  HuggingFace com.example.aicamera.ApiConfig  LocalServer com.example.aicamera.ApiConfig  String com.example.aicamera.ApiConfig  currentProvider com.example.aicamera.ApiConfig  isConfigurationValid com.example.aicamera.ApiConfig  HUGGING_FACE *com.example.aicamera.ApiConfig.ApiProvider  LOCAL_SERVER *com.example.aicamera.ApiConfig.ApiProvider  equals *com.example.aicamera.ApiConfig.ApiProvider  ordinal *com.example.aicamera.ApiConfig.ApiProvider  values *com.example.aicamera.ApiConfig.ApiProvider  BASE_URL *com.example.aicamera.ApiConfig.HuggingFace  CHESS_BOARD_SEGMENTATION *com.example.aicamera.ApiConfig.HuggingFace  CHESS_FEN_API_SPACE *com.example.aicamera.ApiConfig.HuggingFace  CHESS_PIECE_DETECTION *com.example.aicamera.ApiConfig.HuggingFace  DOCUMENT_OCR_MODEL *com.example.aicamera.ApiConfig.HuggingFace  GENERAL_VIT_MODEL *com.example.aicamera.ApiConfig.HuggingFace  	GIT_MODEL *com.example.aicamera.ApiConfig.HuggingFace  IMAGE_CAPTIONING_MODEL *com.example.aicamera.ApiConfig.HuggingFace  String *com.example.aicamera.ApiConfig.HuggingFace  apiToken *com.example.aicamera.ApiConfig.HuggingFace  currentModel *com.example.aicamera.ApiConfig.HuggingFace  BASE_URL *com.example.aicamera.ApiConfig.LocalServer  ENDPOINT *com.example.aicamera.ApiConfig.LocalServer  CameraSelector "com.example.aicamera.CameraManager  Context "com.example.aicamera.CameraManager  
ContextCompat "com.example.aicamera.CameraManager  	Exception "com.example.aicamera.CameraManager  ExecutorService "com.example.aicamera.CameraManager  	Executors "com.example.aicamera.CameraManager  FILENAME_FORMAT "com.example.aicamera.CameraManager  File "com.example.aicamera.CameraManager  IllegalStateException "com.example.aicamera.CameraManager  ImageCapture "com.example.aicamera.CameraManager  ImageCaptureException "com.example.aicamera.CameraManager  LifecycleOwner "com.example.aicamera.CameraManager  Locale "com.example.aicamera.CameraManager  Log "com.example.aicamera.CameraManager  Preview "com.example.aicamera.CameraManager  PreviewView "com.example.aicamera.CameraManager  ProcessCameraProvider "com.example.aicamera.CameraManager  SimpleDateFormat "com.example.aicamera.CameraManager  String "com.example.aicamera.CameraManager  System "com.example.aicamera.CameraManager  TAG "com.example.aicamera.CameraManager  Unit "com.example.aicamera.CameraManager  addListener "com.example.aicamera.CameraManager  also "com.example.aicamera.CameraManager  bindCameraUseCases "com.example.aicamera.CameraManager  cameraExecutor "com.example.aicamera.CameraManager  cameraProvider "com.example.aicamera.CameraManager  capturePhoto "com.example.aicamera.CameraManager  context "com.example.aicamera.CameraManager  getADDListener "com.example.aicamera.CameraManager  getALSO "com.example.aicamera.CameraManager  getAddListener "com.example.aicamera.CameraManager  getAlso "com.example.aicamera.CameraManager  getRUN "com.example.aicamera.CameraManager  getRun "com.example.aicamera.CameraManager  imageCapture "com.example.aicamera.CameraManager  run "com.example.aicamera.CameraManager  shutdown "com.example.aicamera.CameraManager  startCamera "com.example.aicamera.CameraManager  CameraSelector ,com.example.aicamera.CameraManager.Companion  Context ,com.example.aicamera.CameraManager.Companion  
ContextCompat ,com.example.aicamera.CameraManager.Companion  	Exception ,com.example.aicamera.CameraManager.Companion  ExecutorService ,com.example.aicamera.CameraManager.Companion  	Executors ,com.example.aicamera.CameraManager.Companion  FILENAME_FORMAT ,com.example.aicamera.CameraManager.Companion  File ,com.example.aicamera.CameraManager.Companion  IllegalStateException ,com.example.aicamera.CameraManager.Companion  ImageCapture ,com.example.aicamera.CameraManager.Companion  ImageCaptureException ,com.example.aicamera.CameraManager.Companion  LifecycleOwner ,com.example.aicamera.CameraManager.Companion  Locale ,com.example.aicamera.CameraManager.Companion  Log ,com.example.aicamera.CameraManager.Companion  Preview ,com.example.aicamera.CameraManager.Companion  PreviewView ,com.example.aicamera.CameraManager.Companion  ProcessCameraProvider ,com.example.aicamera.CameraManager.Companion  SimpleDateFormat ,com.example.aicamera.CameraManager.Companion  String ,com.example.aicamera.CameraManager.Companion  System ,com.example.aicamera.CameraManager.Companion  TAG ,com.example.aicamera.CameraManager.Companion  Unit ,com.example.aicamera.CameraManager.Companion  addListener ,com.example.aicamera.CameraManager.Companion  also ,com.example.aicamera.CameraManager.Companion  getADDListener ,com.example.aicamera.CameraManager.Companion  getALSO ,com.example.aicamera.CameraManager.Companion  getAddListener ,com.example.aicamera.CameraManager.Companion  getAlso ,com.example.aicamera.CameraManager.Companion  invoke ,com.example.aicamera.CameraManager.Companion  run ,com.example.aicamera.CameraManager.Companion  ChessApplication %com.example.aicamera.ChessApplication  INSTANCE %com.example.aicamera.ChessApplication  Volatile %com.example.aicamera.ChessApplication  equals %com.example.aicamera.ChessApplication  getInstance %com.example.aicamera.ChessApplication  ChessApplication /com.example.aicamera.ChessApplication.Companion  INSTANCE /com.example.aicamera.ChessApplication.Companion  Volatile /com.example.aicamera.ChessApplication.Companion  getInstance /com.example.aicamera.ChessApplication.Companion  Bundle (com.example.aicamera.ComposeMainActivity  ChessApp (com.example.aicamera.ComposeMainActivity  
ChessTheme (com.example.aicamera.ComposeMainActivity  
MaterialTheme (com.example.aicamera.ComposeMainActivity  Modifier (com.example.aicamera.ComposeMainActivity  SettingsActivity (com.example.aicamera.ComposeMainActivity  Surface (com.example.aicamera.ComposeMainActivity  fillMaxSize (com.example.aicamera.ComposeMainActivity  getFILLMaxSize (com.example.aicamera.ComposeMainActivity  getFillMaxSize (com.example.aicamera.ComposeMainActivity  
getSETContent (com.example.aicamera.ComposeMainActivity  
getSetContent (com.example.aicamera.ComposeMainActivity  
setContent (com.example.aicamera.ComposeMainActivity  ActivityMainBinding !com.example.aicamera.MainActivity  ActivityResultContracts !com.example.aicamera.MainActivity  	ApiConfig !com.example.aicamera.MainActivity  Boolean !com.example.aicamera.MainActivity  Bundle !com.example.aicamera.MainActivity  
CameraManager !com.example.aicamera.MainActivity  	Companion !com.example.aicamera.MainActivity  
ContextCompat !com.example.aicamera.MainActivity  File !com.example.aicamera.MainActivity  Intent !com.example.aicamera.MainActivity  Log !com.example.aicamera.MainActivity  Manifest !com.example.aicamera.MainActivity  Menu !com.example.aicamera.MainActivity  MenuItem !com.example.aicamera.MainActivity  NetworkManager !com.example.aicamera.MainActivity  
NetworkResult !com.example.aicamera.MainActivity  PackageManager !com.example.aicamera.MainActivity  R !com.example.aicamera.MainActivity  REQUIRED_PERMISSIONS !com.example.aicamera.MainActivity  SettingsActivity !com.example.aicamera.MainActivity  String !com.example.aicamera.MainActivity  TAG !com.example.aicamera.MainActivity  Toast !com.example.aicamera.MainActivity  View !com.example.aicamera.MainActivity  all !com.example.aicamera.MainActivity  allPermissionsGranted !com.example.aicamera.MainActivity  baseContext !com.example.aicamera.MainActivity  binding !com.example.aicamera.MainActivity  
cameraManager !com.example.aicamera.MainActivity  capturePhoto !com.example.aicamera.MainActivity  capturedImageFile !com.example.aicamera.MainActivity  displayAIResponse !com.example.aicamera.MainActivity  getALL !com.example.aicamera.MainActivity  getAll !com.example.aicamera.MainActivity  getBASEContext !com.example.aicamera.MainActivity  getBaseContext !com.example.aicamera.MainActivity  	getLAUNCH !com.example.aicamera.MainActivity  getLAYOUTInflater !com.example.aicamera.MainActivity  getLET !com.example.aicamera.MainActivity  getLIFECYCLEScope !com.example.aicamera.MainActivity  	getLaunch !com.example.aicamera.MainActivity  getLayoutInflater !com.example.aicamera.MainActivity  getLet !com.example.aicamera.MainActivity  getLifecycleScope !com.example.aicamera.MainActivity  getMENUInflater !com.example.aicamera.MainActivity  getMenuInflater !com.example.aicamera.MainActivity  	getString !com.example.aicamera.MainActivity  invoke !com.example.aicamera.MainActivity  java !com.example.aicamera.MainActivity  launch !com.example.aicamera.MainActivity  layoutInflater !com.example.aicamera.MainActivity  let !com.example.aicamera.MainActivity  lifecycleScope !com.example.aicamera.MainActivity  menuInflater !com.example.aicamera.MainActivity  
mutableListOf !com.example.aicamera.MainActivity  networkManager !com.example.aicamera.MainActivity  registerForActivityResult !com.example.aicamera.MainActivity  requestPermissionLauncher !com.example.aicamera.MainActivity  
sendImageToAI !com.example.aicamera.MainActivity  setBaseContext !com.example.aicamera.MainActivity  setContentView !com.example.aicamera.MainActivity  setLayoutInflater !com.example.aicamera.MainActivity  setMenuInflater !com.example.aicamera.MainActivity  setupUI !com.example.aicamera.MainActivity  	showError !com.example.aicamera.MainActivity  showProgressBar !com.example.aicamera.MainActivity  	showToast !com.example.aicamera.MainActivity  
startActivity !com.example.aicamera.MainActivity  startCamera !com.example.aicamera.MainActivity  toTypedArray !com.example.aicamera.MainActivity  updateStatus !com.example.aicamera.MainActivity  ActivityMainBinding +com.example.aicamera.MainActivity.Companion  ActivityResultContracts +com.example.aicamera.MainActivity.Companion  	ApiConfig +com.example.aicamera.MainActivity.Companion  Boolean +com.example.aicamera.MainActivity.Companion  Bundle +com.example.aicamera.MainActivity.Companion  
CameraManager +com.example.aicamera.MainActivity.Companion  
ContextCompat +com.example.aicamera.MainActivity.Companion  File +com.example.aicamera.MainActivity.Companion  Intent +com.example.aicamera.MainActivity.Companion  Log +com.example.aicamera.MainActivity.Companion  Manifest +com.example.aicamera.MainActivity.Companion  Menu +com.example.aicamera.MainActivity.Companion  MenuItem +com.example.aicamera.MainActivity.Companion  NetworkManager +com.example.aicamera.MainActivity.Companion  
NetworkResult +com.example.aicamera.MainActivity.Companion  PackageManager +com.example.aicamera.MainActivity.Companion  R +com.example.aicamera.MainActivity.Companion  REQUIRED_PERMISSIONS +com.example.aicamera.MainActivity.Companion  SettingsActivity +com.example.aicamera.MainActivity.Companion  String +com.example.aicamera.MainActivity.Companion  TAG +com.example.aicamera.MainActivity.Companion  Toast +com.example.aicamera.MainActivity.Companion  View +com.example.aicamera.MainActivity.Companion  all +com.example.aicamera.MainActivity.Companion  binding +com.example.aicamera.MainActivity.Companion  displayAIResponse +com.example.aicamera.MainActivity.Companion  getALL +com.example.aicamera.MainActivity.Companion  getAll +com.example.aicamera.MainActivity.Companion  	getLAUNCH +com.example.aicamera.MainActivity.Companion  getLET +com.example.aicamera.MainActivity.Companion  	getLaunch +com.example.aicamera.MainActivity.Companion  getLet +com.example.aicamera.MainActivity.Companion  getMUTABLEListOf +com.example.aicamera.MainActivity.Companion  getMutableListOf +com.example.aicamera.MainActivity.Companion  getTOTypedArray +com.example.aicamera.MainActivity.Companion  getToTypedArray +com.example.aicamera.MainActivity.Companion  invoke +com.example.aicamera.MainActivity.Companion  java +com.example.aicamera.MainActivity.Companion  launch +com.example.aicamera.MainActivity.Companion  let +com.example.aicamera.MainActivity.Companion  lifecycleScope +com.example.aicamera.MainActivity.Companion  
mutableListOf +com.example.aicamera.MainActivity.Companion  networkManager +com.example.aicamera.MainActivity.Companion  	showError +com.example.aicamera.MainActivity.Companion  showProgressBar +com.example.aicamera.MainActivity.Companion  	showToast +com.example.aicamera.MainActivity.Companion  toTypedArray +com.example.aicamera.MainActivity.Companion  updateStatus +com.example.aicamera.MainActivity.Companion  
AiResponse #com.example.aicamera.NetworkManager  	ApiConfig #com.example.aicamera.NetworkManager  ChessApplication #com.example.aicamera.NetworkManager  	Exception #com.example.aicamera.NetworkManager  File #com.example.aicamera.NetworkManager  Log #com.example.aicamera.NetworkManager  
NetworkResult #com.example.aicamera.NetworkManager  PythonGradioService #com.example.aicamera.NetworkManager  TAG #com.example.aicamera.NetworkManager  pythonGradioService #com.example.aicamera.NetworkManager  uploadImage #com.example.aicamera.NetworkManager  uploadToPythonGradio #com.example.aicamera.NetworkManager  
AiResponse -com.example.aicamera.NetworkManager.Companion  	ApiConfig -com.example.aicamera.NetworkManager.Companion  ChessApplication -com.example.aicamera.NetworkManager.Companion  	Exception -com.example.aicamera.NetworkManager.Companion  File -com.example.aicamera.NetworkManager.Companion  Log -com.example.aicamera.NetworkManager.Companion  
NetworkResult -com.example.aicamera.NetworkManager.Companion  PythonGradioService -com.example.aicamera.NetworkManager.Companion  TAG -com.example.aicamera.NetworkManager.Companion  invoke -com.example.aicamera.NetworkManager.Companion  Error "com.example.aicamera.NetworkResult  Loading "com.example.aicamera.NetworkResult  
NetworkResult "com.example.aicamera.NetworkResult  String "com.example.aicamera.NetworkResult  Success "com.example.aicamera.NetworkResult  data "com.example.aicamera.NetworkResult  message "com.example.aicamera.NetworkResult  String (com.example.aicamera.NetworkResult.Error  message (com.example.aicamera.NetworkResult.Error  String *com.example.aicamera.NetworkResult.Loading  message *com.example.aicamera.NetworkResult.Loading  data *com.example.aicamera.NetworkResult.Success  
AiResponse (com.example.aicamera.PythonGradioService  AndroidPlatform (com.example.aicamera.PythonGradioService  Any (com.example.aicamera.PythonGradioService  Boolean (com.example.aicamera.PythonGradioService  	Companion (com.example.aicamera.PythonGradioService  Context (com.example.aicamera.PythonGradioService  Dispatchers (com.example.aicamera.PythonGradioService  	Exception (com.example.aicamera.PythonGradioService  File (com.example.aicamera.PythonGradioService  Log (com.example.aicamera.PythonGradioService  
NetworkResult (com.example.aicamera.PythonGradioService  Python (com.example.aicamera.PythonGradioService  PythonGradioService (com.example.aicamera.PythonGradioService  String (com.example.aicamera.PythonGradioService  TAG (com.example.aicamera.PythonGradioService  Volatile (com.example.aicamera.PythonGradioService  also (com.example.aicamera.PythonGradioService  android (com.example.aicamera.PythonGradioService  
component1 (com.example.aicamera.PythonGradioService  
component2 (com.example.aicamera.PythonGradioService  generateFen (com.example.aicamera.PythonGradioService  getALSO (com.example.aicamera.PythonGradioService  
getANDROID (com.example.aicamera.PythonGradioService  getAlso (com.example.aicamera.PythonGradioService  
getAndroid (com.example.aicamera.PythonGradioService  
getComponent1 (com.example.aicamera.PythonGradioService  
getComponent2 (com.example.aicamera.PythonGradioService  getISNullOrEmpty (com.example.aicamera.PythonGradioService  getITERATOR (com.example.aicamera.PythonGradioService  getInstance (com.example.aicamera.PythonGradioService  getIsNullOrEmpty (com.example.aicamera.PythonGradioService  getIterator (com.example.aicamera.PythonGradioService  getMUTABLEMapOf (com.example.aicamera.PythonGradioService  getMutableMapOf (com.example.aicamera.PythonGradioService  getREADBytes (com.example.aicamera.PythonGradioService  getReadBytes (com.example.aicamera.PythonGradioService  getSET (com.example.aicamera.PythonGradioService  getSet (com.example.aicamera.PythonGradioService  getWITHContext (com.example.aicamera.PythonGradioService  getWithContext (com.example.aicamera.PythonGradioService  
initialize (com.example.aicamera.PythonGradioService  invoke (com.example.aicamera.PythonGradioService  
isInitialized (com.example.aicamera.PythonGradioService  
isNullOrEmpty (com.example.aicamera.PythonGradioService  iterator (com.example.aicamera.PythonGradioService  	javaClass (com.example.aicamera.PythonGradioService  mutableMapOf (com.example.aicamera.PythonGradioService  python (com.example.aicamera.PythonGradioService  	readBytes (com.example.aicamera.PythonGradioService  set (com.example.aicamera.PythonGradioService  synchronized (com.example.aicamera.PythonGradioService  withContext (com.example.aicamera.PythonGradioService  
AiResponse 2com.example.aicamera.PythonGradioService.Companion  AndroidPlatform 2com.example.aicamera.PythonGradioService.Companion  Any 2com.example.aicamera.PythonGradioService.Companion  Boolean 2com.example.aicamera.PythonGradioService.Companion  Context 2com.example.aicamera.PythonGradioService.Companion  Dispatchers 2com.example.aicamera.PythonGradioService.Companion  	Exception 2com.example.aicamera.PythonGradioService.Companion  File 2com.example.aicamera.PythonGradioService.Companion  INSTANCE 2com.example.aicamera.PythonGradioService.Companion  Log 2com.example.aicamera.PythonGradioService.Companion  
NetworkResult 2com.example.aicamera.PythonGradioService.Companion  Python 2com.example.aicamera.PythonGradioService.Companion  PythonGradioService 2com.example.aicamera.PythonGradioService.Companion  String 2com.example.aicamera.PythonGradioService.Companion  TAG 2com.example.aicamera.PythonGradioService.Companion  Volatile 2com.example.aicamera.PythonGradioService.Companion  also 2com.example.aicamera.PythonGradioService.Companion  android 2com.example.aicamera.PythonGradioService.Companion  
component1 2com.example.aicamera.PythonGradioService.Companion  
component2 2com.example.aicamera.PythonGradioService.Companion  getALSO 2com.example.aicamera.PythonGradioService.Companion  
getANDROID 2com.example.aicamera.PythonGradioService.Companion  getAlso 2com.example.aicamera.PythonGradioService.Companion  
getAndroid 2com.example.aicamera.PythonGradioService.Companion  
getComponent1 2com.example.aicamera.PythonGradioService.Companion  
getComponent2 2com.example.aicamera.PythonGradioService.Companion  getISNullOrEmpty 2com.example.aicamera.PythonGradioService.Companion  getITERATOR 2com.example.aicamera.PythonGradioService.Companion  getInstance 2com.example.aicamera.PythonGradioService.Companion  getIsNullOrEmpty 2com.example.aicamera.PythonGradioService.Companion  getIterator 2com.example.aicamera.PythonGradioService.Companion  getMUTABLEMapOf 2com.example.aicamera.PythonGradioService.Companion  getMutableMapOf 2com.example.aicamera.PythonGradioService.Companion  getREADBytes 2com.example.aicamera.PythonGradioService.Companion  getReadBytes 2com.example.aicamera.PythonGradioService.Companion  getSET 2com.example.aicamera.PythonGradioService.Companion  getSYNCHRONIZED 2com.example.aicamera.PythonGradioService.Companion  getSet 2com.example.aicamera.PythonGradioService.Companion  getSynchronized 2com.example.aicamera.PythonGradioService.Companion  getWITHContext 2com.example.aicamera.PythonGradioService.Companion  getWithContext 2com.example.aicamera.PythonGradioService.Companion  invoke 2com.example.aicamera.PythonGradioService.Companion  
isInitialized 2com.example.aicamera.PythonGradioService.Companion  
isNullOrEmpty 2com.example.aicamera.PythonGradioService.Companion  iterator 2com.example.aicamera.PythonGradioService.Companion  	javaClass 2com.example.aicamera.PythonGradioService.Companion  mutableMapOf 2com.example.aicamera.PythonGradioService.Companion  python 2com.example.aicamera.PythonGradioService.Companion  	readBytes 2com.example.aicamera.PythonGradioService.Companion  set 2com.example.aicamera.PythonGradioService.Companion  synchronized 2com.example.aicamera.PythonGradioService.Companion  withContext 2com.example.aicamera.PythonGradioService.Companion  id com.example.aicamera.R  menu com.example.aicamera.R  string com.example.aicamera.R  action_settings com.example.aicamera.R.id  	main_menu com.example.aicamera.R.menu  processing_with_cloud com.example.aicamera.R.string  processing_with_local com.example.aicamera.R.string  ActivitySettingsBinding %com.example.aicamera.SettingsActivity  AdapterView %com.example.aicamera.SettingsActivity  	ApiConfig %com.example.aicamera.SettingsActivity  ArrayAdapter %com.example.aicamera.SettingsActivity  Boolean %com.example.aicamera.SettingsActivity  Bundle %com.example.aicamera.SettingsActivity  	Companion %com.example.aicamera.SettingsActivity  Context %com.example.aicamera.SettingsActivity  Int %com.example.aicamera.SettingsActivity  KEY_API_PROVIDER %com.example.aicamera.SettingsActivity  KEY_HF_MODEL %com.example.aicamera.SettingsActivity  KEY_HF_TOKEN %com.example.aicamera.SettingsActivity  Long %com.example.aicamera.SettingsActivity  
PREFS_NAME %com.example.aicamera.SettingsActivity  SharedPreferences %com.example.aicamera.SettingsActivity  String %com.example.aicamera.SettingsActivity  Toast %com.example.aicamera.SettingsActivity  View %com.example.aicamera.SettingsActivity  android %com.example.aicamera.SettingsActivity  
applySettings %com.example.aicamera.SettingsActivity  arrayOf %com.example.aicamera.SettingsActivity  binding %com.example.aicamera.SettingsActivity  finish %com.example.aicamera.SettingsActivity  
getANDROID %com.example.aicamera.SettingsActivity  
getARRAYOf %com.example.aicamera.SettingsActivity  
getAndroid %com.example.aicamera.SettingsActivity  
getArrayOf %com.example.aicamera.SettingsActivity  getGETOrNull %com.example.aicamera.SettingsActivity  getGetOrNull %com.example.aicamera.SettingsActivity  
getISEmpty %com.example.aicamera.SettingsActivity  
getIsEmpty %com.example.aicamera.SettingsActivity  getLAYOUTInflater %com.example.aicamera.SettingsActivity  getLayoutInflater %com.example.aicamera.SettingsActivity  getMAP %com.example.aicamera.SettingsActivity  getMap %com.example.aicamera.SettingsActivity  	getOrNull %com.example.aicamera.SettingsActivity  
getSTARTSWith %com.example.aicamera.SettingsActivity  getSUPPORTActionBar %com.example.aicamera.SettingsActivity  getSharedPreferences %com.example.aicamera.SettingsActivity  
getStartsWith %com.example.aicamera.SettingsActivity  getSupportActionBar %com.example.aicamera.SettingsActivity  getTO %com.example.aicamera.SettingsActivity  getTOString %com.example.aicamera.SettingsActivity  getTOTypedArray %com.example.aicamera.SettingsActivity  getTRIM %com.example.aicamera.SettingsActivity  getTo %com.example.aicamera.SettingsActivity  getToString %com.example.aicamera.SettingsActivity  getToTypedArray %com.example.aicamera.SettingsActivity  getTrim %com.example.aicamera.SettingsActivity  isEmpty %com.example.aicamera.SettingsActivity  layoutInflater %com.example.aicamera.SettingsActivity  loadSettings %com.example.aicamera.SettingsActivity  loadSettingsOnStartup %com.example.aicamera.SettingsActivity  map %com.example.aicamera.SettingsActivity  prefs %com.example.aicamera.SettingsActivity  saveSettings %com.example.aicamera.SettingsActivity  setContentView %com.example.aicamera.SettingsActivity  setLayoutInflater %com.example.aicamera.SettingsActivity  setSupportActionBar %com.example.aicamera.SettingsActivity  setupUI %com.example.aicamera.SettingsActivity  	showToast %com.example.aicamera.SettingsActivity  
startsWith %com.example.aicamera.SettingsActivity  supportActionBar %com.example.aicamera.SettingsActivity  testConnection %com.example.aicamera.SettingsActivity  to %com.example.aicamera.SettingsActivity  toString %com.example.aicamera.SettingsActivity  toTypedArray %com.example.aicamera.SettingsActivity  trim %com.example.aicamera.SettingsActivity  updateUIForProvider %com.example.aicamera.SettingsActivity  ActivitySettingsBinding /com.example.aicamera.SettingsActivity.Companion  AdapterView /com.example.aicamera.SettingsActivity.Companion  	ApiConfig /com.example.aicamera.SettingsActivity.Companion  ArrayAdapter /com.example.aicamera.SettingsActivity.Companion  Boolean /com.example.aicamera.SettingsActivity.Companion  Bundle /com.example.aicamera.SettingsActivity.Companion  Context /com.example.aicamera.SettingsActivity.Companion  Int /com.example.aicamera.SettingsActivity.Companion  KEY_API_PROVIDER /com.example.aicamera.SettingsActivity.Companion  KEY_HF_MODEL /com.example.aicamera.SettingsActivity.Companion  KEY_HF_TOKEN /com.example.aicamera.SettingsActivity.Companion  Long /com.example.aicamera.SettingsActivity.Companion  
PREFS_NAME /com.example.aicamera.SettingsActivity.Companion  SharedPreferences /com.example.aicamera.SettingsActivity.Companion  String /com.example.aicamera.SettingsActivity.Companion  Toast /com.example.aicamera.SettingsActivity.Companion  View /com.example.aicamera.SettingsActivity.Companion  android /com.example.aicamera.SettingsActivity.Companion  arrayOf /com.example.aicamera.SettingsActivity.Companion  
getANDROID /com.example.aicamera.SettingsActivity.Companion  
getARRAYOf /com.example.aicamera.SettingsActivity.Companion  
getAndroid /com.example.aicamera.SettingsActivity.Companion  
getArrayOf /com.example.aicamera.SettingsActivity.Companion  getGETOrNull /com.example.aicamera.SettingsActivity.Companion  getGetOrNull /com.example.aicamera.SettingsActivity.Companion  
getISEmpty /com.example.aicamera.SettingsActivity.Companion  
getIsEmpty /com.example.aicamera.SettingsActivity.Companion  getMAP /com.example.aicamera.SettingsActivity.Companion  getMap /com.example.aicamera.SettingsActivity.Companion  	getOrNull /com.example.aicamera.SettingsActivity.Companion  
getSTARTSWith /com.example.aicamera.SettingsActivity.Companion  
getStartsWith /com.example.aicamera.SettingsActivity.Companion  getTO /com.example.aicamera.SettingsActivity.Companion  getTOString /com.example.aicamera.SettingsActivity.Companion  getTOTypedArray /com.example.aicamera.SettingsActivity.Companion  getTRIM /com.example.aicamera.SettingsActivity.Companion  getTo /com.example.aicamera.SettingsActivity.Companion  getToString /com.example.aicamera.SettingsActivity.Companion  getToTypedArray /com.example.aicamera.SettingsActivity.Companion  getTrim /com.example.aicamera.SettingsActivity.Companion  isEmpty /com.example.aicamera.SettingsActivity.Companion  loadSettingsOnStartup /com.example.aicamera.SettingsActivity.Companion  map /com.example.aicamera.SettingsActivity.Companion  
startsWith /com.example.aicamera.SettingsActivity.Companion  to /com.example.aicamera.SettingsActivity.Companion  toString /com.example.aicamera.SettingsActivity.Companion  toTypedArray /com.example.aicamera.SettingsActivity.Companion  trim /com.example.aicamera.SettingsActivity.Companion  updateUIForProvider /com.example.aicamera.SettingsActivity.Companion  getUpdateUIForProvider @com.example.aicamera.SettingsActivity.setupUI.<no name provided>  	Alignment com.example.aicamera.chess  AnalysisLine com.example.aicamera.chess  Arrangement com.example.aicamera.chess  Board com.example.aicamera.chess  BoardOrientation com.example.aicamera.chess  Boolean com.example.aicamera.chess  Box com.example.aicamera.chess  BufferedReader com.example.aicamera.chess  Button com.example.aicamera.chess  ButtonDefaults com.example.aicamera.chess  	CameraAlt com.example.aicamera.chess  Card com.example.aicamera.chess  CardDefaults com.example.aicamera.chess  ChessAnalysisActivity com.example.aicamera.chess  ChessAnalysisScreen com.example.aicamera.chess  ChessBoardCard com.example.aicamera.chess  ChessBoardInterface com.example.aicamera.chess  ChessBoardUtils com.example.aicamera.chess  ChessBoardWebView com.example.aicamera.chess  ChessColors com.example.aicamera.chess  ChessGameState com.example.aicamera.chess  
ChessPiece com.example.aicamera.chess  
ChessTheme com.example.aicamera.chess  ChessViewModel com.example.aicamera.chess  CircularProgressIndicator com.example.aicamera.chess  Close com.example.aicamera.chess  Color com.example.aicamera.chess  Column com.example.aicamera.chess  
Composable com.example.aicamera.chess  CoroutineScope com.example.aicamera.chess  Dispatchers com.example.aicamera.chess  EXTRA_SCANNED_FEN com.example.aicamera.chess  Edit com.example.aicamera.chess  EditPositionDialog com.example.aicamera.chess  EngineAnalysis com.example.aicamera.chess  EngineAnalysisCard com.example.aicamera.chess  
EvaluationBar com.example.aicamera.chess  	Exception com.example.aicamera.chess  ExperimentalMaterial3Api com.example.aicamera.chess  
FlipToBack com.example.aicamera.chess  Float com.example.aicamera.chess  
FontFamily com.example.aicamera.chess  
FontWeight com.example.aicamera.chess  GameMode com.example.aicamera.chess  
GameResult com.example.aicamera.chess  GameResultType com.example.aicamera.chess  Icon com.example.aicamera.chess  
IconButton com.example.aicamera.chess  Icons com.example.aicamera.chess  Int com.example.aicamera.chess  Intent com.example.aicamera.chess  LaunchedEffect com.example.aicamera.chess  
LazyColumn com.example.aicamera.chess  LazyRow com.example.aicamera.chess  LearningModule com.example.aicamera.chess  List com.example.aicamera.chess  Log com.example.aicamera.chess  Long com.example.aicamera.chess  MainActivity com.example.aicamera.chess  
MaterialTheme com.example.aicamera.chess  Math com.example.aicamera.chess  Modifier com.example.aicamera.chess  MoreVert com.example.aicamera.chess  MoveHistoryCard com.example.aicamera.chess  MutableStateFlow com.example.aicamera.chess  OptIn com.example.aicamera.chess  OutlinedButton com.example.aicamera.chess  OutlinedTextField com.example.aicamera.chess  
PaddingValues com.example.aicamera.chess  Pause com.example.aicamera.chess  Piece com.example.aicamera.chess  
PieceColor com.example.aicamera.chess  	PieceType com.example.aicamera.chess  	PlayArrow com.example.aicamera.chess  
PlayOption com.example.aicamera.chess  PositionInfo com.example.aicamera.chess  PositionInfoCard com.example.aicamera.chess  PrintWriter com.example.aicamera.chess  Process com.example.aicamera.chess  
Psychology com.example.aicamera.chess  
RecentGame com.example.aicamera.chess  
RestartAlt com.example.aicamera.chess  RoundedCornerShape com.example.aicamera.chess  Row com.example.aicamera.chess  Runtime com.example.aicamera.chess  ScanBoardDialog com.example.aicamera.chess  
ScanResult com.example.aicamera.chess  Spacer com.example.aicamera.chess  Square com.example.aicamera.chess  StockfishManager com.example.aicamera.chess  String com.example.aicamera.chess  
SupervisorJob com.example.aicamera.chess  TAG com.example.aicamera.chess  Text com.example.aicamera.chess  	TextAlign com.example.aicamera.chess  	TopAppBar com.example.aicamera.chess  TopAppBarDefaults com.example.aicamera.chess  Unit com.example.aicamera.chess  Volatile com.example.aicamera.chess  
_gameState com.example.aicamera.chess  also com.example.aicamera.chess  analyzeCurrentPosition com.example.aicamera.chess  androidx com.example.aicamera.chess  apply com.example.aicamera.chess  asStateFlow com.example.aicamera.chess  aspectRatio com.example.aicamera.chess  cancel com.example.aicamera.chess  clip com.example.aicamera.chess  coerceIn com.example.aicamera.chess  collectAsState com.example.aicamera.chess  com com.example.aicamera.chess  createChessWebView com.example.aicamera.chess  delay com.example.aicamera.chess  	emptyList com.example.aicamera.chess  
fillMaxHeight com.example.aicamera.chess  fillMaxSize com.example.aicamera.chess  fillMaxWidth com.example.aicamera.chess  filter com.example.aicamera.chess  generateSimulatedAnalysis com.example.aicamera.chess  generateSimulatedEvaluation com.example.aicamera.chess  generateSimulatedMove com.example.aicamera.chess  getValue com.example.aicamera.chess  height com.example.aicamera.chess  invoke com.example.aicamera.chess  isAnalyzing com.example.aicamera.chess  
isInitialized com.example.aicamera.chess  
isNotEmpty com.example.aicamera.chess  items com.example.aicamera.chess  iterator com.example.aicamera.chess  java com.example.aicamera.chess  joinToString com.example.aicamera.chess  
lastOrNull com.example.aicamera.chess  launch com.example.aicamera.chess  let com.example.aicamera.chess  listOf com.example.aicamera.chess  	lowercase com.example.aicamera.chess  map com.example.aicamera.chess  minOf com.example.aicamera.chess  minusAssign com.example.aicamera.chess  
mutableListOf com.example.aicamera.chess  mutableStateOf com.example.aicamera.chess  padding com.example.aicamera.chess  plus com.example.aicamera.chess  
plusAssign com.example.aicamera.chess  provideDelegate com.example.aicamera.chess  random com.example.aicamera.chess  remember com.example.aicamera.chess  rememberCoroutineScope com.example.aicamera.chess  repeat com.example.aicamera.chess  
setContent com.example.aicamera.chess  setValue com.example.aicamera.chess  size com.example.aicamera.chess  	substring com.example.aicamera.chess  synchronized com.example.aicamera.chess  systemBarsPadding com.example.aicamera.chess  take com.example.aicamera.chess  to com.example.aicamera.chess  updateWebViewPosition com.example.aicamera.chess  	uppercase com.example.aicamera.chess  viewModelScope com.example.aicamera.chess  width com.example.aicamera.chess  withContext com.example.aicamera.chess  Float 'com.example.aicamera.chess.AnalysisLine  Int 'com.example.aicamera.chess.AnalysisLine  List 'com.example.aicamera.chess.AnalysisLine  String 'com.example.aicamera.chess.AnalysisLine  WHITE +com.example.aicamera.chess.BoardOrientation  name +com.example.aicamera.chess.BoardOrientation  Bundle 0com.example.aicamera.chess.ChessAnalysisActivity  ChessAnalysisScreen 0com.example.aicamera.chess.ChessAnalysisActivity  
ChessTheme 0com.example.aicamera.chess.ChessAnalysisActivity  EXTRA_SCANNED_FEN 0com.example.aicamera.chess.ChessAnalysisActivity  Intent 0com.example.aicamera.chess.ChessAnalysisActivity  MainActivity 0com.example.aicamera.chess.ChessAnalysisActivity  	getINTENT 0com.example.aicamera.chess.ChessAnalysisActivity  	getIntent 0com.example.aicamera.chess.ChessAnalysisActivity  
getSETContent 0com.example.aicamera.chess.ChessAnalysisActivity  
getSetContent 0com.example.aicamera.chess.ChessAnalysisActivity  intent 0com.example.aicamera.chess.ChessAnalysisActivity  java 0com.example.aicamera.chess.ChessAnalysisActivity  
setContent 0com.example.aicamera.chess.ChessAnalysisActivity  	setIntent 0com.example.aicamera.chess.ChessAnalysisActivity  
startActivity 0com.example.aicamera.chess.ChessAnalysisActivity  Bundle :com.example.aicamera.chess.ChessAnalysisActivity.Companion  ChessAnalysisScreen :com.example.aicamera.chess.ChessAnalysisActivity.Companion  
ChessTheme :com.example.aicamera.chess.ChessAnalysisActivity.Companion  EXTRA_SCANNED_FEN :com.example.aicamera.chess.ChessAnalysisActivity.Companion  Intent :com.example.aicamera.chess.ChessAnalysisActivity.Companion  MainActivity :com.example.aicamera.chess.ChessAnalysisActivity.Companion  java :com.example.aicamera.chess.ChessAnalysisActivity.Companion  
setContent :com.example.aicamera.chess.ChessAnalysisActivity.Companion  JavascriptInterface .com.example.aicamera.chess.ChessBoardInterface  Log .com.example.aicamera.chess.ChessBoardInterface  String .com.example.aicamera.chess.ChessBoardInterface  Unit .com.example.aicamera.chess.ChessBoardInterface  invoke .com.example.aicamera.chess.ChessBoardInterface  onMoveSelected .com.example.aicamera.chess.ChessBoardInterface  onSquareClicked .com.example.aicamera.chess.ChessBoardInterface  onSquareSelected .com.example.aicamera.chess.ChessBoardInterface  Boolean *com.example.aicamera.chess.ChessBoardUtils  	Exception *com.example.aicamera.chess.ChessBoardUtils  Int *com.example.aicamera.chess.ChessBoardUtils  List *com.example.aicamera.chess.ChessBoardUtils  Log *com.example.aicamera.chess.ChessBoardUtils  
PieceColor *com.example.aicamera.chess.ChessBoardUtils  PositionInfo *com.example.aicamera.chess.ChessBoardUtils  Square *com.example.aicamera.chess.ChessBoardUtils  String *com.example.aicamera.chess.ChessBoardUtils  calculateMaterialBalance *com.example.aicamera.chess.ChessBoardUtils  com *com.example.aicamera.chess.ChessBoardUtils  	emptyList *com.example.aicamera.chess.ChessBoardUtils  filter *com.example.aicamera.chess.ChessBoardUtils  getCOM *com.example.aicamera.chess.ChessBoardUtils  getCom *com.example.aicamera.chess.ChessBoardUtils  getEMPTYList *com.example.aicamera.chess.ChessBoardUtils  getEmptyList *com.example.aicamera.chess.ChessBoardUtils  	getFILTER *com.example.aicamera.chess.ChessBoardUtils  	getFilter *com.example.aicamera.chess.ChessBoardUtils  getLegalMovesFromSquare *com.example.aicamera.chess.ChessBoardUtils  getMINUSAssign *com.example.aicamera.chess.ChessBoardUtils  getMinusAssign *com.example.aicamera.chess.ChessBoardUtils  
getPLUSAssign *com.example.aicamera.chess.ChessBoardUtils  
getPlusAssign *com.example.aicamera.chess.ChessBoardUtils  getSUBSTRING *com.example.aicamera.chess.ChessBoardUtils  getSubstring *com.example.aicamera.chess.ChessBoardUtils  getUPPERCASE *com.example.aicamera.chess.ChessBoardUtils  getUppercase *com.example.aicamera.chess.ChessBoardUtils  isLegalMove *com.example.aicamera.chess.ChessBoardUtils  makeMove *com.example.aicamera.chess.ChessBoardUtils  minusAssign *com.example.aicamera.chess.ChessBoardUtils  	parseMove *com.example.aicamera.chess.ChessBoardUtils  
plusAssign *com.example.aicamera.chess.ChessBoardUtils  	substring *com.example.aicamera.chess.ChessBoardUtils  	uppercase *com.example.aicamera.chess.ChessBoardUtils  Board )com.example.aicamera.chess.ChessGameState  Boolean )com.example.aicamera.chess.ChessGameState  EngineAnalysis )com.example.aicamera.chess.ChessGameState  GameMode )com.example.aicamera.chess.ChessGameState  List )com.example.aicamera.chess.ChessGameState  Move )com.example.aicamera.chess.ChessGameState  Square )com.example.aicamera.chess.ChessGameState  String )com.example.aicamera.chess.ChessGameState  board )com.example.aicamera.chess.ChessGameState  copy )com.example.aicamera.chess.ChessGameState  
currentFen )com.example.aicamera.chess.ChessGameState  	emptyList )com.example.aicamera.chess.ChessGameState  engineAnalysis )com.example.aicamera.chess.ChessGameState  isAnalyzing )com.example.aicamera.chess.ChessGameState  
isEditMode )com.example.aicamera.chess.ChessGameState  lastMove )com.example.aicamera.chess.ChessGameState  moveHistory )com.example.aicamera.chess.ChessGameState  
possibleMoves )com.example.aicamera.chess.ChessGameState  selectedSquare )com.example.aicamera.chess.ChessGameState  
ChessPiece %com.example.aicamera.chess.ChessPiece  Piece %com.example.aicamera.chess.ChessPiece  
PieceColor %com.example.aicamera.chess.ChessPiece  	PieceType %com.example.aicamera.chess.ChessPiece  String %com.example.aicamera.chess.ChessPiece  color %com.example.aicamera.chess.ChessPiece  invoke %com.example.aicamera.chess.ChessPiece  listOf %com.example.aicamera.chess.ChessPiece  to %com.example.aicamera.chess.ChessPiece  type %com.example.aicamera.chess.ChessPiece  
ChessPiece /com.example.aicamera.chess.ChessPiece.Companion  Piece /com.example.aicamera.chess.ChessPiece.Companion  
PieceColor /com.example.aicamera.chess.ChessPiece.Companion  	PieceType /com.example.aicamera.chess.ChessPiece.Companion  String /com.example.aicamera.chess.ChessPiece.Companion  	getLISTOf /com.example.aicamera.chess.ChessPiece.Companion  	getListOf /com.example.aicamera.chess.ChessPiece.Companion  getTO /com.example.aicamera.chess.ChessPiece.Companion  getTo /com.example.aicamera.chess.ChessPiece.Companion  invoke /com.example.aicamera.chess.ChessPiece.Companion  listOf /com.example.aicamera.chess.ChessPiece.Companion  to /com.example.aicamera.chess.ChessPiece.Companion  Board )com.example.aicamera.chess.ChessViewModel  ChessBoardUtils )com.example.aicamera.chess.ChessViewModel  ChessGameState )com.example.aicamera.chess.ChessViewModel  	Exception )com.example.aicamera.chess.ChessViewModel  Int )com.example.aicamera.chess.ChessViewModel  Log )com.example.aicamera.chess.ChessViewModel  MutableStateFlow )com.example.aicamera.chess.ChessViewModel  Square )com.example.aicamera.chess.ChessViewModel  	StateFlow )com.example.aicamera.chess.ChessViewModel  StockfishManager )com.example.aicamera.chess.ChessViewModel  String )com.example.aicamera.chess.ChessViewModel  TAG )com.example.aicamera.chess.ChessViewModel  
_gameState )com.example.aicamera.chess.ChessViewModel  analyzeCurrentPosition )com.example.aicamera.chess.ChessViewModel  asStateFlow )com.example.aicamera.chess.ChessViewModel  com )com.example.aicamera.chess.ChessViewModel  	emptyList )com.example.aicamera.chess.ChessViewModel  	gameState )com.example.aicamera.chess.ChessViewModel  getASStateFlow )com.example.aicamera.chess.ChessViewModel  getAsStateFlow )com.example.aicamera.chess.ChessViewModel  getEMPTYList )com.example.aicamera.chess.ChessViewModel  getEmptyList )com.example.aicamera.chess.ChessViewModel  
getLASTOrNull )com.example.aicamera.chess.ChessViewModel  	getLAUNCH )com.example.aicamera.chess.ChessViewModel  
getLastOrNull )com.example.aicamera.chess.ChessViewModel  	getLaunch )com.example.aicamera.chess.ChessViewModel  getPLUS )com.example.aicamera.chess.ChessViewModel  getPlus )com.example.aicamera.chess.ChessViewModel  getTAKE )com.example.aicamera.chess.ChessViewModel  getTake )com.example.aicamera.chess.ChessViewModel  getUPPERCASE )com.example.aicamera.chess.ChessViewModel  getUppercase )com.example.aicamera.chess.ChessViewModel  getVIEWModelScope )com.example.aicamera.chess.ChessViewModel  getViewModelScope )com.example.aicamera.chess.ChessViewModel  goToMove )com.example.aicamera.chess.ChessViewModel  invoke )com.example.aicamera.chess.ChessViewModel  
lastOrNull )com.example.aicamera.chess.ChessViewModel  launch )com.example.aicamera.chess.ChessViewModel  loadPosition )com.example.aicamera.chess.ChessViewModel  makeMove )com.example.aicamera.chess.ChessViewModel  plus )com.example.aicamera.chess.ChessViewModel  selectSquare )com.example.aicamera.chess.ChessViewModel  
startAnalysis )com.example.aicamera.chess.ChessViewModel  stockfishManager )com.example.aicamera.chess.ChessViewModel  stopAnalysis )com.example.aicamera.chess.ChessViewModel  take )com.example.aicamera.chess.ChessViewModel  	uppercase )com.example.aicamera.chess.ChessViewModel  viewModelScope )com.example.aicamera.chess.ChessViewModel  Board 3com.example.aicamera.chess.ChessViewModel.Companion  ChessBoardUtils 3com.example.aicamera.chess.ChessViewModel.Companion  ChessGameState 3com.example.aicamera.chess.ChessViewModel.Companion  	Exception 3com.example.aicamera.chess.ChessViewModel.Companion  Int 3com.example.aicamera.chess.ChessViewModel.Companion  Log 3com.example.aicamera.chess.ChessViewModel.Companion  MutableStateFlow 3com.example.aicamera.chess.ChessViewModel.Companion  Square 3com.example.aicamera.chess.ChessViewModel.Companion  	StateFlow 3com.example.aicamera.chess.ChessViewModel.Companion  StockfishManager 3com.example.aicamera.chess.ChessViewModel.Companion  String 3com.example.aicamera.chess.ChessViewModel.Companion  TAG 3com.example.aicamera.chess.ChessViewModel.Companion  
_gameState 3com.example.aicamera.chess.ChessViewModel.Companion  analyzeCurrentPosition 3com.example.aicamera.chess.ChessViewModel.Companion  asStateFlow 3com.example.aicamera.chess.ChessViewModel.Companion  com 3com.example.aicamera.chess.ChessViewModel.Companion  	emptyList 3com.example.aicamera.chess.ChessViewModel.Companion  getASStateFlow 3com.example.aicamera.chess.ChessViewModel.Companion  getAsStateFlow 3com.example.aicamera.chess.ChessViewModel.Companion  getEMPTYList 3com.example.aicamera.chess.ChessViewModel.Companion  getEmptyList 3com.example.aicamera.chess.ChessViewModel.Companion  
getLASTOrNull 3com.example.aicamera.chess.ChessViewModel.Companion  	getLAUNCH 3com.example.aicamera.chess.ChessViewModel.Companion  
getLastOrNull 3com.example.aicamera.chess.ChessViewModel.Companion  	getLaunch 3com.example.aicamera.chess.ChessViewModel.Companion  getPLUS 3com.example.aicamera.chess.ChessViewModel.Companion  getPlus 3com.example.aicamera.chess.ChessViewModel.Companion  getTAKE 3com.example.aicamera.chess.ChessViewModel.Companion  getTake 3com.example.aicamera.chess.ChessViewModel.Companion  getUPPERCASE 3com.example.aicamera.chess.ChessViewModel.Companion  getUppercase 3com.example.aicamera.chess.ChessViewModel.Companion  
lastOrNull 3com.example.aicamera.chess.ChessViewModel.Companion  launch 3com.example.aicamera.chess.ChessViewModel.Companion  plus 3com.example.aicamera.chess.ChessViewModel.Companion  take 3com.example.aicamera.chess.ChessViewModel.Companion  	uppercase 3com.example.aicamera.chess.ChessViewModel.Companion  viewModelScope 3com.example.aicamera.chess.ChessViewModel.Companion  AnalysisLine )com.example.aicamera.chess.EngineAnalysis  Float )com.example.aicamera.chess.EngineAnalysis  Int )com.example.aicamera.chess.EngineAnalysis  List )com.example.aicamera.chess.EngineAnalysis  Long )com.example.aicamera.chess.EngineAnalysis  String )com.example.aicamera.chess.EngineAnalysis  bestMove )com.example.aicamera.chess.EngineAnalysis  depth )com.example.aicamera.chess.EngineAnalysis  	emptyList )com.example.aicamera.chess.EngineAnalysis  equals )com.example.aicamera.chess.EngineAnalysis  
evaluation )com.example.aicamera.chess.EngineAnalysis  getLET )com.example.aicamera.chess.EngineAnalysis  getLet )com.example.aicamera.chess.EngineAnalysis  let )com.example.aicamera.chess.EngineAnalysis  principalVariation )com.example.aicamera.chess.EngineAnalysis  ANALYSIS #com.example.aicamera.chess.GameMode  GameResultType %com.example.aicamera.chess.GameResult  
PieceColor %com.example.aicamera.chess.GameResult  String %com.example.aicamera.chess.GameResult  Float )com.example.aicamera.chess.LearningModule  String )com.example.aicamera.chess.LearningModule  BLACK %com.example.aicamera.chess.PieceColor  NONE %com.example.aicamera.chess.PieceColor  WHITE %com.example.aicamera.chess.PieceColor  BISHOP $com.example.aicamera.chess.PieceType  ERASER $com.example.aicamera.chess.PieceType  KING $com.example.aicamera.chess.PieceType  KNIGHT $com.example.aicamera.chess.PieceType  PAWN $com.example.aicamera.chess.PieceType  QUEEN $com.example.aicamera.chess.PieceType  ROOK $com.example.aicamera.chess.PieceType  getTO $com.example.aicamera.chess.PieceType  getTo $com.example.aicamera.chess.PieceType  to $com.example.aicamera.chess.PieceType  GameMode %com.example.aicamera.chess.PlayOption  String %com.example.aicamera.chess.PlayOption  Boolean 'com.example.aicamera.chess.PositionInfo  Int 'com.example.aicamera.chess.PositionInfo  
PieceColor 'com.example.aicamera.chess.PositionInfo  String 'com.example.aicamera.chess.PositionInfo  
GameResult %com.example.aicamera.chess.RecentGame  Long %com.example.aicamera.chess.RecentGame  String %com.example.aicamera.chess.RecentGame  EngineAnalysis %com.example.aicamera.chess.ScanResult  Float %com.example.aicamera.chess.ScanResult  Int %com.example.aicamera.chess.ScanResult  Long %com.example.aicamera.chess.ScanResult  String %com.example.aicamera.chess.ScanResult  Boolean +com.example.aicamera.chess.StockfishManager  BufferedReader +com.example.aicamera.chess.StockfishManager  	Companion +com.example.aicamera.chess.StockfishManager  Context +com.example.aicamera.chess.StockfishManager  CoroutineScope +com.example.aicamera.chess.StockfishManager  Dispatchers +com.example.aicamera.chess.StockfishManager  EngineAnalysis +com.example.aicamera.chess.StockfishManager  	Exception +com.example.aicamera.chess.StockfishManager  Float +com.example.aicamera.chess.StockfishManager  Int +com.example.aicamera.chess.StockfishManager  List +com.example.aicamera.chess.StockfishManager  Log +com.example.aicamera.chess.StockfishManager  Math +com.example.aicamera.chess.StockfishManager  PrintWriter +com.example.aicamera.chess.StockfishManager  Process +com.example.aicamera.chess.StockfishManager  Runtime +com.example.aicamera.chess.StockfishManager  StockfishManager +com.example.aicamera.chess.StockfishManager  String +com.example.aicamera.chess.StockfishManager  
SupervisorJob +com.example.aicamera.chess.StockfishManager  TAG +com.example.aicamera.chess.StockfishManager  Volatile +com.example.aicamera.chess.StockfishManager  also +com.example.aicamera.chess.StockfishManager  
analysisScope +com.example.aicamera.chess.StockfishManager  analyzePosition +com.example.aicamera.chess.StockfishManager  androidx +com.example.aicamera.chess.StockfishManager  cancel +com.example.aicamera.chess.StockfishManager  cleanup +com.example.aicamera.chess.StockfishManager  delay +com.example.aicamera.chess.StockfishManager  	emptyList +com.example.aicamera.chess.StockfishManager  errorReader +com.example.aicamera.chess.StockfishManager  formatEvaluation +com.example.aicamera.chess.StockfishManager  generateSimulatedAnalysis +com.example.aicamera.chess.StockfishManager  generateSimulatedEvaluation +com.example.aicamera.chess.StockfishManager  generateSimulatedMove +com.example.aicamera.chess.StockfishManager  generateSimulatedPV +com.example.aicamera.chess.StockfishManager  getALSO +com.example.aicamera.chess.StockfishManager  getANDROIDX +com.example.aicamera.chess.StockfishManager  getAlso +com.example.aicamera.chess.StockfishManager  getAndroidx +com.example.aicamera.chess.StockfishManager  	getCANCEL +com.example.aicamera.chess.StockfishManager  	getCancel +com.example.aicamera.chess.StockfishManager  getDELAY +com.example.aicamera.chess.StockfishManager  getDelay +com.example.aicamera.chess.StockfishManager  getEMPTYList +com.example.aicamera.chess.StockfishManager  getEmptyList +com.example.aicamera.chess.StockfishManager  getEvaluationColor +com.example.aicamera.chess.StockfishManager  getITERATOR +com.example.aicamera.chess.StockfishManager  getInstance +com.example.aicamera.chess.StockfishManager  getIterator +com.example.aicamera.chess.StockfishManager  	getLISTOf +com.example.aicamera.chess.StockfishManager  	getListOf +com.example.aicamera.chess.StockfishManager  getMINOf +com.example.aicamera.chess.StockfishManager  getMINUSAssign +com.example.aicamera.chess.StockfishManager  getMUTABLEListOf +com.example.aicamera.chess.StockfishManager  getMinOf +com.example.aicamera.chess.StockfishManager  getMinusAssign +com.example.aicamera.chess.StockfishManager  getMutableListOf +com.example.aicamera.chess.StockfishManager  
getPLUSAssign +com.example.aicamera.chess.StockfishManager  
getPlusAssign +com.example.aicamera.chess.StockfishManager  	getRANDOM +com.example.aicamera.chess.StockfishManager  	getREPEAT +com.example.aicamera.chess.StockfishManager  	getRandom +com.example.aicamera.chess.StockfishManager  	getRepeat +com.example.aicamera.chess.StockfishManager  getWITHContext +com.example.aicamera.chess.StockfishManager  getWithContext +com.example.aicamera.chess.StockfishManager  inputWriter +com.example.aicamera.chess.StockfishManager  invoke +com.example.aicamera.chess.StockfishManager  isAnalyzing +com.example.aicamera.chess.StockfishManager  
isInitialized +com.example.aicamera.chess.StockfishManager  iterator +com.example.aicamera.chess.StockfishManager  listOf +com.example.aicamera.chess.StockfishManager  minOf +com.example.aicamera.chess.StockfishManager  minusAssign +com.example.aicamera.chess.StockfishManager  
mutableListOf +com.example.aicamera.chess.StockfishManager  outputReader +com.example.aicamera.chess.StockfishManager  
plusAssign +com.example.aicamera.chess.StockfishManager  random +com.example.aicamera.chess.StockfishManager  repeat +com.example.aicamera.chess.StockfishManager  stockfishProcess +com.example.aicamera.chess.StockfishManager  stopAnalysis +com.example.aicamera.chess.StockfishManager  synchronized +com.example.aicamera.chess.StockfishManager  withContext +com.example.aicamera.chess.StockfishManager  Boolean 5com.example.aicamera.chess.StockfishManager.Companion  BufferedReader 5com.example.aicamera.chess.StockfishManager.Companion  Context 5com.example.aicamera.chess.StockfishManager.Companion  CoroutineScope 5com.example.aicamera.chess.StockfishManager.Companion  Dispatchers 5com.example.aicamera.chess.StockfishManager.Companion  EngineAnalysis 5com.example.aicamera.chess.StockfishManager.Companion  	Exception 5com.example.aicamera.chess.StockfishManager.Companion  Float 5com.example.aicamera.chess.StockfishManager.Companion  INSTANCE 5com.example.aicamera.chess.StockfishManager.Companion  Int 5com.example.aicamera.chess.StockfishManager.Companion  List 5com.example.aicamera.chess.StockfishManager.Companion  Log 5com.example.aicamera.chess.StockfishManager.Companion  Math 5com.example.aicamera.chess.StockfishManager.Companion  PrintWriter 5com.example.aicamera.chess.StockfishManager.Companion  Process 5com.example.aicamera.chess.StockfishManager.Companion  Runtime 5com.example.aicamera.chess.StockfishManager.Companion  StockfishManager 5com.example.aicamera.chess.StockfishManager.Companion  String 5com.example.aicamera.chess.StockfishManager.Companion  
SupervisorJob 5com.example.aicamera.chess.StockfishManager.Companion  TAG 5com.example.aicamera.chess.StockfishManager.Companion  Volatile 5com.example.aicamera.chess.StockfishManager.Companion  also 5com.example.aicamera.chess.StockfishManager.Companion  androidx 5com.example.aicamera.chess.StockfishManager.Companion  cancel 5com.example.aicamera.chess.StockfishManager.Companion  delay 5com.example.aicamera.chess.StockfishManager.Companion  	emptyList 5com.example.aicamera.chess.StockfishManager.Companion  generateSimulatedAnalysis 5com.example.aicamera.chess.StockfishManager.Companion  generateSimulatedEvaluation 5com.example.aicamera.chess.StockfishManager.Companion  generateSimulatedMove 5com.example.aicamera.chess.StockfishManager.Companion  getALSO 5com.example.aicamera.chess.StockfishManager.Companion  getANDROIDX 5com.example.aicamera.chess.StockfishManager.Companion  getAlso 5com.example.aicamera.chess.StockfishManager.Companion  getAndroidx 5com.example.aicamera.chess.StockfishManager.Companion  	getCANCEL 5com.example.aicamera.chess.StockfishManager.Companion  	getCancel 5com.example.aicamera.chess.StockfishManager.Companion  getDELAY 5com.example.aicamera.chess.StockfishManager.Companion  getDelay 5com.example.aicamera.chess.StockfishManager.Companion  getEMPTYList 5com.example.aicamera.chess.StockfishManager.Companion  getEmptyList 5com.example.aicamera.chess.StockfishManager.Companion  getITERATOR 5com.example.aicamera.chess.StockfishManager.Companion  getInstance 5com.example.aicamera.chess.StockfishManager.Companion  getIterator 5com.example.aicamera.chess.StockfishManager.Companion  	getLISTOf 5com.example.aicamera.chess.StockfishManager.Companion  	getListOf 5com.example.aicamera.chess.StockfishManager.Companion  getMINOf 5com.example.aicamera.chess.StockfishManager.Companion  getMINUSAssign 5com.example.aicamera.chess.StockfishManager.Companion  getMUTABLEListOf 5com.example.aicamera.chess.StockfishManager.Companion  getMinOf 5com.example.aicamera.chess.StockfishManager.Companion  getMinusAssign 5com.example.aicamera.chess.StockfishManager.Companion  getMutableListOf 5com.example.aicamera.chess.StockfishManager.Companion  
getPLUSAssign 5com.example.aicamera.chess.StockfishManager.Companion  
getPlusAssign 5com.example.aicamera.chess.StockfishManager.Companion  	getRANDOM 5com.example.aicamera.chess.StockfishManager.Companion  	getREPEAT 5com.example.aicamera.chess.StockfishManager.Companion  	getRandom 5com.example.aicamera.chess.StockfishManager.Companion  	getRepeat 5com.example.aicamera.chess.StockfishManager.Companion  getSYNCHRONIZED 5com.example.aicamera.chess.StockfishManager.Companion  getSynchronized 5com.example.aicamera.chess.StockfishManager.Companion  getWITHContext 5com.example.aicamera.chess.StockfishManager.Companion  getWithContext 5com.example.aicamera.chess.StockfishManager.Companion  invoke 5com.example.aicamera.chess.StockfishManager.Companion  isAnalyzing 5com.example.aicamera.chess.StockfishManager.Companion  
isInitialized 5com.example.aicamera.chess.StockfishManager.Companion  iterator 5com.example.aicamera.chess.StockfishManager.Companion  listOf 5com.example.aicamera.chess.StockfishManager.Companion  minOf 5com.example.aicamera.chess.StockfishManager.Companion  minusAssign 5com.example.aicamera.chess.StockfishManager.Companion  
mutableListOf 5com.example.aicamera.chess.StockfishManager.Companion  
plusAssign 5com.example.aicamera.chess.StockfishManager.Companion  random 5com.example.aicamera.chess.StockfishManager.Companion  repeat 5com.example.aicamera.chess.StockfishManager.Companion  synchronized 5com.example.aicamera.chess.StockfishManager.Companion  withContext 5com.example.aicamera.chess.StockfishManager.Companion  Boolean #com.example.aicamera.chess.ui.theme  ChessAnalysisBlue #com.example.aicamera.chess.ui.theme  ChessAnalysisGreen #com.example.aicamera.chess.ui.theme  ChessAnalysisRed #com.example.aicamera.chess.ui.theme  ChessColors #com.example.aicamera.chess.ui.theme  ChessOnPrimary #com.example.aicamera.chess.ui.theme  ChessOnPrimaryDark #com.example.aicamera.chess.ui.theme  ChessOnSecondary #com.example.aicamera.chess.ui.theme  ChessOnSurface #com.example.aicamera.chess.ui.theme  ChessOnSurfaceDark #com.example.aicamera.chess.ui.theme  ChessOutline #com.example.aicamera.chess.ui.theme  ChessPrimary #com.example.aicamera.chess.ui.theme  ChessPrimaryDark #com.example.aicamera.chess.ui.theme  ChessScanAccent #com.example.aicamera.chess.ui.theme  ChessSecondary #com.example.aicamera.chess.ui.theme  ChessShapes #com.example.aicamera.chess.ui.theme  ChessSurface #com.example.aicamera.chess.ui.theme  ChessSurfaceDark #com.example.aicamera.chess.ui.theme  ChessSurfaceVariant #com.example.aicamera.chess.ui.theme  
ChessTheme #com.example.aicamera.chess.ui.theme  ChessTypography #com.example.aicamera.chess.ui.theme  DarkColorScheme #com.example.aicamera.chess.ui.theme  LightColorScheme #com.example.aicamera.chess.ui.theme  
MaterialTheme #com.example.aicamera.chess.ui.theme  Shapes #com.example.aicamera.chess.ui.theme  
Typography #com.example.aicamera.chess.ui.theme  Unit #com.example.aicamera.chess.ui.theme  darkColorScheme #com.example.aicamera.chess.ui.theme  invoke #com.example.aicamera.chess.ui.theme  lightColorScheme #com.example.aicamera.chess.ui.theme  ChessAnalysisBlue /com.example.aicamera.chess.ui.theme.ChessColors  ChessAnalysisGreen /com.example.aicamera.chess.ui.theme.ChessColors  ChessAnalysisRed /com.example.aicamera.chess.ui.theme.ChessColors  ChessPrimary /com.example.aicamera.chess.ui.theme.ChessColors  ChessScanAccent /com.example.aicamera.chess.ui.theme.ChessColors  ChessSecondary /com.example.aicamera.chess.ui.theme.ChessColors  ChessSurface /com.example.aicamera.chess.ui.theme.ChessColors  ChessSurfaceVariant /com.example.aicamera.chess.ui.theme.ChessColors  SurfaceVariant /com.example.aicamera.chess.ui.theme.ChessColors  ActivityMainBinding  com.example.aicamera.databinding  ActivitySettingsBinding  com.example.aicamera.databinding  aiResponseText 4com.example.aicamera.databinding.ActivityMainBinding  
captureButton 4com.example.aicamera.databinding.ActivityMainBinding  getROOT 4com.example.aicamera.databinding.ActivityMainBinding  getRoot 4com.example.aicamera.databinding.ActivityMainBinding  inflate 4com.example.aicamera.databinding.ActivityMainBinding  previewView 4com.example.aicamera.databinding.ActivityMainBinding  progressBar 4com.example.aicamera.databinding.ActivityMainBinding  root 4com.example.aicamera.databinding.ActivityMainBinding  sendToAiButton 4com.example.aicamera.databinding.ActivityMainBinding  setRoot 4com.example.aicamera.databinding.ActivityMainBinding  
statusText 4com.example.aicamera.databinding.ActivityMainBinding  apiProviderSpinner 8com.example.aicamera.databinding.ActivitySettingsBinding  getROOT 8com.example.aicamera.databinding.ActivitySettingsBinding  getRoot 8com.example.aicamera.databinding.ActivitySettingsBinding  huggingFaceSection 8com.example.aicamera.databinding.ActivitySettingsBinding  inflate 8com.example.aicamera.databinding.ActivitySettingsBinding  localServerSection 8com.example.aicamera.databinding.ActivitySettingsBinding  modelSpinner 8com.example.aicamera.databinding.ActivitySettingsBinding  root 8com.example.aicamera.databinding.ActivitySettingsBinding  
saveButton 8com.example.aicamera.databinding.ActivitySettingsBinding  setRoot 8com.example.aicamera.databinding.ActivitySettingsBinding  testConnectionButton 8com.example.aicamera.databinding.ActivitySettingsBinding  
tokenEditText 8com.example.aicamera.databinding.ActivitySettingsBinding  toolbar 8com.example.aicamera.databinding.ActivitySettingsBinding  ActivityResultContracts com.example.aicamera.ui.screens  AlertDialog com.example.aicamera.ui.screens  	Alignment com.example.aicamera.ui.screens  	Analytics com.example.aicamera.ui.screens  AndroidView com.example.aicamera.ui.screens  Arrangement com.example.aicamera.ui.screens  	ArrowBack com.example.aicamera.ui.screens  Box com.example.aicamera.ui.screens  Button com.example.aicamera.ui.screens  ButtonDefaults com.example.aicamera.ui.screens  Camera com.example.aicamera.ui.screens  	CameraAlt com.example.aicamera.ui.screens  CameraScreen com.example.aicamera.ui.screens  Card com.example.aicamera.ui.screens  CardDefaults com.example.aicamera.ui.screens  CheckCircle com.example.aicamera.ui.screens  CircularProgressIndicator com.example.aicamera.ui.screens  Cloud com.example.aicamera.ui.screens  Color com.example.aicamera.ui.screens  ColorScheme com.example.aicamera.ui.screens  Column com.example.aicamera.ui.screens  
Composable com.example.aicamera.ui.screens  ExperimentalMaterial3Api com.example.aicamera.ui.screens  FeatureItem com.example.aicamera.ui.screens  FlashOff com.example.aicamera.ui.screens  FlashOn com.example.aicamera.ui.screens  
FontWeight com.example.aicamera.ui.screens  Home com.example.aicamera.ui.screens  
HomeScreen com.example.aicamera.ui.screens  Icon com.example.aicamera.ui.screens  
IconButton com.example.aicamera.ui.screens  Icons com.example.aicamera.ui.screens  LaunchedEffect com.example.aicamera.ui.screens  Manifest com.example.aicamera.ui.screens  
MaterialTheme com.example.aicamera.ui.screens  Modifier com.example.aicamera.ui.screens  
NavigationBar com.example.aicamera.ui.screens  NavigationBarItem com.example.aicamera.ui.screens  OptIn com.example.aicamera.ui.screens  OutlinedButton com.example.aicamera.ui.screens  
PaddingValues com.example.aicamera.ui.screens  PhotoLibrary com.example.aicamera.ui.screens  
Psychology com.example.aicamera.ui.screens  RoundedCornerShape com.example.aicamera.ui.screens  Row com.example.aicamera.ui.screens  Scaffold com.example.aicamera.ui.screens  Settings com.example.aicamera.ui.screens  Spacer com.example.aicamera.ui.screens  Speed com.example.aicamera.ui.screens  String com.example.aicamera.ui.screens  Text com.example.aicamera.ui.screens  	TextAlign com.example.aicamera.ui.screens  
TextButton com.example.aicamera.ui.screens  	TopAppBar com.example.aicamera.ui.screens  TopAppBarDefaults com.example.aicamera.ui.screens  Unit com.example.aicamera.ui.screens  androidx com.example.aicamera.ui.screens  aspectRatio com.example.aicamera.ui.screens  
background com.example.aicamera.ui.screens  border com.example.aicamera.ui.screens  clip com.example.aicamera.ui.screens  fillMaxSize com.example.aicamera.ui.screens  fillMaxWidth com.example.aicamera.ui.screens  getValue com.example.aicamera.ui.screens  height com.example.aicamera.ui.screens  launch com.example.aicamera.ui.screens  let com.example.aicamera.ui.screens  mutableStateOf com.example.aicamera.ui.screens  padding com.example.aicamera.ui.screens  provideDelegate com.example.aicamera.ui.screens  remember com.example.aicamera.ui.screens  rememberCoroutineScope com.example.aicamera.ui.screens  setValue com.example.aicamera.ui.screens  size com.example.aicamera.ui.screens  width com.example.aicamera.ui.screens  Boolean com.example.aicamera.ui.theme  Build com.example.aicamera.ui.theme  
ChessBrown com.example.aicamera.ui.theme  ChessBrownDark com.example.aicamera.ui.theme  ChessBrownLight com.example.aicamera.ui.theme  	ChessGold com.example.aicamera.ui.theme  
ChessGoldDark com.example.aicamera.ui.theme  ChessGoldLight com.example.aicamera.ui.theme  
ChessGreen com.example.aicamera.ui.theme  ChessGreenDark com.example.aicamera.ui.theme  ChessGreenLight com.example.aicamera.ui.theme  
ChessTheme com.example.aicamera.ui.theme  DarkColorScheme com.example.aicamera.ui.theme  LightColorScheme com.example.aicamera.ui.theme  
Typography com.example.aicamera.ui.theme  Unit com.example.aicamera.ui.theme  WindowCompat com.example.aicamera.ui.theme  getDynamicColorScheme com.example.aicamera.ui.theme  md_theme_dark_background com.example.aicamera.ui.theme  md_theme_dark_error com.example.aicamera.ui.theme  md_theme_dark_errorContainer com.example.aicamera.ui.theme  md_theme_dark_inverseOnSurface com.example.aicamera.ui.theme  md_theme_dark_inversePrimary com.example.aicamera.ui.theme  md_theme_dark_inverseSurface com.example.aicamera.ui.theme  md_theme_dark_onBackground com.example.aicamera.ui.theme  md_theme_dark_onError com.example.aicamera.ui.theme  md_theme_dark_onErrorContainer com.example.aicamera.ui.theme  md_theme_dark_onPrimary com.example.aicamera.ui.theme   md_theme_dark_onPrimaryContainer com.example.aicamera.ui.theme  md_theme_dark_onSecondary com.example.aicamera.ui.theme  "md_theme_dark_onSecondaryContainer com.example.aicamera.ui.theme  md_theme_dark_onSurface com.example.aicamera.ui.theme  md_theme_dark_onSurfaceVariant com.example.aicamera.ui.theme  md_theme_dark_onTertiary com.example.aicamera.ui.theme  !md_theme_dark_onTertiaryContainer com.example.aicamera.ui.theme  md_theme_dark_outline com.example.aicamera.ui.theme  md_theme_dark_outlineVariant com.example.aicamera.ui.theme  md_theme_dark_primary com.example.aicamera.ui.theme  md_theme_dark_primaryContainer com.example.aicamera.ui.theme  md_theme_dark_scrim com.example.aicamera.ui.theme  md_theme_dark_secondary com.example.aicamera.ui.theme   md_theme_dark_secondaryContainer com.example.aicamera.ui.theme  md_theme_dark_shadow com.example.aicamera.ui.theme  md_theme_dark_surface com.example.aicamera.ui.theme  md_theme_dark_surfaceTint com.example.aicamera.ui.theme  md_theme_dark_surfaceVariant com.example.aicamera.ui.theme  md_theme_dark_tertiary com.example.aicamera.ui.theme  md_theme_dark_tertiaryContainer com.example.aicamera.ui.theme  md_theme_light_background com.example.aicamera.ui.theme  md_theme_light_error com.example.aicamera.ui.theme  md_theme_light_errorContainer com.example.aicamera.ui.theme  md_theme_light_inverseOnSurface com.example.aicamera.ui.theme  md_theme_light_inversePrimary com.example.aicamera.ui.theme  md_theme_light_inverseSurface com.example.aicamera.ui.theme  md_theme_light_onBackground com.example.aicamera.ui.theme  md_theme_light_onError com.example.aicamera.ui.theme  md_theme_light_onErrorContainer com.example.aicamera.ui.theme  md_theme_light_onPrimary com.example.aicamera.ui.theme  !md_theme_light_onPrimaryContainer com.example.aicamera.ui.theme  md_theme_light_onSecondary com.example.aicamera.ui.theme  #md_theme_light_onSecondaryContainer com.example.aicamera.ui.theme  md_theme_light_onSurface com.example.aicamera.ui.theme  md_theme_light_onSurfaceVariant com.example.aicamera.ui.theme  md_theme_light_onTertiary com.example.aicamera.ui.theme  "md_theme_light_onTertiaryContainer com.example.aicamera.ui.theme  md_theme_light_outline com.example.aicamera.ui.theme  md_theme_light_outlineVariant com.example.aicamera.ui.theme  md_theme_light_primary com.example.aicamera.ui.theme  md_theme_light_primaryContainer com.example.aicamera.ui.theme  md_theme_light_scrim com.example.aicamera.ui.theme  md_theme_light_secondary com.example.aicamera.ui.theme  !md_theme_light_secondaryContainer com.example.aicamera.ui.theme  md_theme_light_shadow com.example.aicamera.ui.theme  md_theme_light_surface com.example.aicamera.ui.theme  md_theme_light_surfaceTint com.example.aicamera.ui.theme  md_theme_light_surfaceVariant com.example.aicamera.ui.theme  md_theme_light_tertiary com.example.aicamera.ui.theme   md_theme_light_tertiaryContainer com.example.aicamera.ui.theme  seed com.example.aicamera.ui.theme  Board com.github.bhlangonijr.chesslib  ChessBoardUtils com.github.bhlangonijr.chesslib  ChessGameState com.github.bhlangonijr.chesslib  
ChessPiece com.github.bhlangonijr.chesslib  	Exception com.github.bhlangonijr.chesslib  GameMode com.github.bhlangonijr.chesslib  Log com.github.bhlangonijr.chesslib  MutableStateFlow com.github.bhlangonijr.chesslib  Piece com.github.bhlangonijr.chesslib  
PieceColor com.github.bhlangonijr.chesslib  	PieceType com.github.bhlangonijr.chesslib  Side com.github.bhlangonijr.chesslib  Square com.github.bhlangonijr.chesslib  StockfishManager com.github.bhlangonijr.chesslib  TAG com.github.bhlangonijr.chesslib  
_gameState com.github.bhlangonijr.chesslib  analyzeCurrentPosition com.github.bhlangonijr.chesslib  asStateFlow com.github.bhlangonijr.chesslib  com com.github.bhlangonijr.chesslib  	emptyList com.github.bhlangonijr.chesslib  
lastOrNull com.github.bhlangonijr.chesslib  launch com.github.bhlangonijr.chesslib  listOf com.github.bhlangonijr.chesslib  plus com.github.bhlangonijr.chesslib  take com.github.bhlangonijr.chesslib  to com.github.bhlangonijr.chesslib  	uppercase com.github.bhlangonijr.chesslib  viewModelScope com.github.bhlangonijr.chesslib  castleRight %com.github.bhlangonijr.chesslib.Board  doMove %com.github.bhlangonijr.chesslib.Board  enPassantTarget %com.github.bhlangonijr.chesslib.Board  fen %com.github.bhlangonijr.chesslib.Board  getCASTLERight %com.github.bhlangonijr.chesslib.Board  getCastleRight %com.github.bhlangonijr.chesslib.Board  getENPassantTarget %com.github.bhlangonijr.chesslib.Board  getEnPassantTarget %com.github.bhlangonijr.chesslib.Board  getFEN %com.github.bhlangonijr.chesslib.Board  getFen %com.github.bhlangonijr.chesslib.Board  getHALFMoveCounter %com.github.bhlangonijr.chesslib.Board  getHalfMoveCounter %com.github.bhlangonijr.chesslib.Board  getISKingAttacked %com.github.bhlangonijr.chesslib.Board  
getISMated %com.github.bhlangonijr.chesslib.Board  getISStaleMate %com.github.bhlangonijr.chesslib.Board  getIsKingAttacked %com.github.bhlangonijr.chesslib.Board  
getIsMated %com.github.bhlangonijr.chesslib.Board  getIsStaleMate %com.github.bhlangonijr.chesslib.Board  getPiece %com.github.bhlangonijr.chesslib.Board  
getSIDEToMove %com.github.bhlangonijr.chesslib.Board  
getSideToMove %com.github.bhlangonijr.chesslib.Board  halfMoveCounter %com.github.bhlangonijr.chesslib.Board  isKingAttacked %com.github.bhlangonijr.chesslib.Board  isMated %com.github.bhlangonijr.chesslib.Board  isStaleMate %com.github.bhlangonijr.chesslib.Board  
legalMoves %com.github.bhlangonijr.chesslib.Board  loadFromFen %com.github.bhlangonijr.chesslib.Board  setCastleRight %com.github.bhlangonijr.chesslib.Board  setEnPassantTarget %com.github.bhlangonijr.chesslib.Board  setFen %com.github.bhlangonijr.chesslib.Board  setHalfMoveCounter %com.github.bhlangonijr.chesslib.Board  setKingAttacked %com.github.bhlangonijr.chesslib.Board  setMated %com.github.bhlangonijr.chesslib.Board  
setSideToMove %com.github.bhlangonijr.chesslib.Board  setStaleMate %com.github.bhlangonijr.chesslib.Board  
sideToMove %com.github.bhlangonijr.chesslib.Board  undoMove %com.github.bhlangonijr.chesslib.Board  BLACK_BISHOP %com.github.bhlangonijr.chesslib.Piece  
BLACK_KING %com.github.bhlangonijr.chesslib.Piece  BLACK_KNIGHT %com.github.bhlangonijr.chesslib.Piece  
BLACK_PAWN %com.github.bhlangonijr.chesslib.Piece  BLACK_QUEEN %com.github.bhlangonijr.chesslib.Piece  
BLACK_ROOK %com.github.bhlangonijr.chesslib.Piece  NONE %com.github.bhlangonijr.chesslib.Piece  WHITE_BISHOP %com.github.bhlangonijr.chesslib.Piece  
WHITE_KING %com.github.bhlangonijr.chesslib.Piece  WHITE_KNIGHT %com.github.bhlangonijr.chesslib.Piece  
WHITE_PAWN %com.github.bhlangonijr.chesslib.Piece  WHITE_QUEEN %com.github.bhlangonijr.chesslib.Piece  
WHITE_ROOK %com.github.bhlangonijr.chesslib.Piece  getPIECESide %com.github.bhlangonijr.chesslib.Piece  getPIECEType %com.github.bhlangonijr.chesslib.Piece  getPieceSide %com.github.bhlangonijr.chesslib.Piece  getPieceType %com.github.bhlangonijr.chesslib.Piece  	pieceSide %com.github.bhlangonijr.chesslib.Piece  	pieceType %com.github.bhlangonijr.chesslib.Piece  setPieceSide %com.github.bhlangonijr.chesslib.Piece  setPieceType %com.github.bhlangonijr.chesslib.Piece  BISHOP )com.github.bhlangonijr.chesslib.PieceType  KING )com.github.bhlangonijr.chesslib.PieceType  KNIGHT )com.github.bhlangonijr.chesslib.PieceType  PAWN )com.github.bhlangonijr.chesslib.PieceType  QUEEN )com.github.bhlangonijr.chesslib.PieceType  ROOK )com.github.bhlangonijr.chesslib.PieceType  BLACK $com.github.bhlangonijr.chesslib.Side  WHITE $com.github.bhlangonijr.chesslib.Side  equals $com.github.bhlangonijr.chesslib.Side  equals &com.github.bhlangonijr.chesslib.Square  toString &com.github.bhlangonijr.chesslib.Square  valueOf &com.github.bhlangonijr.chesslib.Square  values &com.github.bhlangonijr.chesslib.Square  Move $com.github.bhlangonijr.chesslib.move  equals )com.github.bhlangonijr.chesslib.move.Move  from )com.github.bhlangonijr.chesslib.move.Move  getFROM )com.github.bhlangonijr.chesslib.move.Move  getFrom )com.github.bhlangonijr.chesslib.move.Move  getLET )com.github.bhlangonijr.chesslib.move.Move  getLet )com.github.bhlangonijr.chesslib.move.Move  getTO )com.github.bhlangonijr.chesslib.move.Move  getTo )com.github.bhlangonijr.chesslib.move.Move  let )com.github.bhlangonijr.chesslib.move.Move  setFrom )com.github.bhlangonijr.chesslib.move.Move  setTo )com.github.bhlangonijr.chesslib.move.Move  to )com.github.bhlangonijr.chesslib.move.Move  
getVISIBILITY 1com.google.android.material.card.MaterialCardView  
getVisibility 1com.google.android.material.card.MaterialCardView  
setVisibility 1com.google.android.material.card.MaterialCardView  
visibility 1com.google.android.material.card.MaterialCardView  getTEXT 7com.google.android.material.textfield.TextInputEditText  getText 7com.google.android.material.textfield.TextInputEditText  setText 7com.google.android.material.textfield.TextInputEditText  text 7com.google.android.material.textfield.TextInputEditText  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  getADDListener 2com.google.common.util.concurrent.ListenableFuture  getAddListener 2com.google.common.util.concurrent.ListenableFuture  BufferedReader java.io  CoroutineScope java.io  Dispatchers java.io  EngineAnalysis java.io  	Exception java.io  File java.io  Log java.io  Math java.io  PrintWriter java.io  Process java.io  Runtime java.io  StockfishManager java.io  
SupervisorJob java.io  TAG java.io  Volatile java.io  also java.io  androidx java.io  cancel java.io  delay java.io  	emptyList java.io  generateSimulatedAnalysis java.io  generateSimulatedEvaluation java.io  generateSimulatedMove java.io  isAnalyzing java.io  
isInitialized java.io  iterator java.io  listOf java.io  minOf java.io  minusAssign java.io  
mutableListOf java.io  
plusAssign java.io  random java.io  repeat java.io  synchronized java.io  withContext java.io  close java.io.BufferedReader  absolutePath java.io.File  exists java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  getLET java.io.File  getLet java.io.File  getNAME java.io.File  getName java.io.File  getREADBytes java.io.File  getReadBytes java.io.File  length java.io.File  let java.io.File  name java.io.File  	readBytes java.io.File  setAbsolutePath java.io.File  setName java.io.File  close java.io.PrintWriter  close java.io.Reader  close java.io.Writer  ActivityMainBinding 	java.lang  ActivityResultContracts 	java.lang  ActivitySettingsBinding 	java.lang  
AiResponse 	java.lang  AlertDialog 	java.lang  	Alignment 	java.lang  AnalysisScreen 	java.lang  AndroidPlatform 	java.lang  AndroidView 	java.lang  	ApiConfig 	java.lang  ApiProvider 	java.lang  Arrangement 	java.lang  ArrayAdapter 	java.lang  Board 	java.lang  BoardOrientation 	java.lang  Box 	java.lang  Build 	java.lang  Button 	java.lang  ButtonDefaults 	java.lang  
CameraManager 	java.lang  CameraScreen 	java.lang  CameraSelector 	java.lang  Card 	java.lang  CardDefaults 	java.lang  ChessAnalysisBlue 	java.lang  ChessAnalysisGreen 	java.lang  ChessAnalysisRed 	java.lang  ChessAnalysisScreen 	java.lang  ChessApp 	java.lang  ChessApplication 	java.lang  ChessBoardCard 	java.lang  ChessBoardInterface 	java.lang  ChessBoardUtils 	java.lang  ChessBoardWebView 	java.lang  ChessColors 	java.lang  ChessGameState 	java.lang  
ChessPiece 	java.lang  ChessPrimary 	java.lang  ChessScanAccent 	java.lang  ChessSecondary 	java.lang  ChessSurface 	java.lang  ChessSurfaceVariant 	java.lang  
ChessTheme 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Color 	java.lang  Column 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  CoroutineScope 	java.lang  Dispatchers 	java.lang  EXTRA_SCANNED_FEN 	java.lang  EngineAnalysis 	java.lang  EngineAnalysisCard 	java.lang  
EvaluationBar 	java.lang  	Exception 	java.lang  	Executors 	java.lang  ExperimentalMaterial3Api 	java.lang  FILENAME_FORMAT 	java.lang  FeatureItem 	java.lang  File 	java.lang  
FontFamily 	java.lang  
FontWeight 	java.lang  GameMode 	java.lang  
HomeScreen 	java.lang  HuggingFace 	java.lang  INSTANCE 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  IllegalStateException 	java.lang  ImageCapture 	java.lang  Intent 	java.lang  KEY_API_PROVIDER 	java.lang  KEY_HF_MODEL 	java.lang  KEY_HF_TOKEN 	java.lang  
LazyColumn 	java.lang  LazyRow 	java.lang  LocalServer 	java.lang  Locale 	java.lang  Log 	java.lang  MainActivity 	java.lang  Manifest 	java.lang  
MaterialTheme 	java.lang  Math 	java.lang  Modifier 	java.lang  MoveHistoryCard 	java.lang  MutableStateFlow 	java.lang  NetworkManager 	java.lang  
NetworkResult 	java.lang  OutlinedButton 	java.lang  OutlinedTextField 	java.lang  
PREFS_NAME 	java.lang  PackageManager 	java.lang  
PaddingValues 	java.lang  Piece 	java.lang  
PieceColor 	java.lang  	PieceType 	java.lang  PositionInfo 	java.lang  PositionInfoCard 	java.lang  Preview 	java.lang  Process 	java.lang  ProcessCameraProvider 	java.lang  Python 	java.lang  PythonGradioService 	java.lang  R 	java.lang  REQUIRED_PERMISSIONS 	java.lang  RoundedCornerShape 	java.lang  Row 	java.lang  Runtime 	java.lang  SettingsActivity 	java.lang  SettingsScreen 	java.lang  SimpleDateFormat 	java.lang  Spacer 	java.lang  Square 	java.lang  StockfishManager 	java.lang  
SupervisorJob 	java.lang  Surface 	java.lang  System 	java.lang  TAG 	java.lang  Text 	java.lang  	TextAlign 	java.lang  
TextButton 	java.lang  Toast 	java.lang  	TopAppBar 	java.lang  TopAppBarDefaults 	java.lang  View 	java.lang  WindowCompat 	java.lang  
_gameState 	java.lang  addListener 	java.lang  all 	java.lang  also 	java.lang  analyzeCurrentPosition 	java.lang  android 	java.lang  androidx 	java.lang  apply 	java.lang  arrayOf 	java.lang  asStateFlow 	java.lang  aspectRatio 	java.lang  
background 	java.lang  binding 	java.lang  border 	java.lang  cancel 	java.lang  clip 	java.lang  coerceIn 	java.lang  com 	java.lang  
component1 	java.lang  
component2 	java.lang  delay 	java.lang  displayAIResponse 	java.lang  	emptyList 	java.lang  
fillMaxHeight 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  filter 	java.lang  forEach 	java.lang  generateSimulatedAnalysis 	java.lang  generateSimulatedEvaluation 	java.lang  generateSimulatedMove 	java.lang  	getOrNull 	java.lang  height 	java.lang  isAnalyzing 	java.lang  isEmpty 	java.lang  
isInitialized 	java.lang  
isNotEmpty 	java.lang  
isNullOrEmpty 	java.lang  iterator 	java.lang  java 	java.lang  	javaClass 	java.lang  joinToString 	java.lang  
lastOrNull 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  	lowercase 	java.lang  map 	java.lang  minOf 	java.lang  minusAssign 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  networkManager 	java.lang  padding 	java.lang  plus 	java.lang  
plusAssign 	java.lang  provideDelegate 	java.lang  python 	java.lang  random 	java.lang  	readBytes 	java.lang  repeat 	java.lang  run 	java.lang  set 	java.lang  	showError 	java.lang  showProgressBar 	java.lang  	showToast 	java.lang  size 	java.lang  
startsWith 	java.lang  	substring 	java.lang  synchronized 	java.lang  take 	java.lang  to 	java.lang  toString 	java.lang  toTypedArray 	java.lang  trim 	java.lang  updateStatus 	java.lang  updateUIForProvider 	java.lang  	uppercase 	java.lang  width 	java.lang  withContext 	java.lang  
getSIMPLEName java.lang.Class  
getSimpleName java.lang.Class  
setSimpleName java.lang.Class  
simpleName java.lang.Class  getJAVAClass java.lang.Exception  getJavaClass java.lang.Exception  	javaClass java.lang.Exception  message java.lang.Exception  printStackTrace java.lang.Exception  random java.lang.Math  destroy java.lang.Process  <SAM-CONSTRUCTOR> java.lang.Runnable  availableProcessors java.lang.Runtime  
getRuntime java.lang.Runtime  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  CameraSelector 	java.util  
ContextCompat 	java.util  	Exception 	java.util  	Executors 	java.util  FILENAME_FORMAT 	java.util  File 	java.util  IllegalStateException 	java.util  ImageCapture 	java.util  ImageCaptureException 	java.util  Locale 	java.util  Log 	java.util  Preview 	java.util  ProcessCameraProvider 	java.util  SimpleDateFormat 	java.util  System 	java.util  TAG 	java.util  addListener 	java.util  also 	java.util  run 	java.util  asMap java.util.AbstractMap  callAttr java.util.AbstractMap  toString java.util.AbstractMap  toString java.util.EnumMap  US java.util.Locale  Executor java.util.concurrent  ExecutorService java.util.concurrent  	Executors java.util.concurrent  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  ActivityMainBinding kotlin  ActivityResultContracts kotlin  ActivitySettingsBinding kotlin  
AiResponse kotlin  AlertDialog kotlin  	Alignment kotlin  AnalysisScreen kotlin  AndroidPlatform kotlin  AndroidView kotlin  Any kotlin  	ApiConfig kotlin  ApiProvider kotlin  Arrangement kotlin  Array kotlin  ArrayAdapter kotlin  Board kotlin  BoardOrientation kotlin  Boolean kotlin  Box kotlin  Build kotlin  Button kotlin  ButtonDefaults kotlin  	ByteArray kotlin  
CameraManager kotlin  CameraScreen kotlin  CameraSelector kotlin  Card kotlin  CardDefaults kotlin  Char kotlin  CharSequence kotlin  ChessAnalysisBlue kotlin  ChessAnalysisGreen kotlin  ChessAnalysisRed kotlin  ChessAnalysisScreen kotlin  ChessApp kotlin  ChessApplication kotlin  ChessBoardCard kotlin  ChessBoardInterface kotlin  ChessBoardUtils kotlin  ChessBoardWebView kotlin  ChessColors kotlin  ChessGameState kotlin  
ChessPiece kotlin  ChessPrimary kotlin  ChessScanAccent kotlin  ChessSecondary kotlin  ChessSurface kotlin  ChessSurfaceVariant kotlin  
ChessTheme kotlin  CircularProgressIndicator kotlin  Color kotlin  Column kotlin  Context kotlin  
ContextCompat kotlin  CoroutineScope kotlin  Dispatchers kotlin  Double kotlin  EXTRA_SCANNED_FEN kotlin  EngineAnalysis kotlin  EngineAnalysisCard kotlin  
EvaluationBar kotlin  	Exception kotlin  	Executors kotlin  ExperimentalMaterial3Api kotlin  FILENAME_FORMAT kotlin  FeatureItem kotlin  File kotlin  Float kotlin  
FontFamily kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  GameMode kotlin  
HomeScreen kotlin  HuggingFace kotlin  INSTANCE kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  IllegalStateException kotlin  ImageCapture kotlin  Int kotlin  Intent kotlin  KEY_API_PROVIDER kotlin  KEY_HF_MODEL kotlin  KEY_HF_TOKEN kotlin  
LazyColumn kotlin  LazyRow kotlin  LocalServer kotlin  Locale kotlin  Log kotlin  Long kotlin  MainActivity kotlin  Manifest kotlin  
MaterialTheme kotlin  Math kotlin  Modifier kotlin  MoveHistoryCard kotlin  MutableStateFlow kotlin  NetworkManager kotlin  
NetworkResult kotlin  Nothing kotlin  OptIn kotlin  OutlinedButton kotlin  OutlinedTextField kotlin  
PREFS_NAME kotlin  PackageManager kotlin  
PaddingValues kotlin  Pair kotlin  Piece kotlin  
PieceColor kotlin  	PieceType kotlin  PositionInfo kotlin  PositionInfoCard kotlin  Preview kotlin  Process kotlin  ProcessCameraProvider kotlin  Python kotlin  PythonGradioService kotlin  R kotlin  REQUIRED_PERMISSIONS kotlin  RoundedCornerShape kotlin  Row kotlin  Runtime kotlin  SettingsActivity kotlin  SettingsScreen kotlin  SimpleDateFormat kotlin  Spacer kotlin  Square kotlin  StockfishManager kotlin  String kotlin  
SupervisorJob kotlin  Surface kotlin  System kotlin  TAG kotlin  Text kotlin  	TextAlign kotlin  
TextButton kotlin  Toast kotlin  	TopAppBar kotlin  TopAppBarDefaults kotlin  Unit kotlin  View kotlin  Volatile kotlin  WindowCompat kotlin  
_gameState kotlin  addListener kotlin  all kotlin  also kotlin  analyzeCurrentPosition kotlin  android kotlin  androidx kotlin  apply kotlin  arrayOf kotlin  asStateFlow kotlin  aspectRatio kotlin  
background kotlin  binding kotlin  border kotlin  cancel kotlin  clip kotlin  coerceIn kotlin  com kotlin  
component1 kotlin  
component2 kotlin  delay kotlin  displayAIResponse kotlin  	emptyList kotlin  
fillMaxHeight kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  filter kotlin  forEach kotlin  generateSimulatedAnalysis kotlin  generateSimulatedEvaluation kotlin  generateSimulatedMove kotlin  	getOrNull kotlin  height kotlin  isAnalyzing kotlin  isEmpty kotlin  
isInitialized kotlin  
isNotEmpty kotlin  
isNullOrEmpty kotlin  iterator kotlin  java kotlin  	javaClass kotlin  joinToString kotlin  
lastOrNull kotlin  launch kotlin  let kotlin  listOf kotlin  	lowercase kotlin  map kotlin  minOf kotlin  minusAssign kotlin  
mutableListOf kotlin  mutableMapOf kotlin  networkManager kotlin  padding kotlin  plus kotlin  
plusAssign kotlin  provideDelegate kotlin  python kotlin  random kotlin  	readBytes kotlin  repeat kotlin  run kotlin  set kotlin  	showError kotlin  showProgressBar kotlin  	showToast kotlin  size kotlin  
startsWith kotlin  	substring kotlin  synchronized kotlin  take kotlin  to kotlin  toString kotlin  toTypedArray kotlin  trim kotlin  updateStatus kotlin  updateUIForProvider kotlin  	uppercase kotlin  width kotlin  withContext kotlin  getALL kotlin.Array  getAll kotlin.Array  getGETOrNull kotlin.Array  getGetOrNull kotlin.Array  getMAP kotlin.Array  getMap kotlin.Array  getLET 
kotlin.Double  getLet 
kotlin.Double  getSP 
kotlin.Double  getSp 
kotlin.Double  getCOERCEIn kotlin.Float  getCoerceIn kotlin.Float  getMINUSAssign kotlin.Float  getMinusAssign kotlin.Float  
getPLUSAssign kotlin.Float  
getPlusAssign kotlin.Float  getDP 
kotlin.Int  getDp 
kotlin.Int  getMINUSAssign 
kotlin.Int  getMinusAssign 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  equals kotlin.Pair  first kotlin.Pair  
getISEmpty 
kotlin.String  getISNullOrEmpty 
kotlin.String  getITERATOR 
kotlin.String  
getIsEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getIterator 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  
getSTARTSWith 
kotlin.String  getSUBSTRING 
kotlin.String  
getStartsWith 
kotlin.String  getSubstring 
kotlin.String  getTO 
kotlin.String  getTRIM 
kotlin.String  getTo 
kotlin.String  getTrim 
kotlin.String  getUPPERCASE 
kotlin.String  getUppercase 
kotlin.String  isEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  ActivityMainBinding kotlin.annotation  ActivityResultContracts kotlin.annotation  ActivitySettingsBinding kotlin.annotation  
AiResponse kotlin.annotation  AlertDialog kotlin.annotation  	Alignment kotlin.annotation  AnalysisScreen kotlin.annotation  AndroidPlatform kotlin.annotation  AndroidView kotlin.annotation  	ApiConfig kotlin.annotation  ApiProvider kotlin.annotation  Arrangement kotlin.annotation  ArrayAdapter kotlin.annotation  Board kotlin.annotation  BoardOrientation kotlin.annotation  Box kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  ButtonDefaults kotlin.annotation  
CameraManager kotlin.annotation  CameraScreen kotlin.annotation  CameraSelector kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  ChessAnalysisBlue kotlin.annotation  ChessAnalysisGreen kotlin.annotation  ChessAnalysisRed kotlin.annotation  ChessAnalysisScreen kotlin.annotation  ChessApp kotlin.annotation  ChessApplication kotlin.annotation  ChessBoardCard kotlin.annotation  ChessBoardInterface kotlin.annotation  ChessBoardUtils kotlin.annotation  ChessBoardWebView kotlin.annotation  ChessColors kotlin.annotation  ChessGameState kotlin.annotation  
ChessPiece kotlin.annotation  ChessPrimary kotlin.annotation  ChessScanAccent kotlin.annotation  ChessSecondary kotlin.annotation  ChessSurface kotlin.annotation  ChessSurfaceVariant kotlin.annotation  
ChessTheme kotlin.annotation  CircularProgressIndicator kotlin.annotation  Color kotlin.annotation  Column kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  CoroutineScope kotlin.annotation  Dispatchers kotlin.annotation  EXTRA_SCANNED_FEN kotlin.annotation  EngineAnalysis kotlin.annotation  EngineAnalysisCard kotlin.annotation  
EvaluationBar kotlin.annotation  	Exception kotlin.annotation  	Executors kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  FILENAME_FORMAT kotlin.annotation  FeatureItem kotlin.annotation  File kotlin.annotation  
FontFamily kotlin.annotation  
FontWeight kotlin.annotation  GameMode kotlin.annotation  
HomeScreen kotlin.annotation  HuggingFace kotlin.annotation  INSTANCE kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  IllegalStateException kotlin.annotation  ImageCapture kotlin.annotation  Intent kotlin.annotation  KEY_API_PROVIDER kotlin.annotation  KEY_HF_MODEL kotlin.annotation  KEY_HF_TOKEN kotlin.annotation  
LazyColumn kotlin.annotation  LazyRow kotlin.annotation  LocalServer kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  MainActivity kotlin.annotation  Manifest kotlin.annotation  
MaterialTheme kotlin.annotation  Math kotlin.annotation  Modifier kotlin.annotation  MoveHistoryCard kotlin.annotation  MutableStateFlow kotlin.annotation  NetworkManager kotlin.annotation  
NetworkResult kotlin.annotation  OutlinedButton kotlin.annotation  OutlinedTextField kotlin.annotation  
PREFS_NAME kotlin.annotation  PackageManager kotlin.annotation  
PaddingValues kotlin.annotation  Piece kotlin.annotation  
PieceColor kotlin.annotation  	PieceType kotlin.annotation  PositionInfo kotlin.annotation  PositionInfoCard kotlin.annotation  Preview kotlin.annotation  Process kotlin.annotation  ProcessCameraProvider kotlin.annotation  Python kotlin.annotation  PythonGradioService kotlin.annotation  R kotlin.annotation  REQUIRED_PERMISSIONS kotlin.annotation  RoundedCornerShape kotlin.annotation  Row kotlin.annotation  Runtime kotlin.annotation  SettingsActivity kotlin.annotation  SettingsScreen kotlin.annotation  SimpleDateFormat kotlin.annotation  Spacer kotlin.annotation  Square kotlin.annotation  StockfishManager kotlin.annotation  
SupervisorJob kotlin.annotation  Surface kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  Text kotlin.annotation  	TextAlign kotlin.annotation  
TextButton kotlin.annotation  Toast kotlin.annotation  	TopAppBar kotlin.annotation  TopAppBarDefaults kotlin.annotation  View kotlin.annotation  Volatile kotlin.annotation  WindowCompat kotlin.annotation  
_gameState kotlin.annotation  addListener kotlin.annotation  all kotlin.annotation  also kotlin.annotation  analyzeCurrentPosition kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  asStateFlow kotlin.annotation  aspectRatio kotlin.annotation  
background kotlin.annotation  binding kotlin.annotation  border kotlin.annotation  cancel kotlin.annotation  clip kotlin.annotation  coerceIn kotlin.annotation  com kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  delay kotlin.annotation  displayAIResponse kotlin.annotation  	emptyList kotlin.annotation  
fillMaxHeight kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  filter kotlin.annotation  forEach kotlin.annotation  generateSimulatedAnalysis kotlin.annotation  generateSimulatedEvaluation kotlin.annotation  generateSimulatedMove kotlin.annotation  	getOrNull kotlin.annotation  height kotlin.annotation  isAnalyzing kotlin.annotation  isEmpty kotlin.annotation  
isInitialized kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrEmpty kotlin.annotation  iterator kotlin.annotation  java kotlin.annotation  	javaClass kotlin.annotation  joinToString kotlin.annotation  
lastOrNull kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  	lowercase kotlin.annotation  map kotlin.annotation  minOf kotlin.annotation  minusAssign kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  networkManager kotlin.annotation  padding kotlin.annotation  plus kotlin.annotation  
plusAssign kotlin.annotation  provideDelegate kotlin.annotation  python kotlin.annotation  random kotlin.annotation  	readBytes kotlin.annotation  repeat kotlin.annotation  run kotlin.annotation  set kotlin.annotation  	showError kotlin.annotation  showProgressBar kotlin.annotation  	showToast kotlin.annotation  size kotlin.annotation  
startsWith kotlin.annotation  	substring kotlin.annotation  synchronized kotlin.annotation  take kotlin.annotation  to kotlin.annotation  toString kotlin.annotation  toTypedArray kotlin.annotation  trim kotlin.annotation  updateStatus kotlin.annotation  updateUIForProvider kotlin.annotation  	uppercase kotlin.annotation  width kotlin.annotation  withContext kotlin.annotation  ActivityMainBinding kotlin.collections  ActivityResultContracts kotlin.collections  ActivitySettingsBinding kotlin.collections  
AiResponse kotlin.collections  AlertDialog kotlin.collections  	Alignment kotlin.collections  AnalysisScreen kotlin.collections  AndroidPlatform kotlin.collections  AndroidView kotlin.collections  	ApiConfig kotlin.collections  ApiProvider kotlin.collections  Arrangement kotlin.collections  ArrayAdapter kotlin.collections  Board kotlin.collections  BoardOrientation kotlin.collections  Box kotlin.collections  Build kotlin.collections  Button kotlin.collections  ButtonDefaults kotlin.collections  
CameraManager kotlin.collections  CameraScreen kotlin.collections  CameraSelector kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  ChessAnalysisBlue kotlin.collections  ChessAnalysisGreen kotlin.collections  ChessAnalysisRed kotlin.collections  ChessAnalysisScreen kotlin.collections  ChessApp kotlin.collections  ChessApplication kotlin.collections  ChessBoardCard kotlin.collections  ChessBoardInterface kotlin.collections  ChessBoardUtils kotlin.collections  ChessBoardWebView kotlin.collections  ChessColors kotlin.collections  ChessGameState kotlin.collections  
ChessPiece kotlin.collections  ChessPrimary kotlin.collections  ChessScanAccent kotlin.collections  ChessSecondary kotlin.collections  ChessSurface kotlin.collections  ChessSurfaceVariant kotlin.collections  
ChessTheme kotlin.collections  CircularProgressIndicator kotlin.collections  Color kotlin.collections  Column kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  CoroutineScope kotlin.collections  Dispatchers kotlin.collections  EXTRA_SCANNED_FEN kotlin.collections  EngineAnalysis kotlin.collections  EngineAnalysisCard kotlin.collections  
EvaluationBar kotlin.collections  	Exception kotlin.collections  	Executors kotlin.collections  ExperimentalMaterial3Api kotlin.collections  FILENAME_FORMAT kotlin.collections  FeatureItem kotlin.collections  File kotlin.collections  
FontFamily kotlin.collections  
FontWeight kotlin.collections  GameMode kotlin.collections  
HomeScreen kotlin.collections  HuggingFace kotlin.collections  INSTANCE kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  IllegalStateException kotlin.collections  ImageCapture kotlin.collections  Intent kotlin.collections  KEY_API_PROVIDER kotlin.collections  KEY_HF_MODEL kotlin.collections  KEY_HF_TOKEN kotlin.collections  
LazyColumn kotlin.collections  LazyRow kotlin.collections  List kotlin.collections  LocalServer kotlin.collections  Locale kotlin.collections  Log kotlin.collections  MainActivity kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  
MaterialTheme kotlin.collections  Math kotlin.collections  Modifier kotlin.collections  MoveHistoryCard kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  MutableStateFlow kotlin.collections  NetworkManager kotlin.collections  
NetworkResult kotlin.collections  OutlinedButton kotlin.collections  OutlinedTextField kotlin.collections  
PREFS_NAME kotlin.collections  PackageManager kotlin.collections  
PaddingValues kotlin.collections  Piece kotlin.collections  
PieceColor kotlin.collections  	PieceType kotlin.collections  PositionInfo kotlin.collections  PositionInfoCard kotlin.collections  Preview kotlin.collections  Process kotlin.collections  ProcessCameraProvider kotlin.collections  Python kotlin.collections  PythonGradioService kotlin.collections  R kotlin.collections  REQUIRED_PERMISSIONS kotlin.collections  RoundedCornerShape kotlin.collections  Row kotlin.collections  Runtime kotlin.collections  SettingsActivity kotlin.collections  SettingsScreen kotlin.collections  SimpleDateFormat kotlin.collections  Spacer kotlin.collections  Square kotlin.collections  StockfishManager kotlin.collections  
SupervisorJob kotlin.collections  Surface kotlin.collections  System kotlin.collections  TAG kotlin.collections  Text kotlin.collections  	TextAlign kotlin.collections  
TextButton kotlin.collections  Toast kotlin.collections  	TopAppBar kotlin.collections  TopAppBarDefaults kotlin.collections  View kotlin.collections  Volatile kotlin.collections  WindowCompat kotlin.collections  
_gameState kotlin.collections  addListener kotlin.collections  all kotlin.collections  also kotlin.collections  analyzeCurrentPosition kotlin.collections  android kotlin.collections  androidx kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  asStateFlow kotlin.collections  aspectRatio kotlin.collections  
background kotlin.collections  binding kotlin.collections  border kotlin.collections  cancel kotlin.collections  clip kotlin.collections  coerceIn kotlin.collections  com kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  delay kotlin.collections  displayAIResponse kotlin.collections  	emptyList kotlin.collections  
fillMaxHeight kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  filter kotlin.collections  forEach kotlin.collections  generateSimulatedAnalysis kotlin.collections  generateSimulatedEvaluation kotlin.collections  generateSimulatedMove kotlin.collections  	getOrNull kotlin.collections  height kotlin.collections  isAnalyzing kotlin.collections  isEmpty kotlin.collections  
isInitialized kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  iterator kotlin.collections  java kotlin.collections  	javaClass kotlin.collections  joinToString kotlin.collections  
lastOrNull kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  	lowercase kotlin.collections  map kotlin.collections  minOf kotlin.collections  minusAssign kotlin.collections  mutableIterator kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  networkManager kotlin.collections  padding kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  provideDelegate kotlin.collections  python kotlin.collections  random kotlin.collections  	readBytes kotlin.collections  repeat kotlin.collections  run kotlin.collections  set kotlin.collections  	showError kotlin.collections  showProgressBar kotlin.collections  	showToast kotlin.collections  size kotlin.collections  
startsWith kotlin.collections  	substring kotlin.collections  synchronized kotlin.collections  take kotlin.collections  to kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  trim kotlin.collections  updateStatus kotlin.collections  updateUIForProvider kotlin.collections  	uppercase kotlin.collections  width kotlin.collections  withContext kotlin.collections  hasNext kotlin.collections.CharIterator  next kotlin.collections.CharIterator  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getJOINToString kotlin.collections.List  getJoinToString kotlin.collections.List  
getLASTOrNull kotlin.collections.List  
getLastOrNull kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getPLUS kotlin.collections.List  getPlus kotlin.collections.List  	getRANDOM kotlin.collections.List  	getRandom kotlin.collections.List  getTAKE kotlin.collections.List  getTOTypedArray kotlin.collections.List  getTake kotlin.collections.List  getToTypedArray kotlin.collections.List  
isNotEmpty kotlin.collections.List  Entry kotlin.collections.Map  	getFILTER kotlin.collections.MutableList  	getFilter kotlin.collections.MutableList  getTOTypedArray kotlin.collections.MutableList  getToTypedArray kotlin.collections.MutableList  getITERATOR kotlin.collections.MutableMap  getIterator kotlin.collections.MutableMap  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  
getComponent1 *kotlin.collections.MutableMap.MutableEntry  
getComponent2 *kotlin.collections.MutableMap.MutableEntry  ActivityMainBinding kotlin.comparisons  ActivityResultContracts kotlin.comparisons  ActivitySettingsBinding kotlin.comparisons  
AiResponse kotlin.comparisons  AlertDialog kotlin.comparisons  	Alignment kotlin.comparisons  AnalysisScreen kotlin.comparisons  AndroidPlatform kotlin.comparisons  AndroidView kotlin.comparisons  	ApiConfig kotlin.comparisons  ApiProvider kotlin.comparisons  Arrangement kotlin.comparisons  ArrayAdapter kotlin.comparisons  Board kotlin.comparisons  BoardOrientation kotlin.comparisons  Box kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  ButtonDefaults kotlin.comparisons  
CameraManager kotlin.comparisons  CameraScreen kotlin.comparisons  CameraSelector kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  ChessAnalysisBlue kotlin.comparisons  ChessAnalysisGreen kotlin.comparisons  ChessAnalysisRed kotlin.comparisons  ChessAnalysisScreen kotlin.comparisons  ChessApp kotlin.comparisons  ChessApplication kotlin.comparisons  ChessBoardCard kotlin.comparisons  ChessBoardInterface kotlin.comparisons  ChessBoardUtils kotlin.comparisons  ChessBoardWebView kotlin.comparisons  ChessColors kotlin.comparisons  ChessGameState kotlin.comparisons  
ChessPiece kotlin.comparisons  ChessPrimary kotlin.comparisons  ChessScanAccent kotlin.comparisons  ChessSecondary kotlin.comparisons  ChessSurface kotlin.comparisons  ChessSurfaceVariant kotlin.comparisons  
ChessTheme kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Color kotlin.comparisons  Column kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  CoroutineScope kotlin.comparisons  Dispatchers kotlin.comparisons  EXTRA_SCANNED_FEN kotlin.comparisons  EngineAnalysis kotlin.comparisons  EngineAnalysisCard kotlin.comparisons  
EvaluationBar kotlin.comparisons  	Exception kotlin.comparisons  	Executors kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  FILENAME_FORMAT kotlin.comparisons  FeatureItem kotlin.comparisons  File kotlin.comparisons  
FontFamily kotlin.comparisons  
FontWeight kotlin.comparisons  GameMode kotlin.comparisons  
HomeScreen kotlin.comparisons  HuggingFace kotlin.comparisons  INSTANCE kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  IllegalStateException kotlin.comparisons  ImageCapture kotlin.comparisons  Intent kotlin.comparisons  KEY_API_PROVIDER kotlin.comparisons  KEY_HF_MODEL kotlin.comparisons  KEY_HF_TOKEN kotlin.comparisons  
LazyColumn kotlin.comparisons  LazyRow kotlin.comparisons  LocalServer kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  MainActivity kotlin.comparisons  Manifest kotlin.comparisons  
MaterialTheme kotlin.comparisons  Math kotlin.comparisons  Modifier kotlin.comparisons  MoveHistoryCard kotlin.comparisons  MutableStateFlow kotlin.comparisons  NetworkManager kotlin.comparisons  
NetworkResult kotlin.comparisons  OutlinedButton kotlin.comparisons  OutlinedTextField kotlin.comparisons  
PREFS_NAME kotlin.comparisons  PackageManager kotlin.comparisons  
PaddingValues kotlin.comparisons  Piece kotlin.comparisons  
PieceColor kotlin.comparisons  	PieceType kotlin.comparisons  PositionInfo kotlin.comparisons  PositionInfoCard kotlin.comparisons  Preview kotlin.comparisons  Process kotlin.comparisons  ProcessCameraProvider kotlin.comparisons  Python kotlin.comparisons  PythonGradioService kotlin.comparisons  R kotlin.comparisons  REQUIRED_PERMISSIONS kotlin.comparisons  RoundedCornerShape kotlin.comparisons  Row kotlin.comparisons  Runtime kotlin.comparisons  SettingsActivity kotlin.comparisons  SettingsScreen kotlin.comparisons  SimpleDateFormat kotlin.comparisons  Spacer kotlin.comparisons  Square kotlin.comparisons  StockfishManager kotlin.comparisons  
SupervisorJob kotlin.comparisons  Surface kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  Text kotlin.comparisons  	TextAlign kotlin.comparisons  
TextButton kotlin.comparisons  Toast kotlin.comparisons  	TopAppBar kotlin.comparisons  TopAppBarDefaults kotlin.comparisons  View kotlin.comparisons  Volatile kotlin.comparisons  WindowCompat kotlin.comparisons  
_gameState kotlin.comparisons  addListener kotlin.comparisons  all kotlin.comparisons  also kotlin.comparisons  analyzeCurrentPosition kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  asStateFlow kotlin.comparisons  aspectRatio kotlin.comparisons  
background kotlin.comparisons  binding kotlin.comparisons  border kotlin.comparisons  cancel kotlin.comparisons  clip kotlin.comparisons  coerceIn kotlin.comparisons  com kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  delay kotlin.comparisons  displayAIResponse kotlin.comparisons  	emptyList kotlin.comparisons  
fillMaxHeight kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  filter kotlin.comparisons  forEach kotlin.comparisons  generateSimulatedAnalysis kotlin.comparisons  generateSimulatedEvaluation kotlin.comparisons  generateSimulatedMove kotlin.comparisons  	getOrNull kotlin.comparisons  height kotlin.comparisons  isAnalyzing kotlin.comparisons  isEmpty kotlin.comparisons  
isInitialized kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  iterator kotlin.comparisons  java kotlin.comparisons  	javaClass kotlin.comparisons  joinToString kotlin.comparisons  
lastOrNull kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  	lowercase kotlin.comparisons  map kotlin.comparisons  minOf kotlin.comparisons  minusAssign kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  networkManager kotlin.comparisons  padding kotlin.comparisons  plus kotlin.comparisons  
plusAssign kotlin.comparisons  provideDelegate kotlin.comparisons  python kotlin.comparisons  random kotlin.comparisons  	readBytes kotlin.comparisons  repeat kotlin.comparisons  run kotlin.comparisons  set kotlin.comparisons  	showError kotlin.comparisons  showProgressBar kotlin.comparisons  	showToast kotlin.comparisons  size kotlin.comparisons  
startsWith kotlin.comparisons  	substring kotlin.comparisons  synchronized kotlin.comparisons  take kotlin.comparisons  to kotlin.comparisons  toString kotlin.comparisons  toTypedArray kotlin.comparisons  trim kotlin.comparisons  updateStatus kotlin.comparisons  updateUIForProvider kotlin.comparisons  	uppercase kotlin.comparisons  width kotlin.comparisons  withContext kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus 1kotlin.coroutines.AbstractCoroutineContextElement  ActivityMainBinding 	kotlin.io  ActivityResultContracts 	kotlin.io  ActivitySettingsBinding 	kotlin.io  
AiResponse 	kotlin.io  AlertDialog 	kotlin.io  	Alignment 	kotlin.io  AnalysisScreen 	kotlin.io  AndroidPlatform 	kotlin.io  AndroidView 	kotlin.io  	ApiConfig 	kotlin.io  ApiProvider 	kotlin.io  Arrangement 	kotlin.io  ArrayAdapter 	kotlin.io  Board 	kotlin.io  BoardOrientation 	kotlin.io  Box 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  ButtonDefaults 	kotlin.io  
CameraManager 	kotlin.io  CameraScreen 	kotlin.io  CameraSelector 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  ChessAnalysisBlue 	kotlin.io  ChessAnalysisGreen 	kotlin.io  ChessAnalysisRed 	kotlin.io  ChessAnalysisScreen 	kotlin.io  ChessApp 	kotlin.io  ChessApplication 	kotlin.io  ChessBoardCard 	kotlin.io  ChessBoardInterface 	kotlin.io  ChessBoardUtils 	kotlin.io  ChessBoardWebView 	kotlin.io  ChessColors 	kotlin.io  ChessGameState 	kotlin.io  
ChessPiece 	kotlin.io  ChessPrimary 	kotlin.io  ChessScanAccent 	kotlin.io  ChessSecondary 	kotlin.io  ChessSurface 	kotlin.io  ChessSurfaceVariant 	kotlin.io  
ChessTheme 	kotlin.io  CircularProgressIndicator 	kotlin.io  Color 	kotlin.io  Column 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  CoroutineScope 	kotlin.io  Dispatchers 	kotlin.io  EXTRA_SCANNED_FEN 	kotlin.io  EngineAnalysis 	kotlin.io  EngineAnalysisCard 	kotlin.io  
EvaluationBar 	kotlin.io  	Exception 	kotlin.io  	Executors 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  FILENAME_FORMAT 	kotlin.io  FeatureItem 	kotlin.io  File 	kotlin.io  
FontFamily 	kotlin.io  
FontWeight 	kotlin.io  GameMode 	kotlin.io  
HomeScreen 	kotlin.io  HuggingFace 	kotlin.io  INSTANCE 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  IllegalStateException 	kotlin.io  ImageCapture 	kotlin.io  Intent 	kotlin.io  KEY_API_PROVIDER 	kotlin.io  KEY_HF_MODEL 	kotlin.io  KEY_HF_TOKEN 	kotlin.io  
LazyColumn 	kotlin.io  LazyRow 	kotlin.io  LocalServer 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  MainActivity 	kotlin.io  Manifest 	kotlin.io  
MaterialTheme 	kotlin.io  Math 	kotlin.io  Modifier 	kotlin.io  MoveHistoryCard 	kotlin.io  MutableStateFlow 	kotlin.io  NetworkManager 	kotlin.io  
NetworkResult 	kotlin.io  OutlinedButton 	kotlin.io  OutlinedTextField 	kotlin.io  
PREFS_NAME 	kotlin.io  PackageManager 	kotlin.io  
PaddingValues 	kotlin.io  Piece 	kotlin.io  
PieceColor 	kotlin.io  	PieceType 	kotlin.io  PositionInfo 	kotlin.io  PositionInfoCard 	kotlin.io  Preview 	kotlin.io  Process 	kotlin.io  ProcessCameraProvider 	kotlin.io  Python 	kotlin.io  PythonGradioService 	kotlin.io  R 	kotlin.io  REQUIRED_PERMISSIONS 	kotlin.io  RoundedCornerShape 	kotlin.io  Row 	kotlin.io  Runtime 	kotlin.io  SettingsActivity 	kotlin.io  SettingsScreen 	kotlin.io  SimpleDateFormat 	kotlin.io  Spacer 	kotlin.io  Square 	kotlin.io  StockfishManager 	kotlin.io  
SupervisorJob 	kotlin.io  Surface 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  Text 	kotlin.io  	TextAlign 	kotlin.io  
TextButton 	kotlin.io  Toast 	kotlin.io  	TopAppBar 	kotlin.io  TopAppBarDefaults 	kotlin.io  View 	kotlin.io  Volatile 	kotlin.io  WindowCompat 	kotlin.io  
_gameState 	kotlin.io  addListener 	kotlin.io  all 	kotlin.io  also 	kotlin.io  analyzeCurrentPosition 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  asStateFlow 	kotlin.io  aspectRatio 	kotlin.io  
background 	kotlin.io  binding 	kotlin.io  border 	kotlin.io  cancel 	kotlin.io  clip 	kotlin.io  coerceIn 	kotlin.io  com 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  delay 	kotlin.io  displayAIResponse 	kotlin.io  	emptyList 	kotlin.io  
fillMaxHeight 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  filter 	kotlin.io  forEach 	kotlin.io  generateSimulatedAnalysis 	kotlin.io  generateSimulatedEvaluation 	kotlin.io  generateSimulatedMove 	kotlin.io  	getOrNull 	kotlin.io  height 	kotlin.io  isAnalyzing 	kotlin.io  isEmpty 	kotlin.io  
isInitialized 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrEmpty 	kotlin.io  iterator 	kotlin.io  java 	kotlin.io  	javaClass 	kotlin.io  joinToString 	kotlin.io  
lastOrNull 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  	lowercase 	kotlin.io  map 	kotlin.io  minOf 	kotlin.io  minusAssign 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  networkManager 	kotlin.io  padding 	kotlin.io  plus 	kotlin.io  
plusAssign 	kotlin.io  provideDelegate 	kotlin.io  python 	kotlin.io  random 	kotlin.io  	readBytes 	kotlin.io  repeat 	kotlin.io  run 	kotlin.io  set 	kotlin.io  	showError 	kotlin.io  showProgressBar 	kotlin.io  	showToast 	kotlin.io  size 	kotlin.io  
startsWith 	kotlin.io  	substring 	kotlin.io  synchronized 	kotlin.io  take 	kotlin.io  to 	kotlin.io  toString 	kotlin.io  toTypedArray 	kotlin.io  trim 	kotlin.io  updateStatus 	kotlin.io  updateUIForProvider 	kotlin.io  	uppercase 	kotlin.io  width 	kotlin.io  withContext 	kotlin.io  ActivityMainBinding 
kotlin.jvm  ActivityResultContracts 
kotlin.jvm  ActivitySettingsBinding 
kotlin.jvm  
AiResponse 
kotlin.jvm  AlertDialog 
kotlin.jvm  	Alignment 
kotlin.jvm  AnalysisScreen 
kotlin.jvm  AndroidPlatform 
kotlin.jvm  AndroidView 
kotlin.jvm  	ApiConfig 
kotlin.jvm  ApiProvider 
kotlin.jvm  Arrangement 
kotlin.jvm  ArrayAdapter 
kotlin.jvm  Board 
kotlin.jvm  BoardOrientation 
kotlin.jvm  Box 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  ButtonDefaults 
kotlin.jvm  
CameraManager 
kotlin.jvm  CameraScreen 
kotlin.jvm  CameraSelector 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  ChessAnalysisBlue 
kotlin.jvm  ChessAnalysisGreen 
kotlin.jvm  ChessAnalysisRed 
kotlin.jvm  ChessAnalysisScreen 
kotlin.jvm  ChessApp 
kotlin.jvm  ChessApplication 
kotlin.jvm  ChessBoardCard 
kotlin.jvm  ChessBoardInterface 
kotlin.jvm  ChessBoardUtils 
kotlin.jvm  ChessBoardWebView 
kotlin.jvm  ChessColors 
kotlin.jvm  ChessGameState 
kotlin.jvm  
ChessPiece 
kotlin.jvm  ChessPrimary 
kotlin.jvm  ChessScanAccent 
kotlin.jvm  ChessSecondary 
kotlin.jvm  ChessSurface 
kotlin.jvm  ChessSurfaceVariant 
kotlin.jvm  
ChessTheme 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Color 
kotlin.jvm  Column 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  CoroutineScope 
kotlin.jvm  Dispatchers 
kotlin.jvm  EXTRA_SCANNED_FEN 
kotlin.jvm  EngineAnalysis 
kotlin.jvm  EngineAnalysisCard 
kotlin.jvm  
EvaluationBar 
kotlin.jvm  	Exception 
kotlin.jvm  	Executors 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  FILENAME_FORMAT 
kotlin.jvm  FeatureItem 
kotlin.jvm  File 
kotlin.jvm  
FontFamily 
kotlin.jvm  
FontWeight 
kotlin.jvm  GameMode 
kotlin.jvm  
HomeScreen 
kotlin.jvm  HuggingFace 
kotlin.jvm  INSTANCE 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  IllegalStateException 
kotlin.jvm  ImageCapture 
kotlin.jvm  Intent 
kotlin.jvm  KEY_API_PROVIDER 
kotlin.jvm  KEY_HF_MODEL 
kotlin.jvm  KEY_HF_TOKEN 
kotlin.jvm  
LazyColumn 
kotlin.jvm  LazyRow 
kotlin.jvm  LocalServer 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  MainActivity 
kotlin.jvm  Manifest 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Math 
kotlin.jvm  Modifier 
kotlin.jvm  MoveHistoryCard 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  NetworkManager 
kotlin.jvm  
NetworkResult 
kotlin.jvm  OutlinedButton 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  
PREFS_NAME 
kotlin.jvm  PackageManager 
kotlin.jvm  
PaddingValues 
kotlin.jvm  Piece 
kotlin.jvm  
PieceColor 
kotlin.jvm  	PieceType 
kotlin.jvm  PositionInfo 
kotlin.jvm  PositionInfoCard 
kotlin.jvm  Preview 
kotlin.jvm  Process 
kotlin.jvm  ProcessCameraProvider 
kotlin.jvm  Python 
kotlin.jvm  PythonGradioService 
kotlin.jvm  R 
kotlin.jvm  REQUIRED_PERMISSIONS 
kotlin.jvm  RoundedCornerShape 
kotlin.jvm  Row 
kotlin.jvm  Runtime 
kotlin.jvm  SettingsActivity 
kotlin.jvm  SettingsScreen 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  Spacer 
kotlin.jvm  Square 
kotlin.jvm  StockfishManager 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  Surface 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  Text 
kotlin.jvm  	TextAlign 
kotlin.jvm  
TextButton 
kotlin.jvm  Toast 
kotlin.jvm  	TopAppBar 
kotlin.jvm  TopAppBarDefaults 
kotlin.jvm  View 
kotlin.jvm  Volatile 
kotlin.jvm  WindowCompat 
kotlin.jvm  
_gameState 
kotlin.jvm  addListener 
kotlin.jvm  all 
kotlin.jvm  also 
kotlin.jvm  analyzeCurrentPosition 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  asStateFlow 
kotlin.jvm  aspectRatio 
kotlin.jvm  
background 
kotlin.jvm  binding 
kotlin.jvm  border 
kotlin.jvm  cancel 
kotlin.jvm  clip 
kotlin.jvm  coerceIn 
kotlin.jvm  com 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  delay 
kotlin.jvm  displayAIResponse 
kotlin.jvm  	emptyList 
kotlin.jvm  
fillMaxHeight 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  filter 
kotlin.jvm  forEach 
kotlin.jvm  generateSimulatedAnalysis 
kotlin.jvm  generateSimulatedEvaluation 
kotlin.jvm  generateSimulatedMove 
kotlin.jvm  	getOrNull 
kotlin.jvm  height 
kotlin.jvm  isAnalyzing 
kotlin.jvm  isEmpty 
kotlin.jvm  
isInitialized 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  iterator 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  joinToString 
kotlin.jvm  
lastOrNull 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  	lowercase 
kotlin.jvm  map 
kotlin.jvm  minOf 
kotlin.jvm  minusAssign 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  networkManager 
kotlin.jvm  padding 
kotlin.jvm  plus 
kotlin.jvm  
plusAssign 
kotlin.jvm  provideDelegate 
kotlin.jvm  python 
kotlin.jvm  random 
kotlin.jvm  	readBytes 
kotlin.jvm  repeat 
kotlin.jvm  run 
kotlin.jvm  set 
kotlin.jvm  	showError 
kotlin.jvm  showProgressBar 
kotlin.jvm  	showToast 
kotlin.jvm  size 
kotlin.jvm  
startsWith 
kotlin.jvm  	substring 
kotlin.jvm  synchronized 
kotlin.jvm  take 
kotlin.jvm  to 
kotlin.jvm  toString 
kotlin.jvm  toTypedArray 
kotlin.jvm  trim 
kotlin.jvm  updateStatus 
kotlin.jvm  updateUIForProvider 
kotlin.jvm  	uppercase 
kotlin.jvm  width 
kotlin.jvm  withContext 
kotlin.jvm  ActivityMainBinding 
kotlin.ranges  ActivityResultContracts 
kotlin.ranges  ActivitySettingsBinding 
kotlin.ranges  
AiResponse 
kotlin.ranges  AlertDialog 
kotlin.ranges  	Alignment 
kotlin.ranges  AnalysisScreen 
kotlin.ranges  AndroidPlatform 
kotlin.ranges  AndroidView 
kotlin.ranges  	ApiConfig 
kotlin.ranges  ApiProvider 
kotlin.ranges  Arrangement 
kotlin.ranges  ArrayAdapter 
kotlin.ranges  Board 
kotlin.ranges  BoardOrientation 
kotlin.ranges  Box 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  ButtonDefaults 
kotlin.ranges  
CameraManager 
kotlin.ranges  CameraScreen 
kotlin.ranges  CameraSelector 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  ChessAnalysisBlue 
kotlin.ranges  ChessAnalysisGreen 
kotlin.ranges  ChessAnalysisRed 
kotlin.ranges  ChessAnalysisScreen 
kotlin.ranges  ChessApp 
kotlin.ranges  ChessApplication 
kotlin.ranges  ChessBoardCard 
kotlin.ranges  ChessBoardInterface 
kotlin.ranges  ChessBoardUtils 
kotlin.ranges  ChessBoardWebView 
kotlin.ranges  ChessColors 
kotlin.ranges  ChessGameState 
kotlin.ranges  
ChessPiece 
kotlin.ranges  ChessPrimary 
kotlin.ranges  ChessScanAccent 
kotlin.ranges  ChessSecondary 
kotlin.ranges  ChessSurface 
kotlin.ranges  ChessSurfaceVariant 
kotlin.ranges  
ChessTheme 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Color 
kotlin.ranges  Column 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  CoroutineScope 
kotlin.ranges  Dispatchers 
kotlin.ranges  EXTRA_SCANNED_FEN 
kotlin.ranges  EngineAnalysis 
kotlin.ranges  EngineAnalysisCard 
kotlin.ranges  
EvaluationBar 
kotlin.ranges  	Exception 
kotlin.ranges  	Executors 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  FILENAME_FORMAT 
kotlin.ranges  FeatureItem 
kotlin.ranges  File 
kotlin.ranges  
FontFamily 
kotlin.ranges  
FontWeight 
kotlin.ranges  GameMode 
kotlin.ranges  
HomeScreen 
kotlin.ranges  HuggingFace 
kotlin.ranges  INSTANCE 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  IllegalStateException 
kotlin.ranges  ImageCapture 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  KEY_API_PROVIDER 
kotlin.ranges  KEY_HF_MODEL 
kotlin.ranges  KEY_HF_TOKEN 
kotlin.ranges  
LazyColumn 
kotlin.ranges  LazyRow 
kotlin.ranges  LocalServer 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  MainActivity 
kotlin.ranges  Manifest 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Math 
kotlin.ranges  Modifier 
kotlin.ranges  MoveHistoryCard 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  NetworkManager 
kotlin.ranges  
NetworkResult 
kotlin.ranges  OutlinedButton 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  
PREFS_NAME 
kotlin.ranges  PackageManager 
kotlin.ranges  
PaddingValues 
kotlin.ranges  Piece 
kotlin.ranges  
PieceColor 
kotlin.ranges  	PieceType 
kotlin.ranges  PositionInfo 
kotlin.ranges  PositionInfoCard 
kotlin.ranges  Preview 
kotlin.ranges  Process 
kotlin.ranges  ProcessCameraProvider 
kotlin.ranges  Python 
kotlin.ranges  PythonGradioService 
kotlin.ranges  R 
kotlin.ranges  REQUIRED_PERMISSIONS 
kotlin.ranges  RoundedCornerShape 
kotlin.ranges  Row 
kotlin.ranges  Runtime 
kotlin.ranges  SettingsActivity 
kotlin.ranges  SettingsScreen 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  Spacer 
kotlin.ranges  Square 
kotlin.ranges  StockfishManager 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  Surface 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  Text 
kotlin.ranges  	TextAlign 
kotlin.ranges  
TextButton 
kotlin.ranges  Toast 
kotlin.ranges  	TopAppBar 
kotlin.ranges  TopAppBarDefaults 
kotlin.ranges  View 
kotlin.ranges  Volatile 
kotlin.ranges  WindowCompat 
kotlin.ranges  
_gameState 
kotlin.ranges  addListener 
kotlin.ranges  all 
kotlin.ranges  also 
kotlin.ranges  analyzeCurrentPosition 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  asStateFlow 
kotlin.ranges  aspectRatio 
kotlin.ranges  
background 
kotlin.ranges  binding 
kotlin.ranges  border 
kotlin.ranges  cancel 
kotlin.ranges  clip 
kotlin.ranges  coerceIn 
kotlin.ranges  com 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  delay 
kotlin.ranges  displayAIResponse 
kotlin.ranges  	emptyList 
kotlin.ranges  
fillMaxHeight 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  filter 
kotlin.ranges  forEach 
kotlin.ranges  generateSimulatedAnalysis 
kotlin.ranges  generateSimulatedEvaluation 
kotlin.ranges  generateSimulatedMove 
kotlin.ranges  	getOrNull 
kotlin.ranges  height 
kotlin.ranges  isAnalyzing 
kotlin.ranges  isEmpty 
kotlin.ranges  
isInitialized 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  iterator 
kotlin.ranges  java 
kotlin.ranges  	javaClass 
kotlin.ranges  joinToString 
kotlin.ranges  
lastOrNull 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  	lowercase 
kotlin.ranges  map 
kotlin.ranges  minOf 
kotlin.ranges  minusAssign 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  networkManager 
kotlin.ranges  padding 
kotlin.ranges  plus 
kotlin.ranges  
plusAssign 
kotlin.ranges  provideDelegate 
kotlin.ranges  python 
kotlin.ranges  random 
kotlin.ranges  	readBytes 
kotlin.ranges  repeat 
kotlin.ranges  run 
kotlin.ranges  set 
kotlin.ranges  	showError 
kotlin.ranges  showProgressBar 
kotlin.ranges  	showToast 
kotlin.ranges  size 
kotlin.ranges  
startsWith 
kotlin.ranges  	substring 
kotlin.ranges  synchronized 
kotlin.ranges  take 
kotlin.ranges  to 
kotlin.ranges  toString 
kotlin.ranges  toTypedArray 
kotlin.ranges  trim 
kotlin.ranges  updateStatus 
kotlin.ranges  updateUIForProvider 
kotlin.ranges  	uppercase 
kotlin.ranges  width 
kotlin.ranges  withContext 
kotlin.ranges  random kotlin.ranges.IntProgression  	getRANDOM kotlin.ranges.IntRange  	getRandom kotlin.ranges.IntRange  random kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ActivityMainBinding kotlin.sequences  ActivityResultContracts kotlin.sequences  ActivitySettingsBinding kotlin.sequences  
AiResponse kotlin.sequences  AlertDialog kotlin.sequences  	Alignment kotlin.sequences  AnalysisScreen kotlin.sequences  AndroidPlatform kotlin.sequences  AndroidView kotlin.sequences  	ApiConfig kotlin.sequences  ApiProvider kotlin.sequences  Arrangement kotlin.sequences  ArrayAdapter kotlin.sequences  Board kotlin.sequences  BoardOrientation kotlin.sequences  Box kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  ButtonDefaults kotlin.sequences  
CameraManager kotlin.sequences  CameraScreen kotlin.sequences  CameraSelector kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  ChessAnalysisBlue kotlin.sequences  ChessAnalysisGreen kotlin.sequences  ChessAnalysisRed kotlin.sequences  ChessAnalysisScreen kotlin.sequences  ChessApp kotlin.sequences  ChessApplication kotlin.sequences  ChessBoardCard kotlin.sequences  ChessBoardInterface kotlin.sequences  ChessBoardUtils kotlin.sequences  ChessBoardWebView kotlin.sequences  ChessColors kotlin.sequences  ChessGameState kotlin.sequences  
ChessPiece kotlin.sequences  ChessPrimary kotlin.sequences  ChessScanAccent kotlin.sequences  ChessSecondary kotlin.sequences  ChessSurface kotlin.sequences  ChessSurfaceVariant kotlin.sequences  
ChessTheme kotlin.sequences  CircularProgressIndicator kotlin.sequences  Color kotlin.sequences  Column kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  CoroutineScope kotlin.sequences  Dispatchers kotlin.sequences  EXTRA_SCANNED_FEN kotlin.sequences  EngineAnalysis kotlin.sequences  EngineAnalysisCard kotlin.sequences  
EvaluationBar kotlin.sequences  	Exception kotlin.sequences  	Executors kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  FILENAME_FORMAT kotlin.sequences  FeatureItem kotlin.sequences  File kotlin.sequences  
FontFamily kotlin.sequences  
FontWeight kotlin.sequences  GameMode kotlin.sequences  
HomeScreen kotlin.sequences  HuggingFace kotlin.sequences  INSTANCE kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  IllegalStateException kotlin.sequences  ImageCapture kotlin.sequences  Intent kotlin.sequences  KEY_API_PROVIDER kotlin.sequences  KEY_HF_MODEL kotlin.sequences  KEY_HF_TOKEN kotlin.sequences  
LazyColumn kotlin.sequences  LazyRow kotlin.sequences  LocalServer kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  MainActivity kotlin.sequences  Manifest kotlin.sequences  
MaterialTheme kotlin.sequences  Math kotlin.sequences  Modifier kotlin.sequences  MoveHistoryCard kotlin.sequences  MutableStateFlow kotlin.sequences  NetworkManager kotlin.sequences  
NetworkResult kotlin.sequences  OutlinedButton kotlin.sequences  OutlinedTextField kotlin.sequences  
PREFS_NAME kotlin.sequences  PackageManager kotlin.sequences  
PaddingValues kotlin.sequences  Piece kotlin.sequences  
PieceColor kotlin.sequences  	PieceType kotlin.sequences  PositionInfo kotlin.sequences  PositionInfoCard kotlin.sequences  Preview kotlin.sequences  Process kotlin.sequences  ProcessCameraProvider kotlin.sequences  Python kotlin.sequences  PythonGradioService kotlin.sequences  R kotlin.sequences  REQUIRED_PERMISSIONS kotlin.sequences  RoundedCornerShape kotlin.sequences  Row kotlin.sequences  Runtime kotlin.sequences  SettingsActivity kotlin.sequences  SettingsScreen kotlin.sequences  SimpleDateFormat kotlin.sequences  Spacer kotlin.sequences  Square kotlin.sequences  StockfishManager kotlin.sequences  
SupervisorJob kotlin.sequences  Surface kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  Text kotlin.sequences  	TextAlign kotlin.sequences  
TextButton kotlin.sequences  Toast kotlin.sequences  	TopAppBar kotlin.sequences  TopAppBarDefaults kotlin.sequences  View kotlin.sequences  Volatile kotlin.sequences  WindowCompat kotlin.sequences  
_gameState kotlin.sequences  addListener kotlin.sequences  all kotlin.sequences  also kotlin.sequences  analyzeCurrentPosition kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  asStateFlow kotlin.sequences  aspectRatio kotlin.sequences  
background kotlin.sequences  binding kotlin.sequences  border kotlin.sequences  cancel kotlin.sequences  clip kotlin.sequences  coerceIn kotlin.sequences  com kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  delay kotlin.sequences  displayAIResponse kotlin.sequences  	emptyList kotlin.sequences  
fillMaxHeight kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  filter kotlin.sequences  forEach kotlin.sequences  generateSimulatedAnalysis kotlin.sequences  generateSimulatedEvaluation kotlin.sequences  generateSimulatedMove kotlin.sequences  	getOrNull kotlin.sequences  height kotlin.sequences  isAnalyzing kotlin.sequences  isEmpty kotlin.sequences  
isInitialized kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrEmpty kotlin.sequences  iterator kotlin.sequences  java kotlin.sequences  	javaClass kotlin.sequences  joinToString kotlin.sequences  
lastOrNull kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  	lowercase kotlin.sequences  map kotlin.sequences  minOf kotlin.sequences  minusAssign kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  networkManager kotlin.sequences  padding kotlin.sequences  plus kotlin.sequences  
plusAssign kotlin.sequences  provideDelegate kotlin.sequences  python kotlin.sequences  random kotlin.sequences  	readBytes kotlin.sequences  repeat kotlin.sequences  run kotlin.sequences  set kotlin.sequences  	showError kotlin.sequences  showProgressBar kotlin.sequences  	showToast kotlin.sequences  size kotlin.sequences  
startsWith kotlin.sequences  	substring kotlin.sequences  synchronized kotlin.sequences  take kotlin.sequences  to kotlin.sequences  toString kotlin.sequences  toTypedArray kotlin.sequences  trim kotlin.sequences  updateStatus kotlin.sequences  updateUIForProvider kotlin.sequences  	uppercase kotlin.sequences  width kotlin.sequences  withContext kotlin.sequences  ActivityMainBinding kotlin.text  ActivityResultContracts kotlin.text  ActivitySettingsBinding kotlin.text  
AiResponse kotlin.text  AlertDialog kotlin.text  	Alignment kotlin.text  AnalysisScreen kotlin.text  AndroidPlatform kotlin.text  AndroidView kotlin.text  	ApiConfig kotlin.text  ApiProvider kotlin.text  Arrangement kotlin.text  ArrayAdapter kotlin.text  Board kotlin.text  BoardOrientation kotlin.text  Box kotlin.text  Build kotlin.text  Button kotlin.text  ButtonDefaults kotlin.text  
CameraManager kotlin.text  CameraScreen kotlin.text  CameraSelector kotlin.text  Card kotlin.text  CardDefaults kotlin.text  ChessAnalysisBlue kotlin.text  ChessAnalysisGreen kotlin.text  ChessAnalysisRed kotlin.text  ChessAnalysisScreen kotlin.text  ChessApp kotlin.text  ChessApplication kotlin.text  ChessBoardCard kotlin.text  ChessBoardInterface kotlin.text  ChessBoardUtils kotlin.text  ChessBoardWebView kotlin.text  ChessColors kotlin.text  ChessGameState kotlin.text  
ChessPiece kotlin.text  ChessPrimary kotlin.text  ChessScanAccent kotlin.text  ChessSecondary kotlin.text  ChessSurface kotlin.text  ChessSurfaceVariant kotlin.text  
ChessTheme kotlin.text  CircularProgressIndicator kotlin.text  Color kotlin.text  Column kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  CoroutineScope kotlin.text  Dispatchers kotlin.text  EXTRA_SCANNED_FEN kotlin.text  EngineAnalysis kotlin.text  EngineAnalysisCard kotlin.text  
EvaluationBar kotlin.text  	Exception kotlin.text  	Executors kotlin.text  ExperimentalMaterial3Api kotlin.text  FILENAME_FORMAT kotlin.text  FeatureItem kotlin.text  File kotlin.text  
FontFamily kotlin.text  
FontWeight kotlin.text  GameMode kotlin.text  
HomeScreen kotlin.text  HuggingFace kotlin.text  INSTANCE kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  IllegalStateException kotlin.text  ImageCapture kotlin.text  Intent kotlin.text  KEY_API_PROVIDER kotlin.text  KEY_HF_MODEL kotlin.text  KEY_HF_TOKEN kotlin.text  
LazyColumn kotlin.text  LazyRow kotlin.text  LocalServer kotlin.text  Locale kotlin.text  Log kotlin.text  MainActivity kotlin.text  Manifest kotlin.text  
MaterialTheme kotlin.text  Math kotlin.text  Modifier kotlin.text  MoveHistoryCard kotlin.text  MutableStateFlow kotlin.text  NetworkManager kotlin.text  
NetworkResult kotlin.text  OutlinedButton kotlin.text  OutlinedTextField kotlin.text  
PREFS_NAME kotlin.text  PackageManager kotlin.text  
PaddingValues kotlin.text  Piece kotlin.text  
PieceColor kotlin.text  	PieceType kotlin.text  PositionInfo kotlin.text  PositionInfoCard kotlin.text  Preview kotlin.text  Process kotlin.text  ProcessCameraProvider kotlin.text  Python kotlin.text  PythonGradioService kotlin.text  R kotlin.text  REQUIRED_PERMISSIONS kotlin.text  RoundedCornerShape kotlin.text  Row kotlin.text  Runtime kotlin.text  SettingsActivity kotlin.text  SettingsScreen kotlin.text  SimpleDateFormat kotlin.text  Spacer kotlin.text  Square kotlin.text  StockfishManager kotlin.text  
SupervisorJob kotlin.text  Surface kotlin.text  System kotlin.text  TAG kotlin.text  Text kotlin.text  	TextAlign kotlin.text  
TextButton kotlin.text  Toast kotlin.text  	TopAppBar kotlin.text  TopAppBarDefaults kotlin.text  View kotlin.text  Volatile kotlin.text  WindowCompat kotlin.text  
_gameState kotlin.text  addListener kotlin.text  all kotlin.text  also kotlin.text  analyzeCurrentPosition kotlin.text  android kotlin.text  androidx kotlin.text  apply kotlin.text  arrayOf kotlin.text  asStateFlow kotlin.text  aspectRatio kotlin.text  
background kotlin.text  binding kotlin.text  border kotlin.text  cancel kotlin.text  clip kotlin.text  coerceIn kotlin.text  com kotlin.text  
component1 kotlin.text  
component2 kotlin.text  delay kotlin.text  displayAIResponse kotlin.text  	emptyList kotlin.text  
fillMaxHeight kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  filter kotlin.text  forEach kotlin.text  generateSimulatedAnalysis kotlin.text  generateSimulatedEvaluation kotlin.text  generateSimulatedMove kotlin.text  	getOrNull kotlin.text  height kotlin.text  isAnalyzing kotlin.text  isEmpty kotlin.text  
isInitialized kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  iterator kotlin.text  java kotlin.text  	javaClass kotlin.text  joinToString kotlin.text  
lastOrNull kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  	lowercase kotlin.text  map kotlin.text  minOf kotlin.text  minusAssign kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  networkManager kotlin.text  padding kotlin.text  plus kotlin.text  
plusAssign kotlin.text  provideDelegate kotlin.text  python kotlin.text  random kotlin.text  	readBytes kotlin.text  repeat kotlin.text  run kotlin.text  set kotlin.text  	showError kotlin.text  showProgressBar kotlin.text  	showToast kotlin.text  size kotlin.text  
startsWith kotlin.text  	substring kotlin.text  synchronized kotlin.text  take kotlin.text  to kotlin.text  toString kotlin.text  toTypedArray kotlin.text  trim kotlin.text  updateStatus kotlin.text  updateUIForProvider kotlin.text  	uppercase kotlin.text  width kotlin.text  withContext kotlin.text  BufferedReader kotlinx.coroutines  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  EngineAnalysis kotlinx.coroutines  	Exception kotlinx.coroutines  Job kotlinx.coroutines  Log kotlinx.coroutines  Math kotlinx.coroutines  PrintWriter kotlinx.coroutines  Process kotlinx.coroutines  Runtime kotlinx.coroutines  StockfishManager kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  TAG kotlinx.coroutines  Volatile kotlinx.coroutines  also kotlinx.coroutines  androidx kotlinx.coroutines  cancel kotlinx.coroutines  delay kotlinx.coroutines  	emptyList kotlinx.coroutines  generateSimulatedAnalysis kotlinx.coroutines  generateSimulatedEvaluation kotlinx.coroutines  generateSimulatedMove kotlinx.coroutines  isAnalyzing kotlinx.coroutines  
isInitialized kotlinx.coroutines  iterator kotlinx.coroutines  launch kotlinx.coroutines  listOf kotlinx.coroutines  minOf kotlinx.coroutines  minusAssign kotlinx.coroutines  
mutableListOf kotlinx.coroutines  
plusAssign kotlinx.coroutines  random kotlinx.coroutines  repeat kotlinx.coroutines  synchronized kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  
AiResponse !kotlinx.coroutines.CoroutineScope  Board !kotlinx.coroutines.CoroutineScope  ChessBoardUtils !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Manifest !kotlinx.coroutines.CoroutineScope  Math !kotlinx.coroutines.CoroutineScope  
NetworkResult !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  
_gameState !kotlinx.coroutines.CoroutineScope  analyzeCurrentPosition !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  binding !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  
component1 !kotlinx.coroutines.CoroutineScope  
component2 !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  displayAIResponse !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  generateSimulatedAnalysis !kotlinx.coroutines.CoroutineScope  generateSimulatedEvaluation !kotlinx.coroutines.CoroutineScope  generateSimulatedMove !kotlinx.coroutines.CoroutineScope  getANALYZECurrentPosition !kotlinx.coroutines.CoroutineScope  
getANDROID !kotlinx.coroutines.CoroutineScope  getAnalyzeCurrentPosition !kotlinx.coroutines.CoroutineScope  
getAndroid !kotlinx.coroutines.CoroutineScope  
getBINDING !kotlinx.coroutines.CoroutineScope  
getBinding !kotlinx.coroutines.CoroutineScope  	getCANCEL !kotlinx.coroutines.CoroutineScope  	getCancel !kotlinx.coroutines.CoroutineScope  
getComponent1 !kotlinx.coroutines.CoroutineScope  
getComponent2 !kotlinx.coroutines.CoroutineScope  getDELAY !kotlinx.coroutines.CoroutineScope  getDelay !kotlinx.coroutines.CoroutineScope  getDisplayAIResponse !kotlinx.coroutines.CoroutineScope  getEMPTYList !kotlinx.coroutines.CoroutineScope  getEmptyList !kotlinx.coroutines.CoroutineScope  getGENERATESimulatedAnalysis !kotlinx.coroutines.CoroutineScope  getGENERATESimulatedEvaluation !kotlinx.coroutines.CoroutineScope  getGENERATESimulatedMove !kotlinx.coroutines.CoroutineScope  getGenerateSimulatedAnalysis !kotlinx.coroutines.CoroutineScope  getGenerateSimulatedEvaluation !kotlinx.coroutines.CoroutineScope  getGenerateSimulatedMove !kotlinx.coroutines.CoroutineScope  getISAnalyzing !kotlinx.coroutines.CoroutineScope  getISInitialized !kotlinx.coroutines.CoroutineScope  getISNullOrEmpty !kotlinx.coroutines.CoroutineScope  getITERATOR !kotlinx.coroutines.CoroutineScope  getIsAnalyzing !kotlinx.coroutines.CoroutineScope  getIsInitialized !kotlinx.coroutines.CoroutineScope  getIsNullOrEmpty !kotlinx.coroutines.CoroutineScope  getIterator !kotlinx.coroutines.CoroutineScope  getJOINToString !kotlinx.coroutines.CoroutineScope  getJoinToString !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  getLET !kotlinx.coroutines.CoroutineScope  getLOWERCASE !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getLet !kotlinx.coroutines.CoroutineScope  getLowercase !kotlinx.coroutines.CoroutineScope  getMAP !kotlinx.coroutines.CoroutineScope  getMUTABLEMapOf !kotlinx.coroutines.CoroutineScope  getMap !kotlinx.coroutines.CoroutineScope  getMutableMapOf !kotlinx.coroutines.CoroutineScope  getNETWORKManager !kotlinx.coroutines.CoroutineScope  getNetworkManager !kotlinx.coroutines.CoroutineScope  getPLUS !kotlinx.coroutines.CoroutineScope  	getPYTHON !kotlinx.coroutines.CoroutineScope  getPlus !kotlinx.coroutines.CoroutineScope  	getPython !kotlinx.coroutines.CoroutineScope  getREADBytes !kotlinx.coroutines.CoroutineScope  getReadBytes !kotlinx.coroutines.CoroutineScope  getSET !kotlinx.coroutines.CoroutineScope  getSHOWError !kotlinx.coroutines.CoroutineScope  getSHOWProgressBar !kotlinx.coroutines.CoroutineScope  getSHOWToast !kotlinx.coroutines.CoroutineScope  getSet !kotlinx.coroutines.CoroutineScope  getShowError !kotlinx.coroutines.CoroutineScope  getShowProgressBar !kotlinx.coroutines.CoroutineScope  getShowToast !kotlinx.coroutines.CoroutineScope  getUPDATEStatus !kotlinx.coroutines.CoroutineScope  getUpdateStatus !kotlinx.coroutines.CoroutineScope  
get_gameState !kotlinx.coroutines.CoroutineScope  isAnalyzing !kotlinx.coroutines.CoroutineScope  
isInitialized !kotlinx.coroutines.CoroutineScope  
isNullOrEmpty !kotlinx.coroutines.CoroutineScope  iterator !kotlinx.coroutines.CoroutineScope  	javaClass !kotlinx.coroutines.CoroutineScope  joinToString !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  	lowercase !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  mutableMapOf !kotlinx.coroutines.CoroutineScope  networkManager !kotlinx.coroutines.CoroutineScope  plus !kotlinx.coroutines.CoroutineScope  python !kotlinx.coroutines.CoroutineScope  	readBytes !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  	showError !kotlinx.coroutines.CoroutineScope  showProgressBar !kotlinx.coroutines.CoroutineScope  	showToast !kotlinx.coroutines.CoroutineScope  updateStatus !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsState !kotlinx.coroutines.flow.StateFlow  getCollectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       