package com.example.aicamera.chess

import android.content.Context
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.github.bhlangonijr.chesslib.Square
import com.github.bhlangonijr.chesslib.move.Move

/**
 * Chess board component using WebView with HTML/CSS/JS
 * Provides interactive chess board with piece movement
 */

@Composable
fun ChessBoardWebView(
    gameState: ChessGameState,
    onMoveSelected: (String) -> Unit,
    onSquareSelected: (String) -> Unit,
    onSquareClicked: (String) -> Unit,
    modifier: Modifier = Modifier,
    orientation: BoardOrientation = BoardOrientation.WHITE,
    showCoordinates: Boolean = true,
    isInteractive: Boolean = true
) {
    var webView by remember { mutableStateOf<WebView?>(null) }

    AndroidView(
        factory = { context ->
            createChessWebView(
                context = context,
                onMoveSelected = onMoveSelected,
                onSquareSelected = onSquareSelected,
                onSquareClicked = onSquareClicked
            ).also { view ->
                webView = view
            }
        },
        modifier = modifier,
        update = { view ->
            // Update position when game state changes
            updateWebViewPosition(view, gameState, orientation)
        }
    )

    // Update possible moves when selection changes
    LaunchedEffect(gameState.possibleMoves) {
        webView?.let { view ->
            val moves = gameState.possibleMoves.map { it.to.toString().lowercase() }
            view.evaluateJavascript(
                "setPossibleMoves(${moves.joinToString(",") { "'$it'" }})",
                null
            )
        }
    }

    // Update last move highlight
    LaunchedEffect(gameState.lastMove) {
        webView?.let { view ->
            gameState.lastMove?.let { move ->
                view.evaluateJavascript(
                    "setLastMove('${move.from.toString().lowercase()}', '${move.to.toString().lowercase()}')",
                    null
                )
            }
        }
    }

    // Update engine move highlight
    LaunchedEffect(gameState.engineAnalysis?.bestMove) {
        webView?.let { view ->
            gameState.engineAnalysis?.bestMove?.let { bestMove ->
                if (bestMove.length >= 4) {
                    view.evaluateJavascript(
                        "setEngineMove('$bestMove')",
                        null
                    )
                }
            }
        }
    }

    // Update edit mode
    LaunchedEffect(gameState.isEditMode) {
        webView?.let { view ->
            view.evaluateJavascript(
                "setEditMode(${gameState.isEditMode})",
                null
            )
        }
    }
}

private fun createChessWebView(
    context: Context,
    onMoveSelected: (String) -> Unit,
    onSquareSelected: (String) -> Unit,
    onSquareClicked: (String) -> Unit
): WebView {
    return WebView(context).apply {
        webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                Log.d("ChessBoardWebView", "Chess board loaded successfully")
            }
        }

        settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
        }

        // Add JavaScript interface for communication
        addJavascriptInterface(
            ChessBoardInterface(
                onMoveSelected = onMoveSelected,
                onSquareSelected = onSquareSelected,
                onSquareClicked = onSquareClicked
            ),
            "Android"
        )

        // Load the chess board HTML
        loadUrl("file:///android_asset/chessboard.html")
    }
}

private fun updateWebViewPosition(
    webView: WebView,
    gameState: ChessGameState,
    orientation: BoardOrientation
) {
    webView.evaluateJavascript(
        "updatePosition('${gameState.currentFen}', '${orientation.name.lowercase()}')",
        null
    )
}

/**
 * JavaScript interface for chess board communication
 */
class ChessBoardInterface(
    private val onMoveSelected: (String) -> Unit,
    private val onSquareSelected: (String) -> Unit,
    private val onSquareClicked: (String) -> Unit
) {

    @JavascriptInterface
    fun onMoveSelected(move: String) {
        Log.d("ChessBoardInterface", "Move selected: $move")
        onMoveSelected(move)
    }

    @JavascriptInterface
    fun onSquareSelected(square: String) {
        Log.d("ChessBoardInterface", "Square selected: $square")
        onSquareSelected(square)
    }

    @JavascriptInterface
    fun onSquareClicked(square: String) {
        Log.d("ChessBoardInterface", "Square clicked: $square")
        onSquareClicked(square)
    }
}

/**
 * Chess board utilities
 */
object ChessBoardUtils {

    /**
     * Convert move string to Move object
     */
    fun parseMove(moveString: String): com.github.bhlangonijr.chesslib.move.Move? {
        return try {
            if (moveString.length >= 4) {
                val from = Square.valueOf(moveString.substring(0, 2).uppercase())
                val to = Square.valueOf(moveString.substring(2, 4).uppercase())
                com.github.bhlangonijr.chesslib.move.Move(from, to)
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e("ChessBoardUtils", "Failed to parse move: $moveString", e)
            null
        }
    }

    /**
     * Get legal moves from a square
     */
    fun getLegalMovesFromSquare(
        board: com.github.bhlangonijr.chesslib.Board,
        square: Square
    ): List<com.github.bhlangonijr.chesslib.move.Move> {
        return try {
            board.legalMoves().filter { it.from == square }
        } catch (e: Exception) {
            Log.e("ChessBoardUtils", "Failed to get legal moves", e)
            emptyList()
        }
    }

    /**
     * Check if move is legal
     */
    fun isLegalMove(
        board: com.github.bhlangonijr.chesslib.Board,
        move: com.github.bhlangonijr.chesslib.move.Move
    ): Boolean {
        return try {
            board.legalMoves().contains(move)
        } catch (e: Exception) {
            Log.e("ChessBoardUtils", "Failed to check move legality", e)
            false
        }
    }

    /**
     * Make move on board
     */
    fun makeMove(
        board: com.github.bhlangonijr.chesslib.Board,
        move: com.github.bhlangonijr.chesslib.move.Move
    ): Boolean {
        return try {
            if (isLegalMove(board, move)) {
                board.doMove(move as com.github.bhlangonijr.chesslib.move.Move)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e("ChessBoardUtils", "Failed to make move", e)
            false
        }
    }

    /**
     * Undo last move
     */
    fun undoMove(board: com.github.bhlangonijr.chesslib.Board): Boolean {
        return try {
            board.undoMove()
            true
        } catch (e: Exception) {
            Log.e("ChessBoardUtils", "Failed to undo move", e)
            false
        }
    }

    /**
     * Get position info from board
     */
    fun getPositionInfo(board: com.github.bhlangonijr.chesslib.Board): PositionInfo {
        return try {
            PositionInfo(
                fen = board.fen,
                sideToMove = if (board.sideToMove == com.github.bhlangonijr.chesslib.Side.WHITE)
                    PieceColor.WHITE else PieceColor.BLACK,
                castlingRights = board.castleRight.toString(),
                enPassantSquare = board.enPassantTarget?.toString(),
                halfMoveClock = board.halfMoveCounter,
                fullMoveNumber = 1, // TODO: Get from board
                isCheck = board.isKingAttacked,
                isCheckmate = board.isMated,
                isStalemate = board.isStaleMate,
                materialBalance = calculateMaterialBalance(board)
            )
        } catch (e: Exception) {
            Log.e("ChessBoardUtils", "Failed to get position info", e)
            PositionInfo(
                fen = board.fen,
                sideToMove = PieceColor.WHITE,
                castlingRights = "",
                enPassantSquare = null,
                halfMoveClock = 0,
                fullMoveNumber = 1,
                isCheck = false,
                isCheckmate = false,
                isStalemate = false,
                materialBalance = 0
            )
        }
    }

    private fun calculateMaterialBalance(board: com.github.bhlangonijr.chesslib.Board): Int {
        var balance = 0

        for (square in Square.values()) {
            val piece = board.getPiece(square)
            val value = when (piece.pieceType) {
                com.github.bhlangonijr.chesslib.PieceType.PAWN -> 100
                com.github.bhlangonijr.chesslib.PieceType.KNIGHT -> 300
                com.github.bhlangonijr.chesslib.PieceType.BISHOP -> 300
                com.github.bhlangonijr.chesslib.PieceType.ROOK -> 500
                com.github.bhlangonijr.chesslib.PieceType.QUEEN -> 900
                com.github.bhlangonijr.chesslib.PieceType.KING -> 0
                else -> 0
            }

            if (piece.pieceSide == com.github.bhlangonijr.chesslib.Side.WHITE) {
                balance += value
            } else if (piece.pieceSide == com.github.bhlangonijr.chesslib.Side.BLACK) {
                balance -= value
            }
        }

        return balance
    }
}
