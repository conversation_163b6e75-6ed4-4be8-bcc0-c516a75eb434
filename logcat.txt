--------- beginning of main
--------- beginning of system
---------------------------- PROCESS STARTED (10376) for package com.example.aicamera ----------------------------
2025-05-30 13:55:16.659 10376-10376 ziparchive              com.example.aicamera                 W  Unable to open '/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.dm': No such file or directory
2025-05-30 13:55:16.659 10376-10376 ziparchive              com.example.aicamera                 W  Unable to open '/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.dm': No such file or directory
2025-05-30 13:55:17.431 10376-10376 nativeloader            com.example.aicamera                 D  Configuring clns-4 for other apk /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/lib/arm64:/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.example.aicamera
2025-05-30 13:55:17.462 10376-10376 GraphicsEnvironment     com.example.aicamera                 V  ANGLE Developer option for 'com.example.aicamera' set to: 'default'
2025-05-30 13:55:17.462 10376-10376 GraphicsEnvironment     com.example.aicamera                 V  Neither updatable production driver nor prerelease driver is supported.
2025-05-30 13:55:17.468 10376-10376 NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 13:55:17.471 10376-10376 NetworkSecurityConfig   com.example.aicamera                 D  No Network Security Config specified, using platform default
2025-05-30 13:55:17.502 10376-10376 libc                    com.example.aicamera                 W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-30 13:55:17.898 10376-10376 xample.aicamer          com.example.aicamera                 E  Invalid ID 0x00000000.
2025-05-30 13:55:18.143 10376-10376 Choreographer           com.example.aicamera                 I  Skipped 38 frames!  The application may be doing too much work on its main thread.
2025-05-30 13:55:18.148 10376-10376 XDR::VRT                com.example.aicamera                 I  sc is not valid!
2025-05-30 13:55:18.812 10376-10376 xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateMap.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-30 13:55:18.812 10376-10376 xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateMap.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 13:55:18.814 10376-10376 xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateMap.removeIf$runtime_release(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 13:55:19.607 10376-10376 xample.aicamer          com.example.aicamera                 W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 13:55:19.607 10376-10376 xample.aicamer          com.example.aicamera                 W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 13:55:19.608 10376-10376 xample.aicamer          com.example.aicamera                 W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-30 13:55:20.004 10376-10376 BufferQueueConsumer     com.example.aicamera                 I  [](id:************,api:0,p:-1,c:10376) connect: controlledByApp=false
2025-05-30 13:55:20.007 10376-10376 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0] constructor()
2025-05-30 13:55:20.031 10376-10395 hw-ProcessState         com.example.aicamera                 D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-30 13:55:20.063 10376-10395 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:************,api:1,p:10376,c:10376) connect: api=1 producerControlledByApp=true
2025-05-30 13:55:20.072 10376-10404 ion                     com.example.aicamera                 E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-30 13:55:20.937 10376-10376 Compatibil...geReporter com.example.aicamera                 D  Compat change id reported: 171228096; UID 10742; state: ENABLED
2025-05-30 13:55:21.124 10376-10381 xample.aicamer          com.example.aicamera                 I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-30 13:55:22.224 10376-10395 OpenGLRenderer          com.example.aicamera                 E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-30 13:55:22.224 10376-10395 OpenGLRenderer          com.example.aicamera                 E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-30 13:55:22.250 10376-10390 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=4714ms; Flags=1, FrameTimelineVsyncId=31499856, IntendedVsync=578302166510129, Vsync=578302799843475, InputEventId=0, HandleInputStart=578302802553776, AnimationStart=578302802574161, PerformTraversalsStart=578302803066776, DrawStart=578306674354777, FrameDeadline=578302186510129, FrameInterval=578302802092007, FrameStartTime=16666667, SyncQueued=578306801903930, SyncStart=578306812819930, IssueDrawCommandsStart=578306815307930, SwapBuffers=578306881821161, FrameCompleted=578306892238392, DequeueBufferDuration=0, QueueBufferDuration=4345307, GpuCompleted=578306890832546, SwapBuffersCompleted=578306892238392, DisplayPresentTime=0, 
2025-05-30 13:55:22.521 10376-10376 ImeFocusController      com.example.aicamera                 V  onWindowFocus: DecorView@36fe229[ComposeMainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-30 13:55:22.521 10376-10376 ImeFocusController      com.example.aicamera                 V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-30 13:55:22.522 10376-10376 ImeFocusController      com.example.aicamera                 D  onViewFocusChanged, view=DecorView@36fe229[ComposeMainActivity], mServedView=null
2025-05-30 13:55:22.522 10376-10376 ImeFocusController      com.example.aicamera                 V  checkFocus: view=null next=DecorView@36fe229[ComposeMainActivity] force=true package=<none>
2025-05-30 13:55:23.127 10376-10382 xample.aicamer          com.example.aicamera                 I  Background young concurrent copying GC freed 670KB AllocSpace bytes, 0(0B) LOS objects, 11% free, 5312KB/6010KB, paused 192us,126us total 101.039ms
2025-05-30 13:55:24.494 10376-10423 ProfileInstaller        com.example.aicamera                 D  Installing profile for com.example.aicamera
2025-05-30 13:55:25.229 10376-10425 CameraManagerGlobal     com.example.aicamera                 I  Connecting to camera service
2025-05-30 13:55:25.234 10376-10376 Choreographer           com.example.aicamera                 I  Skipped 42 frames!  The application may be doing too much work on its main thread.
2025-05-30 13:55:25.299 10376-10425 CameraRepository        com.example.aicamera                 D  Added camera: 0
2025-05-30 13:55:25.350 10376-10390 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=813ms; Flags=0, FrameTimelineVsyncId=31500054, IntendedVsync=578309182001596, Vsync=578309882001610, InputEventId=0, HandleInputStart=578309893566469, AnimationStart=578309893570700, PerformTraversalsStart=578309975254392, DrawStart=578309975418777, FrameDeadline=578309218668263, FrameInterval=578309893343931, FrameStartTime=16666667, SyncQueued=578309985897238, SyncStart=578309985949238, IssueDrawCommandsStart=578309986167315, SwapBuffers=578309988571931, FrameCompleted=578309995637546, DequeueBufferDuration=32616, QueueBufferDuration=1615385, GpuCompleted=578309995637546, SwapBuffersCompleted=578309991518700, DisplayPresentTime=0, 
2025-05-30 13:55:25.355 10376-10425 Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 13:55:25.368 10376-10425 CameraRepository        com.example.aicamera                 D  Added camera: 1
2025-05-30 13:55:25.371 10376-10425 Camera2CameraInfo       com.example.aicamera                 I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-30 13:55:25.373 10376-10425 CameraValidator         com.example.aicamera                 D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-30 13:55:25.440 10376-10376 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 13:55:25.441 10376-10376 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 13:55:25.492 10376-10376 DynamicRangeResolver    com.example.aicamera                 D  Resolved dynamic range for use case androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f66 to no compatible HDR dynamic ranges.
                                                                                                    DynamicRange@1ef92fa{encoding=UNSPECIFIED, bitDepth=0}
                                                                                                    ->
                                                                                                    DynamicRange@e922125{encoding=SDR, bitDepth=8}
2025-05-30 13:55:25.512 10376-10376 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 13:55:25.514 10376-10376 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=1, used_surfaces=0](androidx.camera.core.processing.SurfaceEdge$SettableSurface@250ec87}
2025-05-30 13:55:25.523 10376-10376 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=2, used_surfaces=0](androidx.camera.core.SurfaceRequest$2@45ca23}
2025-05-30 13:55:25.528 10376-10376 DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@45ca23}
2025-05-30 13:55:25.529 10376-10376 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.SurfaceRequest$2@45ca23
2025-05-30 13:55:25.531 10376-10376 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 13:55:25.546 10376-10376 ImageCapture            com.example.aicamera                 D  createPipeline(cameraId: 0, streamSpec: StreamSpec{resolution=3264x2448, dynamicRange=DynamicRange@e922125{encoding=SDR, bitDepth=8}, expectedFrameRateRange=[0, 0], implementationOptions=androidx.camera.camera2.impl.Camera2ImplConfig@28994c})
2025-05-30 13:55:25.560 10376-10376 BufferQueueConsumer     com.example.aicamera                 I  [](id:288800000001,api:0,p:-1,c:10376) connect: controlledByApp=true
2025-05-30 13:55:25.561 10376-10376 DeferrableSurface       com.example.aicamera                 D  Surface created[total_surfaces=3, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@cc69d95}
2025-05-30 13:55:25.579 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Use case androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648 ACTIVE
2025-05-30 13:55:25.581 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 13:55:25.582 10376-10376 PreviewView             com.example.aicamera                 D  Surface requested by Preview.
2025-05-30 13:55:25.589 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Use case androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051 ACTIVE
2025-05-30 13:55:25.590 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 13:55:25.592 10376-10376 SurfaceFactory          com.example.aicamera                 I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@a7ec5e4
2025-05-30 13:55:25.597 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Use case androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648 ACTIVE
2025-05-30 13:55:25.599 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 13:55:25.600 10376-10376 PreviewView             com.example.aicamera                 D  Preview transformation info updated. TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false}
2025-05-30 13:55:25.601 10376-10376 PreviewTransform        com.example.aicamera                 D  Transformation info set: TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false} 1024x768 false
2025-05-30 13:55:25.602 10376-10376 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 13:55:25.605 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 13:55:25.616 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Use cases [androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051, androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648] now ATTACHED
2025-05-30 13:55:25.621 10376-10426 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648, androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051] for camera: 0
2025-05-30 13:55:25.623 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  mMeteringRepeating is ATTACHED, SessionConfig Surfaces: 2, CaptureConfig Surfaces: 1
2025-05-30 13:55:25.632 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648, androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051] for camera: 0
2025-05-30 13:55:25.647 10376-10376 SurfaceViewImpl         com.example.aicamera                 D  Wait for new Surface creation.
2025-05-30 13:55:25.648 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Resetting Capture Session
2025-05-30 13:55:25.651 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Releasing session in state INITIALIZED
2025-05-30 13:55:25.656 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Attempting to force open the camera.
2025-05-30 13:55:25.656 10376-10376 BufferQueueConsumer     com.example.aicamera                 I  [](id:288800000002,api:0,p:-1,c:10376) connect: controlledByApp=false
2025-05-30 13:55:25.658 10376-10426 CameraStateRegistry     com.example.aicamera                 D  tryOpenCamera(Camera@36424bc[id=0]) [Available Cameras: 1, Already Open: false (Previous state: null)] --> SUCCESS
2025-05-30 13:55:25.658 10376-10376 BLASTBufferQueue        com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1] constructor()
2025-05-30 13:55:25.663 10376-10376 SurfaceViewImpl         com.example.aicamera                 D  Surface created.
2025-05-30 13:55:25.663 10376-10426 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@6770ea7[id=1]                         UNKNOWN               
                                                                                                    Camera@36424bc[id=0]                         OPENING               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 13:55:25.665 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Opening camera.
2025-05-30 13:55:25.667 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Transitioning camera internal state: INITIALIZED --> OPENING
2025-05-30 13:55:25.667 10376-10376 SurfaceViewImpl         com.example.aicamera                 D  Surface changed. Size: 1024x768
2025-05-30 13:55:25.667 10376-10376 SurfaceViewImpl         com.example.aicamera                 D  Surface set on Preview.
2025-05-30 13:55:25.668 10376-10426 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPENING, error=null} from OPENING and null
2025-05-30 13:55:25.669 10376-10426 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPENING, error=null}
2025-05-30 13:55:25.673 10376-10426 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648, androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051] for camera: 0
2025-05-30 13:55:25.685 10376-10426 libc                    com.example.aicamera                 W  Access denied finding property "persist.vendor.camera.privapp.list"
2025-05-30 13:55:25.681 10376-10376 CameraX-core_ca         com.example.aicamera                 W  type=1400 audit(0.0:6497357): avc: denied { read } for name="u:object_r:vendor_camera_mtk_prop:s0" dev="tmpfs" ino=13626 scontext=u:r:untrusted_app:s0:c230,c258,c512,c768 tcontext=u:object_r:vendor_camera_mtk_prop:s0 tclass=file permissive=0 app=com.example.aicamera
2025-05-30 13:55:25.741 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Use case androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051 ACTIVE
2025-05-30 13:55:25.743 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648, androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051] for camera: 0
2025-05-30 13:55:25.750 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Use case androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648 ACTIVE
2025-05-30 13:55:25.754 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648, androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051] for camera: 0
2025-05-30 13:55:25.760 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} CameraDevice.onOpened()
2025-05-30 13:55:25.761 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Transitioning camera internal state: OPENING --> OPENED
2025-05-30 13:55:25.763 10376-10426 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@6770ea7[id=1]                         UNKNOWN               
                                                                                                    Camera@36424bc[id=0]                         OPEN                  
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 13:55:25.763 10376-10426 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=OPEN, error=null} from OPEN and null
2025-05-30 13:55:25.764 10376-10426 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=OPEN, error=null}
2025-05-30 13:55:25.766 10376-10426 UseCaseAttachState      com.example.aicamera                 D  All use case: [androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648, androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051] for camera: 0
2025-05-30 13:55:25.777 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648, androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051] for camera: 0
2025-05-30 13:55:25.784 10376-10426 SyncCaptureSessionBase  com.example.aicamera                 D  [androidx.camera.camera2.internal.SynchronizedCaptureSessionBaseImpl@c5bebfe] getSurface...done
2025-05-30 13:55:25.784 10376-10426 CaptureSession          com.example.aicamera                 D  Opening capture session.
2025-05-30 13:55:25.798 10376-10426 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=2 androidx.camera.core.SurfaceRequest$2@45ca23
2025-05-30 13:55:25.798 10376-10426 DeferrableSurface       com.example.aicamera                 D  New surface in use[total_surfaces=3, used_surfaces=2](androidx.camera.core.impl.ImmediateSurface@cc69d95}
2025-05-30 13:55:25.798 10376-10426 DeferrableSurface       com.example.aicamera                 D  use count+1, useCount=1 androidx.camera.core.impl.ImmediateSurface@cc69d95
2025-05-30 13:55:25.922 10376-10426 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) connect: api=4 producerControlledByApp=true
2025-05-30 13:55:25.923 10376-10426 BufferQueueProducer     com.example.aicamera                 I  [ImageReader-3264x2448f100m4-10376-0](id:288800000001,api:4,p:985,c:10376) connect: api=4 producerControlledByApp=false
2025-05-30 13:55:25.930 10376-10426 CaptureSession          com.example.aicamera                 D  Attempting to send capture request onConfigured
2025-05-30 13:55:25.930 10376-10426 CaptureSession          com.example.aicamera                 D  Issuing request for session.
2025-05-30 13:55:25.932 10376-10426 Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 13:55:25.941 10376-10426 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onConfigured() mState=OPENED
2025-05-30 13:55:25.942 10376-10426 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onReady() OPENED
2025-05-30 13:55:26.268 10376-10426 StreamStateObserver     com.example.aicamera                 D  Update Preview stream state to STREAMING
2025-05-30 13:55:29.623 10376-10376 ImageCapture            com.example.aicamera                 D  takePictureInternal
2025-05-30 13:55:29.632 10376-10376 CameraOrientationUtil   com.example.aicamera                 D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-30 13:55:29.635 10376-10376 TakePictureManager      com.example.aicamera                 D  Issue the next TakePictureRequest.
2025-05-30 13:55:29.656 10376-10425 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Issue capture request
2025-05-30 13:55:29.657 10376-10425 CaptureSession          com.example.aicamera                 D  Issuing capture request.
2025-05-30 13:55:29.670 10376-10425 Camera2Cap...estBuilder com.example.aicamera                 D  createCaptureRequest
2025-05-30 13:55:29.774 10376-10390 GraphicBuffer           com.example.aicamera                 D  initWithSize w=24071582, h=1
2025-05-30 13:55:29.775 10376-10390 GraphicBuffer           com.example.aicamera                 D  flatten w=24071582, h=1
2025-05-30 13:55:30.309 10376-10376 TakePictureManager      com.example.aicamera                 D  Issue the next TakePictureRequest.
2025-05-30 13:55:30.309 10376-10376 TakePictureManager      com.example.aicamera                 D  No new request.
2025-05-30 13:55:30.555 10376-10376 CameraManager           com.example.aicamera                 D  Photo capture succeeded: /storage/emulated/0/Android/data/com.example.aicamera/files/2025-05-30-13-55-29-594.jpg
2025-05-30 13:55:30.562 10376-10376 CameraManager           com.example.aicamera                 D  📏 Captured image size: 2448x3264
2025-05-30 13:55:30.562 10376-10376 ImageProcessor          com.example.aicamera                 D  🔄 Resizing image to 416x416: 2025-05-30-13-55-29-594.jpg
2025-05-30 13:55:30.568 10376-10376 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels +
2025-05-30 13:55:30.833 10376-10376 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels -
2025-05-30 13:55:30.834 10376-10376 ImageProcessor          com.example.aicamera                 D  📏 Original size: 2448x3264
2025-05-30 13:55:30.874 10376-10376 ImageProcessor          com.example.aicamera                 D  ✅ Image resized and saved: 416x416
2025-05-30 13:55:30.875 10376-10376 CameraManager           com.example.aicamera                 D  ✅ Image resized to 416x416 (68 KB)
2025-05-30 13:55:30.880 10376-10376 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels +
2025-05-30 13:55:30.888 10376-10376 skia                    com.example.aicamera                 D  SkJpegCodec::onGetPixels -
2025-05-30 13:55:30.890 10376-10376 GalleryManager          com.example.aicamera                 D  📏 Gallery save - Image size: 416x416
2025-05-30 13:55:30.890 10376-10376 GalleryManager          com.example.aicamera                 D  ✅ Image already 416x416, perfect for gallery
2025-05-30 13:55:30.894 10376-10376 GalleryManager          com.example.aicamera                 E  Failed to save image to gallery (Ask Gemini)
                                                                                                    java.lang.IllegalArgumentException: Illegal pattern character 'e'
                                                                                                    	at java.text.SimpleDateFormat.compile(SimpleDateFormat.java:953)
                                                                                                    	at java.text.SimpleDateFormat.initialize(SimpleDateFormat.java:739)
                                                                                                    	at java.text.SimpleDateFormat.<init>(SimpleDateFormat.java:710)
                                                                                                    	at com.example.aicamera.utils.GalleryManager.saveImageToGallery(GalleryManager.kt:103)
                                                                                                    	at com.example.aicamera.utils.GalleryManager.saveToGallery(GalleryManager.kt:62)
                                                                                                    	at com.example.aicamera.CameraManager$capturePhoto$3.onImageSaved(CameraManager.kt:160)
                                                                                                    	at androidx.camera.core.imagecapture.TakePictureRequest.lambda$onResult$1$androidx-camera-core-imagecapture-TakePictureRequest(TakePictureRequest.java:192)
                                                                                                    	at androidx.camera.core.imagecapture.TakePictureRequest$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:938)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:233)
                                                                                                    	at android.os.Looper.loop(Looper.java:334)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8399)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:582)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1068)
2025-05-30 13:55:30.894 10376-10376 CameraManager           com.example.aicamera                 W  ⚠️ Failed to save to gallery: Failed to save to gallery: Illegal pattern character 'e'
2025-05-30 13:55:30.895 10376-10376 CameraScreen            com.example.aicamera                 W  ⚠️ Gallery save failed: Failed to save to gallery: Illegal pattern character 'e'
2025-05-30 13:55:30.905 10376-10376 NetworkManager          com.example.aicamera                 D  🐍 Starting Python gradio_client upload...
2025-05-30 13:55:30.906 10376-10376 NetworkManager          com.example.aicamera                 D  📁 File: 2025-05-30-13-55-29-594.jpg, Size: 69845 bytes
2025-05-30 13:55:30.906 10376-10376 PythonGradioService     com.example.aicamera                 D  🐍 Starting Python environment...
2025-05-30 13:55:31.107 10376-10376 nativeloader            com.example.aicamera                 D  Load /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a/libcrypto_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!classes8.dex): ok
2025-05-30 13:55:31.124 10376-10376 nativeloader            com.example.aicamera                 D  Load /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a/libssl_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!classes8.dex): ok
2025-05-30 13:55:31.134 10376-10376 nativeloader            com.example.aicamera                 D  Load /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a/libsqlite3_chaquopy.so using ns clns-4 from class loader (caller=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!classes8.dex): ok
2025-05-30 13:55:31.138 10376-10376 nativeloader            com.example.aicamera                 D  Load /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a/libcrypto_python.so using ns clns-4 from class loader (caller=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!classes8.dex): ok
2025-05-30 13:55:31.140 10376-10376 nativeloader            com.example.aicamera                 D  Load /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a/libssl_python.so using ns clns-4 from class loader (caller=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!classes8.dex): ok
2025-05-30 13:55:31.143 10376-10376 nativeloader            com.example.aicamera                 D  Load /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a/libsqlite3_python.so using ns clns-4 from class loader (caller=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!classes8.dex): ok
2025-05-30 13:55:31.169 10376-10376 nativeloader            com.example.aicamera                 D  Load /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a/libpython3.11.so using ns clns-4 from class loader (caller=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!classes8.dex): ok
2025-05-30 13:55:31.175 10376-10376 nativeloader            com.example.aicamera                 D  Load /data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!/lib/arm64-v8a/libchaquopy_java.so using ns clns-4 from class loader (caller=/data/app/~~tBgv3hnhXmsK4R03hmYpSg==/com.example.aicamera-bMhCh7Rl8NTZfLY7uVxAGg==/base.apk!classes8.dex): ok
2025-05-30 13:55:32.722 10376-10376 PythonGradioService     com.example.aicamera                 D  ✅ Python environment started
2025-05-30 13:55:32.722 10376-10376 PythonGradioService     com.example.aicamera                 D  🔗 Python instance obtained
2025-05-30 13:55:32.885 10376-10376 xample.aicamera         com.example.aicamera                 W  type=1400 audit(0.0:6497595): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_hashlib.cpython-311.so" dev="dm-10" ino=343462 scontext=u:r:untrusted_app:s0:c230,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c230,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 13:55:33.861 10376-10376 xample.aicamera         com.example.aicamera                 W  type=1400 audit(0.0:6497630): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_heapq.cpython-311.so" dev="dm-10" ino=343483 scontext=u:r:untrusted_app:s0:c230,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c230,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 13:55:33.885 10376-10376 xample.aicamera         com.example.aicamera                 W  type=1400 audit(0.0:6497631): avc: granted { execute } for path="/data/data/com.example.aicamera/files/chaquopy/AssetFinder/stdlib-arm64-v8a/_queue.cpython-311.so" dev="dm-10" ino=343495 scontext=u:r:untrusted_app:s0:c230,c258,c512,c768 tcontext=u:object_r:app_data_file:s0:c230,c258,c512,c768 tclass=file app=com.example.aicamera
2025-05-30 13:55:36.385 10376-10376 python.stderr           com.example.aicamera                 W  /data/data/com.example.aicamera/files/chaquopy/AssetFinder/requirements/websockets/legacy/__init__.py:6: DeprecationWarning: websockets.legacy is deprecated; see https://websockets.readthedocs.io/en/stable/howto/upgrade.html for upgrade instructions
2025-05-30 13:55:36.536 10376-10376 PythonGradioService     com.example.aicamera                 D  📦 chess_fen_client module imported successfully
2025-05-30 13:55:38.287 10376-10376 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 13:55:42.706 10376-10376 PythonGradioService     com.example.aicamera                 D  🔗 Connection test result: {'success': True, 'message': 'Successfully connected to HF Space', 'space': 'yamero999/chess-fen-generation-api'}
2025-05-30 13:55:42.706 10376-10376 PythonGradioService     com.example.aicamera                 D  ✅ Python gradio service initialized successfully
2025-05-30 13:55:42.726 10376-10376 Choreographer           com.example.aicamera                 I  Skipped 730 frames!  The application may be doing too much work on its main thread.
2025-05-30 13:55:42.727 10376-10586 PythonGradioService     com.example.aicamera                 D  🚀 Starting FEN generation using Python gradio_client...
2025-05-30 13:55:42.728 10376-10586 PythonGradioService     com.example.aicamera                 D  📁 File: 2025-05-30-13-55-29-594.jpg, Size: 69845 bytes
2025-05-30 13:55:42.807 10376-10586 PythonGradioService     com.example.aicamera                 D  📦 Converted image to base64: 93128 characters
2025-05-30 13:55:42.808 10376-10586 PythonGradioService     com.example.aicamera                 D  🐍 Calling Python gradio_client...
2025-05-30 13:55:42.848 10376-10390 OpenGLRenderer          com.example.aicamera                 I  Davey! duration=12277ms; Flags=0, FrameTimelineVsyncId=31500484, IntendedVsync=578315214356326, Vsync=578327381023236, InputEventId=0, HandleInputStart=578327385554393, AnimationStart=578327385564932, PerformTraversalsStart=578327430516239, DrawStart=578327431153470, FrameDeadline=578315234356326, FrameInterval=578327384674086, FrameStartTime=16666667, SyncQueued=578327471088624, SyncStart=578327471283086, IssueDrawCommandsStart=578327471631932, SwapBuffers=578327482762162, FrameCompleted=578327492158393, DequeueBufferDuration=46539, QueueBufferDuration=2293770, GpuCompleted=578327492158393, SwapBuffersCompleted=578327488271701, DisplayPresentTime=0, 
2025-05-30 13:55:43.730 10376-10586 python.stdout           com.example.aicamera                 I  Loaded as API: https://yamero999-chess-fen-generation-api.hf.space ✔
2025-05-30 13:55:52.945 10376-10586 PythonGradioService     com.example.aicamera                 D  📄 Python result: {'success': True, 'fen': '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8', 'error': None, 'raw_result': {'success': True, 'fen': '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8', 'analysis': {'pieces_detected': 18, 'processing_time_ms': 1031.34, 'processing_time_seconds': 1.031, 'v6_inference_time_ms': 812.0896816253662, 'board_detection_time_ms': 818.35, 'piece_detection_time_ms': 211.96, 'mapping_time_ms': 0.16, 'save_time_ms': 0.84, 'board_detection': {'corners_found': [[36.0, 83.0], [361.0, 89.0], [342.0, 312.0], [52.0, 314.0]], 'perspective_corrected': True}, 'piece_analysis': {'total_pieces': 18, 'piece_counts': {'black_bishop': 1, 'black_pawn': 5, 'black_rook': 2, 'white_bishop': 1, 'white_pawn': 5, 'white_rook': 2, 'black_king': 1, 'white_king': 1}, 'confidence_stats': {'avg': 0.94, 'min': 0.658, 'max': 0.992}}, 'grid_mapping': {'occupied_squares': 18, 'empty_squares': 46}}, 'provider': 'huggingface_spaces', 'model_info': {'v6_segmentation': 'yamero999/chess-board-segmentation-v6', 'piece_detection': 'yamero999/chess-piece-detection-yolo11n'}}}
2025-05-30 13:55:52.997 10376-10586 PythonGradioService     com.example.aicamera                 D  📊 Parsed result - Success: true, FEN: 1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8, Error: null
2025-05-30 13:55:52.997 10376-10586 PythonGradioService     com.example.aicamera                 D  🎯 FEN Result: 1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8
2025-05-30 13:55:52.998 10376-10586 PythonGradioService     com.example.aicamera                 D  ✅ Successfully processed FEN response
2025-05-30 13:55:53.000 10376-10376 CameraScreen            com.example.aicamera                 D  📝 Original FEN from AI: '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8'
2025-05-30 13:55:53.002 10376-10376 CameraScreen            com.example.aicamera                 D  🔄 Board flipped 180° (vertical + horizontal) - Black king was in wrong position
2025-05-30 13:55:53.002 10376-10376 CameraScreen            com.example.aicamera                 D     Original: '1R6/rPP5/3P1K2/1p2P1Pp/2p1pR1B/3k1p2/4b2r/8'
2025-05-30 13:55:53.002 10376-10376 CameraScreen            com.example.aicamera                 D     Flipped:  '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1'
2025-05-30 13:55:53.003 10376-10376 CameraScreen            com.example.aicamera                 D  🔧 Completed FEN: '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1' (was 1 parts, now 6)
2025-05-30 13:55:53.003 10376-10376 CameraScreen            com.example.aicamera                 D  🚀 Auto-launching external app with processed FEN: '8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1'
2025-05-30 13:55:53.017 10376-10376 ExternalAppManager      com.example.aicamera                 D  Direct launch failed, trying generic Chessis launch
2025-05-30 13:55:53.027 10376-10376 ExternalAppManager      com.example.aicamera                 E  ❌ Failed to launch com.chessis.analysis (Ask Gemini)
                                                                                                    android.content.ActivityNotFoundException: No Activity found to handle Intent { act=android.intent.action.SEND typ=text/plain flg=0x10000001 pkg=com.chessis.analysis clip={text/plain {T(56)}} (has extras) }
                                                                                                    	at android.app.Instrumentation.checkStartActivityResult(Instrumentation.java:2137)
                                                                                                    	at android.app.Instrumentation.execStartActivity(Instrumentation.java:1797)
                                                                                                    	at android.app.Activity.startActivityForResult(Activity.java:5643)
                                                                                                    	at androidx.activity.ComponentActivity.startActivityForResult(ComponentActivity.java:780)
                                                                                                    	at android.app.Activity.startActivityForResult(Activity.java:5591)
                                                                                                    	at androidx.activity.ComponentActivity.startActivityForResult(ComponentActivity.java:761)
                                                                                                    	at android.app.Activity.startActivity(Activity.java:6011)
                                                                                                    	at android.app.Activity.startActivity(Activity.java:5964)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchAppWithFEN(ExternalAppManager.kt:139)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchChessis(ExternalAppManager.kt:78)
                                                                                                    	at com.example.aicamera.integration.ExternalAppManager.launchChessApp(ExternalAppManager.kt:25)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt.CameraScreen$completeAndLaunchFEN(CameraScreen.kt:177)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt.access$CameraScreen$completeAndLaunchFEN(CameraScreen.kt:1)
                                                                                                    	at com.example.aicamera.ui.screens.CameraScreenKt$CameraScreen$6$3$4$2$1$1.invokeSuspend(CameraScreen.kt:478)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher.performTrampolineDispatch(AndroidUiDispatcher.android.kt:81)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher.access$performTrampolineDispatch(AndroidUiDispatcher.android.kt:41)
                                                                                                    	at androidx.compose.ui.platform.AndroidUiDispatcher$dispatchCallback$1.run(AndroidUiDispatcher.android.kt:57)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:938)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:233)
                                                                                                    	at android.os.Looper.loop(Looper.java:334)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8399)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:582)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1068)
2025-05-30 13:55:53.074 10376-10376 ExternalAppManager      com.example.aicamera                 D  ✅ Launched app chooser with clean FEN: 8/r2b4/2p1k3/B1Rp1p2/pP1P2p1/2K1P3/5PPr/6R1 w KQkq - 0 1
2025-05-30 13:55:53.074 10376-10376 CameraScreen            com.example.aicamera                 D  ✅ Successfully launched external app: chessis
2025-05-30 13:55:56.212 10376-10376 SurfaceViewImpl         com.example.aicamera                 D  Surface destroyed.
2025-05-30 13:55:56.213 10376-10376 SurfaceViewImpl         com.example.aicamera                 D  Surface closed androidx.camera.core.SurfaceRequest@5d98752
2025-05-30 13:55:56.214 10376-10376 DeferrableSurface       com.example.aicamera                 D  surface closed,  useCount=2 closed=true androidx.camera.core.SurfaceRequest$2@45ca23
2025-05-30 13:55:56.214 10376-10376 BufferQueueProducer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) disconnect: api -1
2025-05-30 13:55:56.214 10376-10376 BLASTBufferQueue        com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1] destructor()
2025-05-30 13:55:56.214 10376-10376 BufferQueueConsumer     com.example.aicamera                 I  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) disconnect
2025-05-30 13:55:56.225 10376-10395 BufferQueueProducer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:************,api:1,p:10376,c:10376) disconnect: api 1
2025-05-30 13:55:56.240 10376-10422 BLASTBufferQueue        com.example.aicamera                 I  releaseBufferCallbackThunk bufferId:44564580663307 framenumber:570 blastBufferQueue is dead
2025-05-30 13:55:56.250 10376-10376 BLASTBufferQueue        com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0] destructor()
2025-05-30 13:55:56.250 10376-10376 BufferQueueConsumer     com.example.aicamera                 I  [ViewRootImpl[ComposeMainActivity]#0(BLAST Consumer)0](id:************,api:0,p:-1,c:10376) disconnect
2025-05-30 13:55:56.251 10376-10422 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) queueBuffer: BufferQueue has been abandoned
2025-05-30 13:55:56.259 10376-10376 DeferrableSurface       com.example.aicamera                 D  surface closed,  useCount=0 closed=true androidx.camera.core.processing.SurfaceEdge$SettableSurface@250ec87
2025-05-30 13:55:56.261 10376-10376 DeferrableSurface       com.example.aicamera                 D  Surface terminated[total_surfaces=2, used_surfaces=2](androidx.camera.core.processing.SurfaceEdge$SettableSurface@250ec87}
2025-05-30 13:55:56.262 10376-10376 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=1 closed=true androidx.camera.core.SurfaceRequest$2@45ca23
2025-05-30 13:55:56.278 10376-10422 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) dequeueBuffer: BufferQueue has been abandoned
2025-05-30 13:55:56.320 10376-10422 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) queueBuffer: BufferQueue has been abandoned
2025-05-30 13:55:56.392 10376-10422 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) queueBuffer: BufferQueue has been abandoned
2025-05-30 13:55:56.455 10376-10422 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) queueBuffer: BufferQueue has been abandoned
2025-05-30 13:55:56.524 10376-10422 BufferQueueProducer     com.example.aicamera                 E  [SurfaceView[com.example.aicamera/com.example.aicamera.ComposeMainActivity]#1(BLAST Consumer)1](id:288800000002,api:4,p:985,c:10376) queueBuffer: BufferQueue has been abandoned
2025-05-30 13:55:56.527 10376-10425 CaptureSession          com.example.aicamera                 D  CameraCaptureSession.onReady() OPENED
2025-05-30 13:55:57.250 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Use cases [androidx.camera.core.Preview-03af2255-00ee-4aab-941e-21de84268f6682112051, androidx.camera.core.ImageCapture-03097235-8e0b-4a7d-9973-f41e772ba90475441648] now DETACHED for camera
2025-05-30 13:55:57.251 10376-10426 UseCaseAttachState      com.example.aicamera                 D  All use case: [] for camera: 0
2025-05-30 13:55:57.260 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Resetting Capture Session
2025-05-30 13:55:57.275 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Releasing session in state OPENED
2025-05-30 13:55:57.284 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 13:55:57.303 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 13:55:57.364 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Closing camera.
2025-05-30 13:55:57.366 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Transitioning camera internal state: OPENED --> CLOSING
2025-05-30 13:55:57.368 10376-10426 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@6770ea7[id=1]                         UNKNOWN               
                                                                                                    Camera@36424bc[id=0]                         CLOSING               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-30 13:55:57.369 10376-10426 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=CLOSING, error=null} from CLOSING and null
2025-05-30 13:55:57.369 10376-10376 StreamStateObserver     com.example.aicamera                 D  Update Preview stream state to IDLE
2025-05-30 13:55:57.369 10376-10426 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=CLOSING, error=null}
2025-05-30 13:55:57.370 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Resetting Capture Session
2025-05-30 13:55:57.371 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Releasing session in state CLOSING
2025-05-30 13:55:57.372 10376-10426 CaptureSession          com.example.aicamera                 D  onSessionFinished()
2025-05-30 13:55:57.405 10376-10538 BLASTBufferQueue        com.example.aicamera                 I  releaseBufferCallbackThunk bufferId:44564580663301 framenumber:746 blastBufferQueue is dead
2025-05-30 13:55:57.780 10376-10426 BufferQueueProducer     com.example.aicamera                 I  [ImageReader-3264x2448f100m4-10376-0](id:288800000001,api:4,p:985,c:10376) disconnect: api 4
2025-05-30 13:55:57.789 10376-10426 UseCaseAttachState      com.example.aicamera                 D  Active and attached use case: [] for camera: 0
2025-05-30 13:55:57.792 10376-10426 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=0 closed=true androidx.camera.core.SurfaceRequest$2@45ca23
2025-05-30 13:55:57.793 10376-10426 DeferrableSurface       com.example.aicamera                 D  Surface no longer in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@45ca23}
2025-05-30 13:55:57.793 10376-10426 DeferrableSurface       com.example.aicamera                 D  Surface terminated[total_surfaces=1, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@45ca23}
2025-05-30 13:55:57.794 10376-10426 DeferrableSurface       com.example.aicamera                 D  use count-1,  useCount=0 closed=false androidx.camera.core.impl.ImmediateSurface@cc69d95
2025-05-30 13:55:57.795 10376-10426 DeferrableSurface       com.example.aicamera                 D  Surface no longer in use[total_surfaces=1, used_surfaces=0](androidx.camera.core.impl.ImmediateSurface@cc69d95}
2025-05-30 13:55:57.796 10376-10376 SurfaceViewImpl         com.example.aicamera                 D  Safe to release surface.
2025-05-30 13:55:57.796 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} CameraDevice.onClosed()
2025-05-30 13:55:57.797 10376-10426 Camera2CameraImpl       com.example.aicamera                 D  {Camera@36424bc[id=0]} Transitioning camera internal state: CLOSING --> INITIALIZED
2025-05-30 13:55:57.799 10376-10426 CameraStateRegistry     com.example.aicamera                 D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@6770ea7[id=1]                         UNKNOWN               
                                                                                                    Camera@36424bc[id=0]                         CLOSED                
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 0 (Max allowed: 1)
2025-05-30 13:55:57.800 10376-10426 CameraStateMachine      com.example.aicamera                 D  New public camera state CameraState{type=CLOSED, error=null} from CLOSED and null
2025-05-30 13:55:57.800 10376-10426 CameraStateMachine      com.example.aicamera                 D  Publishing new public camera state CameraState{type=CLOSED, error=null}
---------------------------- PROCESS ENDED (10376) for package com.example.aicamera ----------------------------
