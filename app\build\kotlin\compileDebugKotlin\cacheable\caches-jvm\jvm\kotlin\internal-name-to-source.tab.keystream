com/example/aicamera/AiResponsecom/example/aicamera/ApiConfig*com/example/aicamera/ApiConfig$ApiProvider*com/example/aicamera/ApiConfig$LocalServer+com/example/aicamera/ApiConfig$PythonBridge*com/example/aicamera/ApiConfig$HuggingFace&com/example/aicamera/ApiConfig$Network+com/example/aicamera/ApiConfig$WhenMappings"com/example/aicamera/CameraManager1com/example/aicamera/CameraManager$capturePhoto$1,com/example/aicamera/CameraManager$Companion%com/example/aicamera/ChessApplication/com/example/aicamera/ChessApplication$Companion(com/example/aicamera/ComposeMainActivity?com/example/aicamera/ComposableSingletons$ComposeMainActivityKtJcom/example/aicamera/ComposableSingletons$ComposeMainActivityKt$lambda-1$1Jcom/example/aicamera/ComposableSingletons$ComposeMainActivityKt$lambda-2$1Jcom/example/aicamera/ComposableSingletons$ComposeMainActivityKt$lambda-3$1Jcom/example/aicamera/ComposableSingletons$ComposeMainActivityKt$lambda-4$1Jcom/example/aicamera/ComposableSingletons$ComposeMainActivityKt$lambda-5$1Jcom/example/aicamera/ComposableSingletons$ComposeMainActivityKt$lambda-6$1Jcom/example/aicamera/ComposableSingletons$ComposeMainActivityKt$lambda-7$1*com/example/aicamera/ComposeMainActivityKt5com/example/aicamera/ComposeMainActivityKt$ChessApp$17com/example/aicamera/ComposeMainActivityKt$ChessApp$1$19com/example/aicamera/ComposeMainActivityKt$ChessApp$1$1$19com/example/aicamera/ComposeMainActivityKt$ChessApp$1$1$29com/example/aicamera/ComposeMainActivityKt$ChessApp$1$1$37com/example/aicamera/ComposeMainActivityKt$ChessApp$1$29com/example/aicamera/ComposeMainActivityKt$ChessApp$1$2$19com/example/aicamera/ComposeMainActivityKt$ChessApp$1$2$27com/example/aicamera/ComposeMainActivityKt$ChessApp$1$39com/example/aicamera/ComposeMainActivityKt$ChessApp$1$3$17com/example/aicamera/ComposeMainActivityKt$ChessApp$1$49com/example/aicamera/ComposeMainActivityKt$ChessApp$1$4$15com/example/aicamera/ComposeMainActivityKt$ChessApp$2=com/example/aicamera/ComposeMainActivityKt$AnalysisScreen$1$1;com/example/aicamera/ComposeMainActivityKt$AnalysisScreen$2=com/example/aicamera/ComposeMainActivityKt$SettingsScreen$1$1;com/example/aicamera/ComposeMainActivityKt$SettingsScreen$2!com/example/aicamera/MainActivity/com/example/aicamera/MainActivity$startCamera$1/com/example/aicamera/MainActivity$startCamera$20com/example/aicamera/MainActivity$capturePhoto$10com/example/aicamera/MainActivity$capturePhoto$21com/example/aicamera/MainActivity$sendImageToAI$1+com/example/aicamera/MainActivity$Companion.com/example/aicamera/MainActivity$WhenMappings#com/example/aicamera/NetworkManager:com/example/aicamera/NetworkManager$uploadToPythonGradio$1-com/example/aicamera/NetworkManager$Companion0com/example/aicamera/NetworkManager$WhenMappings"com/example/aicamera/NetworkResult*com/example/aicamera/NetworkResult$Success(com/example/aicamera/NetworkResult$Error*com/example/aicamera/NetworkResult$Loading(com/example/aicamera/PythonGradioService6com/example/aicamera/PythonGradioService$generateFen$29com/example/aicamera/PythonGradioService$testConnection$22com/example/aicamera/PythonGradioService$Companion%com/example/aicamera/SettingsActivity/com/example/aicamera/SettingsActivity$setupUI$1/com/example/aicamera/SettingsActivity$Companion0com/example/aicamera/chess/ChessAnalysisActivity;com/example/aicamera/chess/ChessAnalysisActivity$onCreate$1=com/example/aicamera/chess/ChessAnalysisActivity$onCreate$1$1Acom/example/aicamera/chess/ChessAnalysisActivity$onCreate$1$1$1$1:com/example/aicamera/chess/ChessAnalysisActivity$CompanionGcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKtRcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-1$1Rcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-2$1Rcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-3$1Rcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-4$1Rcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-5$1Rcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-6$1Rcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-7$1Rcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-8$1Rcom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-9$1Scom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-10$1Scom/example/aicamera/chess/ComposableSingletons$ChessAnalysisActivityKt$lambda-11$12com/example/aicamera/chess/ChessAnalysisActivityKtHcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$1Jcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$1Jcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$2Lcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$2$1Ncom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$2$1$1Lcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$2$2Ncom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$2$3$1Ncom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$2$4$1Lcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$2$5Jcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3Lcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3$1Ncom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3$1$1Pcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3$1$1$1Ncom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3$1$2Lcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3$2Lcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3$3Ncom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3$3$1Lcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$2$3$4Jcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$3$1Jcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$4$1Jcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$5$1Hcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$6Hcom/example/aicamera/chess/ChessAnalysisActivityKt$ChessAnalysisScreen$7Dcom/example/aicamera/chess/ChessAnalysisActivityKt$ScanBoardDialog$1Fcom/example/aicamera/chess/ChessAnalysisActivityKt$ScanBoardDialog$1$1Dcom/example/aicamera/chess/ChessAnalysisActivityKt$ScanBoardDialog$2Gcom/example/aicamera/chess/ChessAnalysisActivityKt$EditPositionDialog$1Icom/example/aicamera/chess/ChessAnalysisActivityKt$EditPositionDialog$1$1Ocom/example/aicamera/chess/ChessAnalysisActivityKt$EditPositionDialog$1$1$1$2$1Qcom/example/aicamera/chess/ChessAnalysisActivityKt$EditPositionDialog$1$1$1$3$1$1Qcom/example/aicamera/chess/ChessAnalysisActivityKt$EditPositionDialog$1$1$1$3$2$1Qcom/example/aicamera/chess/ChessAnalysisActivityKt$EditPositionDialog$1$1$1$4$1$1Gcom/example/aicamera/chess/ChessAnalysisActivityKt$EditPositionDialog$2.com/example/aicamera/chess/ChessBoardInterface*com/example/aicamera/chess/ChessBoardUtils7com/example/aicamera/chess/ChessBoardUtils$WhenMappings.com/example/aicamera/chess/ChessBoardWebViewKtDcom/example/aicamera/chess/ChessBoardWebViewKt$ChessBoardWebView$1$1Bcom/example/aicamera/chess/ChessBoardWebViewKt$ChessBoardWebView$2Bcom/example/aicamera/chess/ChessBoardWebViewKt$ChessBoardWebView$3Fcom/example/aicamera/chess/ChessBoardWebViewKt$ChessBoardWebView$3$1$1Bcom/example/aicamera/chess/ChessBoardWebViewKt$ChessBoardWebView$4Bcom/example/aicamera/chess/ChessBoardWebViewKt$ChessBoardWebView$5Bcom/example/aicamera/chess/ChessBoardWebViewKt$ChessBoardWebView$6Bcom/example/aicamera/chess/ChessBoardWebViewKt$ChessBoardWebView$7Ecom/example/aicamera/chess/ChessBoardWebViewKt$createChessWebView$1$1)com/example/aicamera/chess/ChessGameState#com/example/aicamera/chess/GameMode)com/example/aicamera/chess/EngineAnalysis'com/example/aicamera/chess/AnalysisLine%com/example/aicamera/chess/ChessPiece/com/example/aicamera/chess/ChessPiece$Companion<com/example/aicamera/chess/ChessPiece$Companion$WhenMappings$com/example/aicamera/chess/PieceType%com/example/aicamera/chess/PieceColor+com/example/aicamera/chess/BoardOrientation%com/example/aicamera/chess/GameResult)com/example/aicamera/chess/GameResultType%com/example/aicamera/chess/PlayOption%com/example/aicamera/chess/RecentGame)com/example/aicamera/chess/LearningModule%com/example/aicamera/chess/ScanResult'com/example/aicamera/chess/PositionInfoCcom/example/aicamera/chess/ComposableSingletons$ChessUIComponentsKtNcom/example/aicamera/chess/ComposableSingletons$ChessUIComponentsKt$lambda-1$1Ncom/example/aicamera/chess/ComposableSingletons$ChessUIComponentsKt$lambda-2$1Ncom/example/aicamera/chess/ComposableSingletons$ChessUIComponentsKt$lambda-3$1Ncom/example/aicamera/chess/ComposableSingletons$ChessUIComponentsKt$lambda-4$1Ncom/example/aicamera/chess/ComposableSingletons$ChessUIComponentsKt$lambda-5$1.com/example/aicamera/chess/ChessUIComponentsKt?com/example/aicamera/chess/ChessUIComponentsKt$ChessBoardCard$1Gcom/example/aicamera/chess/ChessUIComponentsKt$ChessBoardCard$1$1$1$2$1Gcom/example/aicamera/chess/ChessUIComponentsKt$ChessBoardCard$1$1$1$2$2Ccom/example/aicamera/chess/ChessUIComponentsKt$ChessBoardCard$1$1$2Ecom/example/aicamera/chess/ChessUIComponentsKt$ChessBoardCard$1$1$2$1?com/example/aicamera/chess/ChessUIComponentsKt$ChessBoardCard$2Ccom/example/aicamera/chess/ChessUIComponentsKt$EngineAnalysisCard$1Gcom/example/aicamera/chess/ChessUIComponentsKt$EngineAnalysisCard$1$1$3Kcom/example/aicamera/chess/ChessUIComponentsKt$EngineAnalysisCard$1$1$3$1$1gcom/example/aicamera/chess/ChessUIComponentsKt$EngineAnalysisCard$1$1$3$invoke$$inlined$items$default$1gcom/example/aicamera/chess/ChessUIComponentsKt$EngineAnalysisCard$1$1$3$invoke$$inlined$items$default$2gcom/example/aicamera/chess/ChessUIComponentsKt$EngineAnalysisCard$1$1$3$invoke$$inlined$items$default$3gcom/example/aicamera/chess/ChessUIComponentsKt$EngineAnalysisCard$1$1$3$invoke$$inlined$items$default$4Ccom/example/aicamera/chess/ChessUIComponentsKt$EngineAnalysisCard$2>com/example/aicamera/chess/ChessUIComponentsKt$EvaluationBar$2@com/example/aicamera/chess/ChessUIComponentsKt$MoveHistoryCard$1Dcom/example/aicamera/chess/ChessUIComponentsKt$MoveHistoryCard$1$1$1Fcom/example/aicamera/chess/ChessUIComponentsKt$MoveHistoryCard$1$1$1$1Hcom/example/aicamera/chess/ChessUIComponentsKt$MoveHistoryCard$1$1$1$1$1@com/example/aicamera/chess/ChessUIComponentsKt$MoveHistoryCard$2Acom/example/aicamera/chess/ChessUIComponentsKt$PositionInfoCard$1Acom/example/aicamera/chess/ChessUIComponentsKt$PositionInfoCard$2)com/example/aicamera/chess/ChessViewModel4com/example/aicamera/chess/ChessViewModel$makeMove$1Bcom/example/aicamera/chess/ChessViewModel$analyzeCurrentPosition$13com/example/aicamera/chess/ChessViewModel$Companion+com/example/aicamera/chess/StockfishManager8com/example/aicamera/chess/StockfishManager$initialize$2=com/example/aicamera/chess/StockfishManager$analyzePosition$29com/example/aicamera/chess/StockfishManager$getBestMove$2>com/example/aicamera/chess/StockfishManager$evaluatePosition$25com/example/aicamera/chess/StockfishManager$Companion/com/example/aicamera/chess/ui/theme/ChessColors0com/example/aicamera/chess/ui/theme/ChessThemeKt=com/example/aicamera/chess/ui/theme/ChessThemeKt$ChessTheme$1Ccom/example/aicamera/ui/screens/ComposableSingletons$CameraScreenKtNcom/example/aicamera/ui/screens/ComposableSingletons$CameraScreenKt$lambda-1$1Ncom/example/aicamera/ui/screens/ComposableSingletons$CameraScreenKt$lambda-2$1Ncom/example/aicamera/ui/screens/ComposableSingletons$CameraScreenKt$lambda-3$1Ncom/example/aicamera/ui/screens/ComposableSingletons$CameraScreenKt$lambda-4$1Ncom/example/aicamera/ui/screens/ComposableSingletons$CameraScreenKt$lambda-5$1Ncom/example/aicamera/ui/screens/ComposableSingletons$CameraScreenKt$lambda-6$1.com/example/aicamera/ui/screens/CameraScreenKt=com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$1Rcom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$permissionLauncher$1$1=com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$2=com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$3?com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$3$1?com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$3$2?com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$4$1Ccom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$4$1$1$1=com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$5?com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$1?com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$2Ccom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$2$1$1Acom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$2$2Acom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$1Ecom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$1$1$1Acom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$2Acom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$3Ccom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$4$1Ccom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$4$2Ecom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$4$2$1Gcom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$4$2$1$1Ecom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$4$2$2Ccom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$3$4$3Acom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$4$1?com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$5Ccom/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$5$1$1?com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$6$6=com/example/aicamera/ui/screens/CameraScreenKt$CameraScreen$7Acom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKtLcom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKt$lambda-1$1Lcom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKt$lambda-2$1Lcom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKt$lambda-3$1Lcom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKt$lambda-4$1Lcom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKt$lambda-5$1Lcom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKt$lambda-6$1Lcom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKt$lambda-7$1Lcom/example/aicamera/ui/screens/ComposableSingletons$HomeScreenKt$lambda-8$1,com/example/aicamera/ui/screens/HomeScreenKt9com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$1;com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$1$1=com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$1$1$19com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2=com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1?com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1$1Acom/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1$1$1?com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1$2?com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1$3Ccom/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1$3$1$1Ccom/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1$3$1$2?com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1$4?com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$2$1$1$59com/example/aicamera/ui/screens/HomeScreenKt$HomeScreen$3:com/example/aicamera/ui/screens/HomeScreenKt$FeatureItem$1:com/example/aicamera/ui/screens/HomeScreenKt$FeatureItem$2%com/example/aicamera/ui/theme/ColorKt%com/example/aicamera/ui/theme/ThemeKt2com/example/aicamera/ui/theme/ThemeKt$ChessTheme$12com/example/aicamera/ui/theme/ThemeKt$ChessTheme$2$com/example/aicamera/ui/theme/TypeKt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      